/**
 * Demo script showing the AI Response Interceptor functionality
 */

import { AIResponseInterceptor } from '../src/interceptor.js';
import { 
  KubernetesResourceMiddleware, 
  KubernetesErrorMiddleware, 
  KubernetesMetricsMiddleware 
} from '../src/middleware/kubernetes-middleware.js';

async function demonstrateInterceptor() {
  console.log('🚀 Starting AI Response Interceptor Demo\n');

  // Create interceptor instance
  const interceptor = new AIResponseInterceptor();

  // Add Kubernetes-specific middleware
  interceptor.addMiddleware(new KubernetesResourceMiddleware());
  interceptor.addMiddleware(new KubernetesErrorMiddleware());
  interceptor.addMiddleware(new KubernetesMetricsMiddleware());

  console.log('📊 Initial Stats:', interceptor.getStats());
  console.log('');

  // Demo 1: Basic namespace detection from kubectl command
  console.log('🔍 Demo 1: Namespace Detection from kubectl Command');
  const response1 = await interceptor.interceptResponse(
    'kubectl logs -n production web-server-12345-abcde shows CrashLoopBackOff error',
    {
      conversationId: 'demo-conv-1',
      metadata: {
        requestType: 'pod_log_analysis',
      },
    }
  );

  console.log('Response 1:');
  console.log('- ID:', response1.id);
  console.log('- Namespace:', response1.namespace.namespace);
  console.log('- Source:', response1.namespace.source);
  console.log('- Confidence:', response1.namespace.confidence);
  console.log('- Pod Names:', response1.podNames);
  console.log('- Kubernetes Resources:', response1.metadata?.['kubernetesResources']);
  console.log('- Kubernetes Errors:', response1.metadata?.['kubernetesErrors']);
  console.log('');

  // Demo 2: Log path namespace detection with metrics
  console.log('🔍 Demo 2: Log Path Detection with Metrics');
  const response2 = await interceptor.interceptResponse(
    'Error in /var/log/pods/staging_api-gateway-67890_uuid/container.log: OOMKilled due to memory usage at 512Mi (85% of limit)',
    {
      conversationId: 'demo-conv-2',
      metadata: {
        requestType: 'resource_analysis',
      },
    }
  );

  console.log('Response 2:');
  console.log('- ID:', response2.id);
  console.log('- Namespace:', response2.namespace.namespace);
  console.log('- Source:', response2.namespace.source);
  console.log('- Pod Names:', response2.podNames);
  console.log('- Kubernetes Errors:', response2.metadata?.['kubernetesErrors']);
  console.log('- Kubernetes Metrics:', response2.metadata?.['kubernetesMetrics']);
  console.log('');

  // Demo 3: Streaming response
  console.log('🔍 Demo 3: Streaming Response Processing');
  const streamId = 'demo-stream-1';
  
  await interceptor.interceptStreamChunk(
    'Analyzing pod logs for namespace kube-system...',
    streamId,
    false,
    { conversationId: 'demo-conv-3' }
  );

  await interceptor.interceptStreamChunk(
    'Found Deployment coredns with ImagePullBackOff status.',
    streamId,
    false,
    { conversationId: 'demo-conv-3' }
  );

  const finalChunk = await interceptor.interceptStreamChunk(
    'Analysis complete. CPU usage: 150 mcores, Memory: 256Mi.',
    streamId,
    true, // Mark as complete
    { conversationId: 'demo-conv-3' }
  );

  console.log('Final Chunk:');
  console.log('- ID:', finalChunk.id);
  console.log('- Total Chunks:', finalChunk.totalChunks);
  console.log('- Is Complete:', finalChunk.isComplete);
  console.log('');

  // Demo 4: Conversation state tracking
  console.log('🔍 Demo 4: Conversation State Tracking');
  const conv1State = interceptor.getConversationState('demo-conv-1');
  const conv2State = interceptor.getConversationState('demo-conv-2');

  console.log('Conversation 1 State:');
  console.log('- ID:', conv1State?.id);
  console.log('- Namespace:', conv1State?.namespace);
  console.log('- Pod Names:', conv1State?.podNames);
  console.log('- Message Count:', conv1State?.messageCount);
  console.log('');

  console.log('Conversation 2 State:');
  console.log('- ID:', conv2State?.id);
  console.log('- Namespace:', conv2State?.namespace);
  console.log('- Pod Names:', conv2State?.podNames);
  console.log('- Message Count:', conv2State?.messageCount);
  console.log('');

  // Final stats
  console.log('📊 Final Stats:', interceptor.getStats());
  console.log('');

  console.log('✅ Demo completed successfully!');
  console.log('');
  console.log('🎯 Key Features Demonstrated:');
  console.log('- ✅ Intelligent namespace detection from multiple sources');
  console.log('- ✅ Kubernetes resource and error pattern recognition');
  console.log('- ✅ Performance metrics extraction');
  console.log('- ✅ Streaming response support');
  console.log('- ✅ Conversation state management');
  console.log('- ✅ Middleware pipeline processing');
  console.log('- ✅ Pod name extraction and tracking');
}

// Run the demo
demonstrateInterceptor().catch(console.error);
