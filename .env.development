# Development Environment Configuration
NODE_ENV=development

# AWS Configuration (use development/test credentials)
AWS_REGION=us-east-1
S3_BUCKET_NAME=dev-s3-mcp-bucket

# MCP Server Configuration
MCP_SERVER_PORT=3000
MCP_SERVER_NAME=dev-s3-mcp-server

# Kubernetes Configuration
DEFAULT_NAMESPACE_PREFIX=DEV_NAMESPACE
NAMESPACE_VALIDATION_ENABLED=false
ALLOWED_NAMESPACES=development,dev,test,default

# Storage Configuration
FILE_RETENTION_DAYS=7
MAX_FILE_SIZE_MB=50
ENABLE_COMPRESSION=false
S3_BASE_PATH=dev-mcp-responses
ENABLE_DATE_FOLDERS=true
ENABLE_NAMESPACE_FOLDERS=true

# Logging Configuration
LOG_LEVEL=debug
LOG_FILE_PATH=./logs/dev-mcp-server.log
ENABLE_CONSOLE_LOGGING=true

# Development-specific Configuration
DEV_ENABLE_DEBUG_LOGS=true
DEV_MOCK_S3_UPLOADS=false
DEV_SIMULATE_ERRORS=false

# Webhook Configuration
WEBHOOK_ENABLED=true
WEBHOOK_PORT=3001
WEBHOOK_CORS_ENABLED=true
WEBHOOK_MAX_PAYLOAD_SIZE=50mb

# Performance Configuration (relaxed for development)
MAX_CONCURRENT_UPLOADS=10
UPLOAD_TIMEOUT_MS=60000
RETRY_ATTEMPTS=2
RETRY_DELAY_MS=500

# Monitoring Configuration
ENABLE_METRICS=true
METRICS_PORT=9090
HEALTH_CHECK_INTERVAL_MS=10000
