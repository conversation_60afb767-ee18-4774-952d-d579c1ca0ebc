# Configuration Management

The S3 MCP Server features a comprehensive configuration management system with environment-specific settings, validation, secrets management, and CLI tools.

## Quick Start

1. **Copy the example environment file:**
   ```bash
   cp .env.example .env
   ```

2. **Set your AWS credentials:**
   ```bash
   export AWS_ACCESS_KEY_ID="your-access-key"
   export AWS_SECRET_ACCESS_KEY="your-secret-key"
   export AWS_REGION="us-east-1"
   export S3_BUCKET_NAME="your-bucket-name"
   ```

3. **Validate your configuration:**
   ```bash
   npm run config:validate
   ```

## Environment-Specific Configuration

The system supports multiple environments with automatic configuration overrides:

### Environments

- **`development`** - Enhanced logging, relaxed validation, short retention
- **`staging`** - Balanced settings, validation enabled, compression on
- **`production`** - Minimal logging, strict validation, extended retention
- **`test`** - Minimal settings for testing

### Environment Selection

Set the environment using the `NODE_ENV` variable:

```bash
export NODE_ENV=production
```

Or use environment-specific files:
- `.env.development`
- `.env.staging` 
- `.env.production`

## Configuration CLI Tools

### Available Commands

```bash
# Validate configuration
npm run config:validate

# Show current configuration (masked)
npm run config:show

# Show environment information
npm run config:env

# Test configuration loading
npm run config:test

# Test secret loading
npm run config secrets
```

### Example Output

```bash
$ npm run config:env
🌍 Environment information:

Current environment: development
NODE_ENV: development

Required variables for development:
  ✅ AWS_ACCESS_KEY_ID (set)
  ✅ AWS_SECRET_ACCESS_KEY (set)
  ✅ AWS_REGION (set)
  ✅ S3_BUCKET_NAME (set)

Environment warnings:
  ⚠️  Development mode: Enhanced logging and debugging enabled
  ⚠️  Development mode: Reduced file retention period
```

## Configuration Schema

### AWS Configuration (Required)

```bash
AWS_ACCESS_KEY_ID=AKIAIOSFODNN7EXAMPLE
AWS_SECRET_ACCESS_KEY=wJalrXUtnFEMI/K7MDENG/bPxRfiCYEXAMPLEKEY
AWS_REGION=us-east-1
S3_BUCKET_NAME=my-mcp-bucket
```

### MCP Server Configuration

```bash
MCP_SERVER_PORT=3000
MCP_SERVER_NAME=s3-mcp-server
```

### Kubernetes Configuration

```bash
DEFAULT_NAMESPACE_PREFIX=NO_NAMESPACE
NAMESPACE_VALIDATION_ENABLED=false
ALLOWED_NAMESPACES=production,staging,development,kube-system
```

### Storage Configuration

```bash
FILE_RETENTION_DAYS=30
MAX_FILE_SIZE_MB=10
ENABLE_COMPRESSION=false
S3_BASE_PATH=mcp-responses
ENABLE_DATE_FOLDERS=true
ENABLE_NAMESPACE_FOLDERS=true
```

### Logging Configuration

```bash
LOG_LEVEL=info
LOG_FILE_PATH=./logs/mcp-server.log
ENABLE_CONSOLE_LOGGING=true
```

## Validation Rules

The configuration system includes comprehensive validation:

### AWS Credentials
- **Access Key ID**: 16-128 characters, alphanumeric uppercase
- **Secret Access Key**: 40-128 characters
- **Region**: Valid AWS region format
- **Bucket Name**: Valid S3 bucket naming rules

### Kubernetes Namespaces
- **Format**: Lowercase alphanumeric with hyphens
- **Length**: 1-63 characters
- **Validation**: Optional strict validation against allowed list

### File Sizes and Retention
- **Retention**: 1-3650 days (10 years max)
- **File Size**: 1-5120 MB (5GB max)
- **Paths**: Valid filesystem paths

## Secrets Management

### Secret Sources

The system supports multiple secret sources:

1. **Environment Variables** (default)
2. **File System** (for staging)
3. **Kubernetes Secrets** (for production)
4. **AWS Secrets Manager** (planned)

### Environment-Specific Sources

```typescript
// Development: Environment variables
AWS_ACCESS_KEY_ID=your-key

// Staging: File-based secrets
/etc/secrets/aws-access-key-id

// Production: Kubernetes secrets
/var/run/secrets/aws-credentials/access-key-id
```

### Security Features

- **Automatic Masking**: Sensitive values are masked in logs
- **Validation Warnings**: Alerts for insecure configurations
- **Production Checks**: Extra validation in production mode

## Environment-Specific Overrides

### Development Environment

```yaml
logging:
  level: debug
  enableConsoleLogging: true
storage:
  retentionDays: 7
  maxFileSizeMB: 50
  enableCompression: false
kubernetes:
  validationEnabled: false
```

### Production Environment

```yaml
logging:
  level: warn
  enableConsoleLogging: false
storage:
  retentionDays: 90
  maxFileSizeMB: 500
  enableCompression: true
kubernetes:
  validationEnabled: true
```

## Troubleshooting

### Common Issues

1. **Missing Environment Variables**
   ```bash
   npm run config:validate
   # Shows exactly which variables are missing
   ```

2. **Invalid AWS Credentials**
   ```bash
   npm run config:validate
   # Validates credential format and patterns
   ```

3. **Configuration Conflicts**
   ```bash
   npm run config:show
   # Shows final merged configuration
   ```

### Debug Mode

Enable debug logging to see configuration loading details:

```bash
export LOG_LEVEL=debug
npm run config:test
```

## Advanced Features

### Custom Validation Rules

The system includes custom validators for:
- S3 bucket naming compliance
- AWS region validation
- Kubernetes namespace format
- File path validation

### Configuration Caching

- Configurations are cached for 5 minutes
- Cache can be cleared with `clearConfigCache()`
- Automatic cache invalidation on environment changes

### Namespace Extraction Patterns

Configurable patterns for extracting Kubernetes namespaces:
- kubectl command patterns
- Log path patterns
- Content analysis patterns
- Pod name patterns

## API Reference

### Main Functions

```typescript
// Get configuration (synchronous)
const config = getConfig();

// Get configuration (async with full validation)
const config = await getConfigAsync();

// Get masked configuration for logging
const masked = getMaskedConfig();

// Clear configuration cache
clearConfigCache();
```

### Environment Functions

```typescript
// Get current environment
const env = getCurrentEnvironment();

// Check environment type
if (isProduction()) { /* ... */ }
if (isDevelopment()) { /* ... */ }

// Validate environment requirements
const validation = validateEnvironmentRequirements();
```
