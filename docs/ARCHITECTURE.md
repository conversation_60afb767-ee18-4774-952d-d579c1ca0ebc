# Architecture Documentation

This document describes the architecture, design patterns, and technical implementation of the S3 MCP Server.

## 🏗️ System Overview

The S3 MCP Server is a production-ready Node.js application that implements the Model Context Protocol (MCP) to capture AI agent responses and store them in AWS S3 with intelligent organization.

```mermaid
graph TB
    A[AI Agent] -->|MCP Protocol| B[S3 MCP Server]
    B -->|Process & Analyze| C[Response Handler]
    C -->|Extract Metadata| D[Kubernetes Middleware]
    C -->|Store| E[S3 Client]
    E -->|Upload| F[AWS S3]
    B -->|Monitor| G[Health Monitor]
    B -->|Log| H[Enhanced Logger]
    B -->|Report Errors| I[Error Reporter]
```

## 🧩 Core Components

### 1. MCP Server (`src/server.ts`)
- **Purpose**: Main entry point implementing MCP protocol
- **Responsibilities**:
  - Handle MCP tool calls
  - Manage client connections
  - Coordinate request processing
  - Provide health endpoints

### 2. AI Response Interceptor (`src/interceptor.ts`)
- **Purpose**: Process and analyze AI responses
- **Responsibilities**:
  - Namespace detection and extraction
  - Pod name identification
  - Metadata enrichment
  - Middleware orchestration

### 3. S3 Client (`src/s3-client.ts`)
- **Purpose**: Handle AWS S3 operations
- **Responsibilities**:
  - File uploads with retry logic
  - Circuit breaker implementation
  - Performance monitoring
  - Batch operations

### 4. Response Handler (`src/response-handler.ts`)
- **Purpose**: Core response processing logic
- **Responsibilities**:
  - Content validation
  - Namespace validation
  - Metadata generation
  - File naming conventions

## 🔧 Advanced Features

### Error Handling System (`src/error-handling/`)

#### Error Types (`error-types.ts`)
```typescript
export class MCPError extends Error {
  public readonly code: ErrorCode;
  public readonly severity: ErrorSeverity;
  public readonly context: ErrorContext;
  public readonly isRetryable: boolean;
}
```

#### Retry Manager (`retry.ts`)
- **Exponential Backoff**: Intelligent retry with jitter
- **Circuit Breaker**: Prevents cascading failures
- **Configurable Policies**: Customizable retry conditions

#### Health Monitor (`health-monitor.ts`)
- **System Health Checks**: Memory, S3 connectivity
- **Real-time Monitoring**: Continuous health assessment
- **Alert Integration**: Configurable alerting rules

#### Error Reporter (`error-reporter.ts`)
- **Error Deduplication**: Groups similar errors
- **Alert Rules**: Configurable alerting conditions
- **Statistics Tracking**: Comprehensive error analytics

### Configuration System (`src/config/`)

#### Schema Validation (`schema.ts`)
```typescript
export const configSchema = z.object({
  aws: z.object({
    accessKeyId: z.string().min(16).max(128),
    secretAccessKey: z.string().min(40).max(128),
    region: z.string().regex(/^[a-z0-9-]+$/),
    bucketName: z.string().min(3).max(63)
  }),
  // ... more schema definitions
});
```

#### Environment Management (`environments.ts`)
- **Multi-Environment Support**: Development, staging, production
- **Environment-Specific Overrides**: Tailored configurations
- **Validation Rules**: Environment-specific validation

#### Secrets Management (`secrets.ts`)
- **Multiple Sources**: Environment variables, files, Kubernetes secrets
- **Automatic Masking**: Sensitive data protection
- **Security Validation**: Production security checks

## 🔄 Data Flow

### Request Processing Flow

1. **MCP Request Reception**
   ```
   AI Agent → MCP Protocol → Server.handleToolCall()
   ```

2. **Content Processing**
   ```
   Raw Content → Interceptor → Middleware Chain → Processed Response
   ```

3. **Namespace Detection**
   ```
   Content Analysis → Pattern Matching → Namespace Extraction → Validation
   ```

4. **S3 Storage**
   ```
   Processed Response → S3 Client → Retry Logic → AWS S3
   ```

5. **Response Generation**
   ```
   Upload Result → Response Formatting → MCP Response → AI Agent
   ```

### Middleware Pipeline

```typescript
interface Middleware {
  name: string;
  priority: number;
  process(response: AIResponse): Promise<AIResponse>;
}
```

**Execution Order:**
1. **KubernetesResourceMiddleware** (Priority: 100)
   - Detects Kubernetes resources
   - Extracts pod names and services

2. **KubernetesErrorMiddleware** (Priority: 200)
   - Identifies error patterns
   - Categorizes error types

3. **KubernetesMetricsMiddleware** (Priority: 300)
   - Extracts performance metrics
   - Parses resource usage data

## 📊 Performance Architecture

### Retry and Circuit Breaker

```typescript
class CircuitBreaker {
  private state: 'CLOSED' | 'OPEN' | 'HALF_OPEN' = 'CLOSED';
  private failures = 0;
  private lastFailureTime = 0;

  async execute<T>(operation: () => Promise<T>): Promise<T> {
    // Circuit breaker logic
  }
}
```

### Performance Monitoring

```typescript
class PerformanceTracker {
  private metrics: Map<string, PerformanceMetric> = new Map();
  
  track(operation: string, duration: number): void {
    // Track performance metrics
  }
}
```

## 🔐 Security Architecture

### Input Validation
- **Schema Validation**: Zod-based validation
- **Content Sanitization**: XSS and injection prevention
- **Size Limits**: Configurable content size limits

### AWS Security
- **IAM Roles**: Preferred over access keys
- **Bucket Policies**: Least privilege access
- **Encryption**: Server-side encryption enabled

### Secrets Management
- **Environment Variables**: Development
- **File-based Secrets**: Staging
- **Kubernetes Secrets**: Production
- **AWS Secrets Manager**: Enterprise (planned)

## 🏗️ Deployment Architecture

### Container Architecture
```dockerfile
FROM node:18-alpine
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production
COPY dist/ ./dist/
EXPOSE 3000
CMD ["npm", "start"]
```

### Kubernetes Architecture
```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: s3-mcp-server
spec:
  replicas: 3
  template:
    spec:
      containers:
      - name: s3-mcp-server
        image: s3-mcp-server:latest
        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"
          limits:
            memory: "512Mi"
            cpu: "500m"
```

## 📈 Scalability Considerations

### Horizontal Scaling
- **Stateless Design**: No server-side state
- **Load Balancing**: Multiple replicas support
- **Resource Limits**: Configurable resource constraints

### Vertical Scaling
- **Memory Management**: Efficient memory usage
- **CPU Optimization**: Async/await patterns
- **I/O Optimization**: Connection pooling

### Storage Scaling
- **S3 Partitioning**: Namespace-based organization
- **Date-based Folders**: Efficient querying
- **Lifecycle Policies**: Automated cleanup

## 🔍 Monitoring Architecture

### Health Checks
```typescript
interface HealthCheck {
  name: string;
  check: () => Promise<HealthCheckResult>;
  interval: number;
  timeout: number;
  critical: boolean;
}
```

### Metrics Collection
- **Prometheus Metrics**: Standard metrics format
- **Custom Metrics**: Application-specific metrics
- **Performance Tracking**: Request/response times

### Logging Architecture
```typescript
class EnhancedLogger {
  logError(error: Error | MCPError, context?: any): void;
  logPerformance(operation: string, duration: number, meta?: any): void;
  logSecurity(event: string, meta?: any): void;
  logAudit(action: string, meta?: any): void;
}
```

## 🔄 Extension Points

### Custom Middleware
```typescript
class CustomMiddleware implements Middleware {
  name = 'custom-middleware';
  priority = 150;
  
  async process(response: AIResponse): Promise<AIResponse> {
    // Custom processing logic
    return response;
  }
}
```

### Custom Health Checks
```typescript
const customHealthCheck: HealthCheck = {
  name: 'custom-service',
  check: async () => {
    // Custom health check logic
    return { status: 'healthy', timestamp: new Date(), duration: 0 };
  },
  interval: 30000,
  timeout: 5000,
  critical: true
};
```

### Custom Error Handlers
```typescript
const customAlertRule: AlertRule = {
  name: 'custom-alert',
  condition: (report) => report.count > 10,
  action: async (report) => {
    // Custom alert action
  },
  cooldown: 300000,
  enabled: true
};
```

## 📚 Design Patterns

### Factory Pattern
- **Error Factory**: Standardized error creation
- **Configuration Factory**: Environment-specific configs

### Observer Pattern
- **Health Monitoring**: Event-driven health checks
- **Error Reporting**: Event-based error handling

### Strategy Pattern
- **Retry Strategies**: Configurable retry policies
- **Storage Strategies**: Multiple storage backends

### Middleware Pattern
- **Request Processing**: Pluggable processing pipeline
- **Response Enhancement**: Modular response enrichment

## 🔮 Future Architecture

### Planned Enhancements
- **Multi-cloud Support**: Azure Blob, Google Cloud Storage
- **Event Streaming**: Kafka/EventBridge integration
- **Advanced Analytics**: ML-based pattern detection
- **GraphQL API**: Enhanced query capabilities

### Scalability Roadmap
- **Microservices**: Service decomposition
- **Event-driven Architecture**: Async processing
- **Caching Layer**: Redis/ElastiCache integration
- **CDN Integration**: Global content delivery

For implementation details, see:
- [Configuration Guide](CONFIGURATION.md)
- [API Documentation](API.md)
- [Deployment Guide](DEPLOYMENT.md)
