# API Documentation

The S3 MCP Server implements the Model Context Protocol (MCP) specification and provides additional HTTP endpoints for monitoring and management.

## 📋 Table of Contents

1. [MCP Protocol](#mcp-protocol)
2. [HTTP Endpoints](#http-endpoints)
3. [Tool Definitions](#tool-definitions)
4. [Error Responses](#error-responses)
5. [Examples](#examples)

---

## 🔌 MCP Protocol

The server implements the MCP specification for seamless integration with AI agents.

### Connection
- **Transport**: stdio (standard input/output)
- **Protocol Version**: 2024-11-05
- **Capabilities**: tools, logging

### Server Information
```json
{
  "name": "s3-mcp-server",
  "version": "1.0.0",
  "capabilities": {
    "tools": {},
    "logging": {}
  }
}
```

---

## 🛠️ Tool Definitions

### capture_ai_response

Captures and stores AI agent responses in S3 with intelligent namespace organization.

#### Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `content` | string | Yes | The AI response content to store |
| `namespace` | string | No | Kubernetes namespace (auto-detected if not provided) |
| `conversationId` | string | No | Unique conversation identifier |
| `requestType` | string | No | Type of request (e.g., "pod_log_analysis") |
| `metadata` | object | No | Additional metadata to store |

#### Example Request
```json
{
  "method": "tools/call",
  "params": {
    "name": "capture_ai_response",
    "arguments": {
      "content": "kubectl logs -n production web-server-12345 shows connection errors",
      "namespace": "production",
      "conversationId": "conv-123",
      "requestType": "pod_log_analysis",
      "metadata": {
        "userId": "user-456",
        "sessionId": "session-789"
      }
    }
  }
}
```

#### Response
```json
{
  "content": [
    {
      "type": "text",
      "text": "✅ Successfully stored AI response in S3\n\n**Details:**\n- S3 Key: production/2024/06/24/production_20240624_143022_abc123.txt\n- Bucket: your-bucket-name\n- Namespace: production (high confidence)\n- Upload Time: 245ms\n- File Size: 1.2 KB\n\n**Detected Information:**\n- Pod Names: web-server-12345\n- Kubernetes Errors: None detected\n- Metrics: None detected"
    }
  ]
}
```

---

## 🌐 HTTP Endpoints

### Health Check Endpoints

#### GET /health
Basic health check endpoint.

**Response:**
```json
{
  "status": "healthy",
  "timestamp": "2024-06-24T14:30:22.123Z",
  "uptime": 3600000
}
```

#### GET /health/detailed
Comprehensive health information.

**Response:**
```json
{
  "overall": "healthy",
  "checks": {
    "memory": {
      "status": "healthy",
      "message": "Memory usage: 45.2%",
      "details": {
        "usedMB": 128,
        "totalMB": 284,
        "usagePercent": 45.2
      },
      "timestamp": "2024-06-24T14:30:22.123Z",
      "duration": 2
    },
    "s3_connectivity": {
      "status": "healthy",
      "message": "S3 connection successful",
      "details": {
        "bucket": "your-bucket-name",
        "region": "us-east-1"
      },
      "timestamp": "2024-06-24T14:30:22.123Z",
      "duration": 156
    }
  },
  "timestamp": "2024-06-24T14:30:22.123Z",
  "uptime": 3600000
}
```

### Metrics Endpoints

#### GET /metrics
Prometheus-compatible metrics.

**Response:**
```
# HELP s3_mcp_uploads_total Total number of S3 uploads
# TYPE s3_mcp_uploads_total counter
s3_mcp_uploads_total{status="success"} 1234
s3_mcp_uploads_total{status="failure"} 12

# HELP s3_mcp_upload_duration_seconds Upload duration in seconds
# TYPE s3_mcp_upload_duration_seconds histogram
s3_mcp_upload_duration_seconds_bucket{le="0.1"} 456
s3_mcp_upload_duration_seconds_bucket{le="0.5"} 789
s3_mcp_upload_duration_seconds_bucket{le="1.0"} 890
s3_mcp_upload_duration_seconds_bucket{le="+Inf"} 1234
```

#### GET /metrics/errors
Error statistics and reports.

**Response:**
```json
{
  "statistics": {
    "totalReports": 45,
    "uniqueErrors": 12,
    "criticalErrors": 2,
    "recentErrors": 8
  },
  "recentErrors": [
    {
      "id": "err_1719234622_abc123",
      "timestamp": "2024-06-24T14:30:22.123Z",
      "error": {
        "name": "MCPError",
        "message": "S3 upload failed: AccessDenied",
        "code": "S3_UPLOAD_FAILED",
        "severity": "HIGH"
      },
      "count": 3,
      "firstSeen": "2024-06-24T14:25:22.123Z",
      "lastSeen": "2024-06-24T14:30:22.123Z"
    }
  ]
}
```

### Configuration Endpoints

#### GET /config
Current configuration (sensitive values masked).

**Response:**
```json
{
  "aws": {
    "region": "us-east-1",
    "bucketName": "your-bucket-name",
    "accessKeyId": "AKIA***************",
    "secretAccessKey": "***************************"
  },
  "mcp": {
    "serverName": "s3-mcp-server",
    "port": 3000
  },
  "kubernetes": {
    "defaultNamespacePrefix": "NO_NAMESPACE",
    "enableFormatValidation": true
  },
  "logging": {
    "level": "info",
    "enableConsoleLogging": false,
    "filePath": "./logs/mcp-server.log"
  }
}
```

---

## ❌ Error Responses

### MCP Protocol Errors

#### Invalid Tool Call
```json
{
  "error": {
    "code": -32602,
    "message": "Invalid params",
    "data": {
      "details": "Missing required parameter: content"
    }
  }
}
```

#### Tool Not Found
```json
{
  "error": {
    "code": -32601,
    "message": "Method not found",
    "data": {
      "details": "Tool 'unknown_tool' not found"
    }
  }
}
```

### HTTP Error Responses

#### 400 Bad Request
```json
{
  "error": "Bad Request",
  "message": "Invalid request format",
  "timestamp": "2024-06-24T14:30:22.123Z"
}
```

#### 500 Internal Server Error
```json
{
  "error": "Internal Server Error",
  "message": "An unexpected error occurred",
  "errorId": "err_1719234622_abc123",
  "timestamp": "2024-06-24T14:30:22.123Z"
}
```

#### 503 Service Unavailable
```json
{
  "error": "Service Unavailable",
  "message": "S3 service is temporarily unavailable",
  "retryAfter": 30,
  "timestamp": "2024-06-24T14:30:22.123Z"
}
```

---

## 📝 Examples

### Complete MCP Integration Example

```typescript
import { Client } from '@modelcontextprotocol/sdk/client/index.js';
import { StdioClientTransport } from '@modelcontextprotocol/sdk/client/stdio.js';

// Connect to S3 MCP Server
const transport = new StdioClientTransport({
  command: 'node',
  args: ['dist/server.js']
});

const client = new Client({
  name: "example-client",
  version: "1.0.0"
}, {
  capabilities: {}
});

await client.connect(transport);

// Capture AI response
const result = await client.callTool({
  name: "capture_ai_response",
  arguments: {
    content: "kubectl logs -n production web-server-12345 shows errors",
    conversationId: "conv-123",
    requestType: "pod_log_analysis"
  }
});

console.log(result);
```

### Health Check Integration

```bash
#!/bin/bash
# Health check script for monitoring

HEALTH_URL="http://localhost:3000/health/detailed"
RESPONSE=$(curl -s "$HEALTH_URL")
STATUS=$(echo "$RESPONSE" | jq -r '.overall')

if [ "$STATUS" = "healthy" ]; then
    echo "✅ S3 MCP Server is healthy"
    exit 0
else
    echo "❌ S3 MCP Server is unhealthy: $STATUS"
    echo "$RESPONSE" | jq '.checks'
    exit 1
fi
```

### Metrics Collection

```python
import requests
import time

def collect_metrics():
    """Collect and process S3 MCP Server metrics"""
    
    # Get Prometheus metrics
    metrics_response = requests.get('http://localhost:3000/metrics')
    metrics_text = metrics_response.text
    
    # Get error statistics
    errors_response = requests.get('http://localhost:3000/metrics/errors')
    error_stats = errors_response.json()
    
    # Process and store metrics
    print(f"Total uploads: {error_stats['statistics']['totalReports']}")
    print(f"Error rate: {error_stats['statistics']['criticalErrors']}")
    
    return {
        'metrics': metrics_text,
        'errors': error_stats,
        'timestamp': time.time()
    }

# Collect metrics every 60 seconds
while True:
    data = collect_metrics()
    time.sleep(60)
```

---

## 🔗 Related Documentation

- [Configuration Guide](CONFIGURATION.md)
- [Deployment Guide](DEPLOYMENT.md)
- [Troubleshooting Guide](TROUBLESHOOTING.md)
- [MCP Specification](https://spec.modelcontextprotocol.io/)
