# Deployment Guide

This guide covers deploying the S3 MCP Server in various environments, from development to production Kubernetes clusters.

## 🚀 Deployment Options

### 1. Local Development
### 2. Docker Container
### 3. Kubernetes Deployment
### 4. AWS ECS/Fargate
### 5. Serverless (AWS Lambda)

---

## 🏠 Local Development Deployment

### Prerequisites
- Node.js 18+
- AWS CLI configured
- Git

### Setup Steps

1. **<PERSON><PERSON> and Install**
   ```bash
   git clone <repository-url>
   cd s3-mcp-server
   npm install
   ```

2. **Environment Configuration**
   ```bash
   cp .env.example .env.development
   ```

   Edit `.env.development`:
   ```bash
   # AWS Configuration
   AWS_ACCESS_KEY_ID=your-dev-access-key
   AWS_SECRET_ACCESS_KEY=your-dev-secret-key
   AWS_REGION=us-east-1
   S3_BUCKET_NAME=your-dev-bucket

   # Development Settings
   NODE_ENV=development
   LOG_LEVEL=debug
   ENABLE_CONSOLE_LOGGING=true
   DEFAULT_NAMESPACE_PREFIX=DEV_NAMESPACE
   ```

3. **Validate and Start**
   ```bash
   npm run config:validate
   npm run dev
   ```

### Development Features
- Hot reload enabled
- Enhanced debugging
- Relaxed validation
- Console logging

---

## 🐳 Docker Deployment

### Build Docker Image

1. **Create Dockerfile** (already included):
   ```dockerfile
   FROM node:18-alpine
   WORKDIR /app
   COPY package*.json ./
   RUN npm ci --only=production
   COPY dist/ ./dist/
   EXPOSE 3000
   CMD ["npm", "start"]
   ```

2. **Build Image**
   ```bash
   npm run build
   docker build -t s3-mcp-server:latest .
   ```

3. **Run Container**
   ```bash
   docker run -d \
     --name s3-mcp-server \
     -p 3000:3000 \
     -e AWS_ACCESS_KEY_ID=your-key \
     -e AWS_SECRET_ACCESS_KEY=your-secret \
     -e AWS_REGION=us-east-1 \
     -e S3_BUCKET_NAME=your-bucket \
     -e NODE_ENV=production \
     s3-mcp-server:latest
   ```

### Docker Compose

Create `docker-compose.yml`:
```yaml
version: '3.8'
services:
  s3-mcp-server:
    build: .
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - AWS_ACCESS_KEY_ID=${AWS_ACCESS_KEY_ID}
      - AWS_SECRET_ACCESS_KEY=${AWS_SECRET_ACCESS_KEY}
      - AWS_REGION=${AWS_REGION}
      - S3_BUCKET_NAME=${S3_BUCKET_NAME}
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
```

Run with:
```bash
docker-compose up -d
```

---

## ☸️ Kubernetes Deployment

### Prerequisites
- Kubernetes cluster access
- kubectl configured
- AWS credentials

### 1. Create Namespace
```bash
kubectl create namespace s3-mcp-server
```

### 2. Create Secret for AWS Credentials
```bash
kubectl create secret generic aws-credentials \
  --from-literal=access-key-id=your-access-key \
  --from-literal=secret-access-key=your-secret-key \
  --namespace=s3-mcp-server
```

### 3. Create ConfigMap
```yaml
# configmap.yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: s3-mcp-config
  namespace: s3-mcp-server
data:
  NODE_ENV: "production"
  AWS_REGION: "us-east-1"
  S3_BUCKET_NAME: "your-production-bucket"
  LOG_LEVEL: "info"
  ENABLE_CONSOLE_LOGGING: "false"
  DEFAULT_NAMESPACE_PREFIX: "NO_NAMESPACE"
  ENABLE_NAMESPACE_FORMAT_VALIDATION: "true"
```

### 4. Create Deployment
```yaml
# deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: s3-mcp-server
  namespace: s3-mcp-server
  labels:
    app: s3-mcp-server
spec:
  replicas: 3
  selector:
    matchLabels:
      app: s3-mcp-server
  template:
    metadata:
      labels:
        app: s3-mcp-server
    spec:
      containers:
      - name: s3-mcp-server
        image: s3-mcp-server:latest
        ports:
        - containerPort: 3000
        env:
        - name: AWS_ACCESS_KEY_ID
          valueFrom:
            secretKeyRef:
              name: aws-credentials
              key: access-key-id
        - name: AWS_SECRET_ACCESS_KEY
          valueFrom:
            secretKeyRef:
              name: aws-credentials
              key: secret-access-key
        envFrom:
        - configMapRef:
            name: s3-mcp-config
        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"
          limits:
            memory: "512Mi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /health
            port: 3000
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /health
            port: 3000
          initialDelaySeconds: 5
          periodSeconds: 5
```

### 5. Create Service
```yaml
# service.yaml
apiVersion: v1
kind: Service
metadata:
  name: s3-mcp-service
  namespace: s3-mcp-server
spec:
  selector:
    app: s3-mcp-server
  ports:
  - protocol: TCP
    port: 80
    targetPort: 3000
  type: ClusterIP
```

### 6. Deploy to Kubernetes
```bash
kubectl apply -f configmap.yaml
kubectl apply -f deployment.yaml
kubectl apply -f service.yaml
```

### 7. Verify Deployment
```bash
# Check pods
kubectl get pods -n s3-mcp-server

# Check logs
kubectl logs -f deployment/s3-mcp-server -n s3-mcp-server

# Test health endpoint
kubectl port-forward service/s3-mcp-service 3000:80 -n s3-mcp-server
curl http://localhost:3000/health
```

---

## 🔧 Production Considerations

### Security
- Use IAM roles instead of access keys when possible
- Enable S3 bucket encryption
- Implement network policies
- Use secrets management (AWS Secrets Manager, HashiCorp Vault)

### Monitoring
- Set up CloudWatch logs
- Configure Prometheus metrics
- Implement alerting rules
- Monitor S3 costs and usage

### Scaling
- Configure horizontal pod autoscaling
- Set appropriate resource limits
- Use multiple availability zones
- Implement load balancing

### Backup and Recovery
- Enable S3 versioning
- Configure cross-region replication
- Implement disaster recovery procedures
- Regular backup testing

---

## 📊 Monitoring and Observability

### Health Checks
```bash
# Basic health check
curl http://your-server:3000/health

# Detailed health information
curl http://your-server:3000/health/detailed
```

### Metrics Collection
```bash
# Get performance metrics
curl http://your-server:3000/metrics

# Get error statistics
curl http://your-server:3000/metrics/errors
```

### Log Aggregation
- Configure centralized logging (ELK stack, Fluentd)
- Set up log rotation
- Implement log analysis and alerting

---

## 🚨 Troubleshooting

### Common Issues

1. **S3 Connection Failures**
   ```bash
   # Test S3 connectivity
   npm run test:connection
   
   # Check AWS credentials
   aws s3 ls s3://your-bucket-name
   ```

2. **Memory Issues**
   ```bash
   # Monitor memory usage
   kubectl top pods -n s3-mcp-server
   
   # Check for memory leaks
   npm run health:memory
   ```

3. **Configuration Problems**
   ```bash
   # Validate configuration
   npm run config:validate
   
   # Show current config
   npm run config:show
   ```

### Debug Mode
```bash
# Enable debug logging
export LOG_LEVEL=debug
npm start

# Or in Kubernetes
kubectl set env deployment/s3-mcp-server LOG_LEVEL=debug -n s3-mcp-server
```

---

## 🔄 Updates and Maintenance

### Rolling Updates
```bash
# Update image
kubectl set image deployment/s3-mcp-server s3-mcp-server=s3-mcp-server:v2.0.0 -n s3-mcp-server

# Monitor rollout
kubectl rollout status deployment/s3-mcp-server -n s3-mcp-server
```

### Backup Configuration
```bash
# Backup Kubernetes resources
kubectl get all -n s3-mcp-server -o yaml > s3-mcp-backup.yaml

# Backup configuration
kubectl get configmap s3-mcp-config -n s3-mcp-server -o yaml > config-backup.yaml
```

For more detailed configuration options, see [CONFIGURATION.md](CONFIGURATION.md).
