# EKS Pod Identity Setup Guide

This guide shows how to set up EKS Pod Identity for the S3 MCP Server, eliminating the need for hardcoded AWS credentials.

## 🎯 **Benefits of EKS Pod Identity**

- ✅ **No hardcoded credentials** - Credentials never stored in secrets or environment variables
- ✅ **Automatic credential rotation** - AWS handles credential lifecycle
- ✅ **Fine-grained permissions** - IAM roles with least privilege access
- ✅ **Audit trail** - All S3 access logged with pod identity
- ✅ **Secure by default** - Credentials only available to authorized pods

---

## 🚀 **Step 1: Create IAM Role and Policy**

### **1.1 Create S3 Access Policy**

```bash
# Create policy document
cat > s3-mcp-policy.json << EOF
{
    "Version": "2012-10-17",
    "Statement": [
        {
            "Effect": "Allow",
            "Action": [
                "s3:GetObject",
                "s3:PutObject",
                "s3:DeleteObject",
                "s3:ListBucket"
            ],
            "Resource": [
                "arn:aws:s3:::your-s3-bucket-name",
                "arn:aws:s3:::your-s3-bucket-name/*"
            ]
        }
    ]
}
EOF

# Create the policy
aws iam create-policy \
    --policy-name S3MCPServerPolicy \
    --policy-document file://s3-mcp-policy.json
```

### **1.2 Create IAM Role for Pod Identity**

```bash
# Create trust policy for EKS Pod Identity
cat > trust-policy.json << EOF
{
    "Version": "2012-10-17",
    "Statement": [
        {
            "Effect": "Allow",
            "Principal": {
                "Service": "pods.eks.amazonaws.com"
            },
            "Action": [
                "sts:AssumeRole",
                "sts:TagSession"
            ]
        }
    ]
}
EOF

# Create the role
aws iam create-role \
    --role-name s3-mcp-pod-identity-role \
    --assume-role-policy-document file://trust-policy.json

# Attach the policy to the role
aws iam attach-role-policy \
    --role-name s3-mcp-pod-identity-role \
    --policy-arn arn:aws:iam::YOUR_ACCOUNT_ID:policy/S3MCPServerPolicy
```

---

## 🔧 **Step 2: Configure EKS Pod Identity**

### **2.1 Enable Pod Identity on EKS Cluster**

```bash
# Enable Pod Identity add-on (if not already enabled)
aws eks create-addon \
    --cluster-name your-cluster-name \
    --addon-name eks-pod-identity-agent

# Check addon status
aws eks describe-addon \
    --cluster-name your-cluster-name \
    --addon-name eks-pod-identity-agent
```

### **2.2 Create Pod Identity Association**

```bash
# Create the association between ServiceAccount and IAM Role
aws eks create-pod-identity-association \
    --cluster-name your-cluster-name \
    --namespace s3-mcp-server \
    --service-account s3-mcp-service-account \
    --role-arn arn:aws:iam::YOUR_ACCOUNT_ID:role/s3-mcp-pod-identity-role

# Verify the association
aws eks list-pod-identity-associations \
    --cluster-name your-cluster-name
```

---

## 📋 **Step 3: Update Kubernetes Deployment**

### **3.1 Update ServiceAccount ARN**

Edit `k8s/http-deployment.yaml` and update the ServiceAccount:

```yaml
apiVersion: v1
kind: ServiceAccount
metadata:
  name: s3-mcp-service-account
  namespace: s3-mcp-server
  annotations:
    eks.amazonaws.com/role-arn: arn:aws:iam::YOUR_ACCOUNT_ID:role/s3-mcp-pod-identity-role
```

### **3.2 Update Environment Variables**

The deployment now only needs:

```yaml
env:
- name: S3_BUCKET_NAME
  value: "your-s3-bucket-name"
- name: AWS_REGION
  value: "us-east-1"
# No AWS_ACCESS_KEY_ID or AWS_SECRET_ACCESS_KEY needed!
```

---

## 🚀 **Step 4: Deploy**

### **4.1 Create Namespace**

```bash
kubectl create namespace s3-mcp-server
```

### **4.2 Deploy the Application**

```bash
# Apply the updated deployment
kubectl apply -f k8s/http-deployment.yaml

# Check pod status
kubectl get pods -n s3-mcp-server

# Check logs to verify Pod Identity is working
kubectl logs -f deployment/s3-mcp-server -n s3-mcp-server
```

### **4.3 Verify Pod Identity**

```bash
# Check if pod has AWS credentials via Pod Identity
kubectl exec -it deployment/s3-mcp-server -n s3-mcp-server -- \
  aws sts get-caller-identity

# Test S3 access
kubectl exec -it deployment/s3-mcp-server -n s3-mcp-server -- \
  aws s3 ls s3://your-bucket-name
```

---

## 🔍 **Step 5: Verification and Testing**

### **5.1 Test the Health Endpoint**

```bash
# Port forward to test locally
kubectl port-forward deployment/s3-mcp-server 3000:3000 -n s3-mcp-server

# Test health endpoint
curl http://localhost:3000/health/detailed
```

### **5.2 Test MCP Tool Call**

```bash
# Test the capture tool
curl -X POST -H "Content-Type: application/json" \
  -d '{
    "jsonrpc": "2.0",
    "id": 1,
    "method": "tools/call",
    "params": {
      "name": "capture_ai_response",
      "arguments": {
        "content": "kubectl logs -n production web-server-12345 shows connection errors",
        "conversationId": "test-pod-identity",
        "requestType": "pod_troubleshooting"
      }
    }
  }' \
  http://localhost:3000/mcp
```

---

## 🛠️ **Troubleshooting**

### **Common Issues**

#### 1. Pod Identity Association Not Found
```bash
# Check if association exists
aws eks describe-pod-identity-association \
    --cluster-name your-cluster-name \
    --association-id association-id

# Recreate if needed
aws eks delete-pod-identity-association \
    --cluster-name your-cluster-name \
    --association-id association-id
```

#### 2. IAM Permission Errors
```bash
# Check role permissions
aws iam list-attached-role-policies \
    --role-name s3-mcp-pod-identity-role

# Test role assumption
aws sts assume-role \
    --role-arn arn:aws:iam::YOUR_ACCOUNT_ID:role/s3-mcp-pod-identity-role \
    --role-session-name test-session
```

#### 3. Pod Can't Access S3
```bash
# Check pod logs for credential errors
kubectl logs deployment/s3-mcp-server -n s3-mcp-server

# Verify pod identity agent is running
kubectl get pods -n kube-system | grep eks-pod-identity-agent

# Check service account annotation
kubectl describe serviceaccount s3-mcp-service-account -n s3-mcp-server
```

---

## 📊 **Security Best Practices**

### **1. Least Privilege IAM Policy**

```json
{
    "Version": "2012-10-17",
    "Statement": [
        {
            "Effect": "Allow",
            "Action": [
                "s3:PutObject",
                "s3:PutObjectAcl"
            ],
            "Resource": "arn:aws:s3:::your-bucket/s3-mcp-server/*"
        },
        {
            "Effect": "Allow",
            "Action": [
                "s3:ListBucket"
            ],
            "Resource": "arn:aws:s3:::your-bucket",
            "Condition": {
                "StringLike": {
                    "s3:prefix": "s3-mcp-server/*"
                }
            }
        }
    ]
}
```

### **2. Resource Tagging**

```bash
# Tag the IAM role for better governance
aws iam tag-role \
    --role-name s3-mcp-pod-identity-role \
    --tags Key=Application,Value=S3MCPServer \
           Key=Environment,Value=Production \
           Key=Team,Value=Platform
```

### **3. Monitoring and Auditing**

- ✅ Enable CloudTrail for S3 API calls
- ✅ Set up CloudWatch alarms for unusual S3 access patterns
- ✅ Monitor Pod Identity association changes
- ✅ Regular IAM access reviews

---

## 🎉 **Complete Example Commands**

```bash
# 1. Set variables
export CLUSTER_NAME="your-cluster-name"
export ACCOUNT_ID="************"
export BUCKET_NAME="your-s3-bucket-name"
export REGION="us-east-1"

# 2. Create IAM policy and role
aws iam create-policy --policy-name S3MCPServerPolicy --policy-document file://s3-mcp-policy.json
aws iam create-role --role-name s3-mcp-pod-identity-role --assume-role-policy-document file://trust-policy.json
aws iam attach-role-policy --role-name s3-mcp-pod-identity-role --policy-arn arn:aws:iam::$ACCOUNT_ID:policy/S3MCPServerPolicy

# 3. Create Pod Identity association
aws eks create-pod-identity-association \
    --cluster-name $CLUSTER_NAME \
    --namespace s3-mcp-server \
    --service-account s3-mcp-service-account \
    --role-arn arn:aws:iam::$ACCOUNT_ID:role/s3-mcp-pod-identity-role

# 4. Deploy application
kubectl create namespace s3-mcp-server
kubectl apply -f k8s/http-deployment.yaml

# 5. Verify
kubectl get pods -n s3-mcp-server
kubectl logs -f deployment/s3-mcp-server -n s3-mcp-server
```

Your S3 MCP Server now runs with **zero hardcoded credentials** and maximum security! 🔒✨
