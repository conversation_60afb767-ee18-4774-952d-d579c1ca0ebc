# Troubleshooting Guide

This guide helps you diagnose and resolve common issues with the S3 MCP Server.

## 🔍 Quick Diagnostics

### Health Check Commands
```bash
# Basic health check
npm run health:check

# Detailed system information
npm run config:show

# Test S3 connectivity
npm run test:connection

# Validate configuration
npm run config:validate
```

---

## 🚨 Common Issues

### 1. S3 Connection Problems

#### Symptoms
- Upload failures
- "AccessDenied" errors
- Connection timeouts

#### Diagnosis
```bash
# Test AWS credentials
aws s3 ls s3://your-bucket-name

# Check S3 connectivity
npm run test:connection

# Verify bucket permissions
aws s3api get-bucket-policy --bucket your-bucket-name
```

#### Solutions

**Invalid Credentials:**
```bash
# Update credentials
export AWS_ACCESS_KEY_ID=your-correct-key
export AWS_SECRET_ACCESS_KEY=your-correct-secret

# Or update .env file
echo "AWS_ACCESS_KEY_ID=your-correct-key" >> .env
```

**Bucket Permissions:**
```json
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Effect": "Allow",
      "Principal": {
        "AWS": "arn:aws:iam::ACCOUNT:user/USERNAME"
      },
      "Action": [
        "s3:GetObject",
        "s3:PutObject",
        "s3:DeleteObject",
        "s3:ListBucket"
      ],
      "Resource": [
        "arn:aws:s3:::your-bucket-name",
        "arn:aws:s3:::your-bucket-name/*"
      ]
    }
  ]
}
```

**Network Issues:**
```bash
# Test network connectivity
curl -I https://s3.amazonaws.com

# Check DNS resolution
nslookup s3.amazonaws.com

# Test with different region
export AWS_REGION=us-west-2
```

### 2. Configuration Errors

#### Symptoms
- Server won't start
- "Configuration invalid" errors
- Missing environment variables

#### Diagnosis
```bash
# Validate all configuration
npm run config:validate

# Show current configuration
npm run config:show

# Check environment variables
npm run config:env
```

#### Solutions

**Missing Variables:**
```bash
# Copy example configuration
cp .env.example .env

# Set required variables
export AWS_ACCESS_KEY_ID=your-key
export AWS_SECRET_ACCESS_KEY=your-secret
export AWS_REGION=us-east-1
export S3_BUCKET_NAME=your-bucket
```

**Invalid Format:**
```bash
# Check AWS region format
echo $AWS_REGION  # Should be like "us-east-1"

# Validate bucket name
echo $S3_BUCKET_NAME  # Should follow S3 naming rules
```

### 3. Memory Issues

#### Symptoms
- Server crashes
- "Out of memory" errors
- Slow performance

#### Diagnosis
```bash
# Check memory usage
npm run health:memory

# Monitor in real-time
top -p $(pgrep -f "node.*server")

# Check for memory leaks
node --inspect dist/server.js
```

#### Solutions

**Increase Memory Limit:**
```bash
# For Node.js
export NODE_OPTIONS="--max-old-space-size=4096"

# For Docker
docker run --memory=2g s3-mcp-server

# For Kubernetes
resources:
  limits:
    memory: "2Gi"
```

**Optimize Configuration:**
```bash
# Reduce file retention
FILE_RETENTION_DAYS=7

# Enable compression
ENABLE_COMPRESSION=true

# Limit file size
MAX_FILE_SIZE_MB=50
```

### 4. Namespace Detection Issues

#### Symptoms
- Files stored in "NO_NAMESPACE" folder
- Incorrect namespace detection
- Missing pod information

#### Diagnosis
```bash
# Test namespace detection
npm run demo:namespace-flexibility

# Check detection patterns
npm run test:namespace-patterns

# Validate input content
echo "kubectl logs -n production pod-123" | npm run test:detection
```

#### Solutions

**Enable Format Validation:**
```bash
export ENABLE_NAMESPACE_FORMAT_VALIDATION=true
```

**Custom Namespace Patterns:**
```bash
# Add custom detection patterns (if needed)
export CUSTOM_NAMESPACE_PATTERNS="team-.*,customer-.*,feature-.*"
```

**Manual Namespace Override:**
```json
{
  "name": "capture_ai_response",
  "arguments": {
    "content": "Your content here",
    "namespace": "explicit-namespace"
  }
}
```

### 5. Performance Issues

#### Symptoms
- Slow upload times
- High CPU usage
- Request timeouts

#### Diagnosis
```bash
# Check performance metrics
npm run metrics:show

# Monitor upload times
npm run test:performance

# Profile the application
node --prof dist/server.js
```

#### Solutions

**Optimize S3 Settings:**
```bash
# Use appropriate storage class
S3_STORAGE_CLASS=STANDARD_IA

# Enable multipart uploads for large files
ENABLE_MULTIPART_UPLOAD=true

# Optimize retry settings
S3_MAX_RETRIES=3
S3_RETRY_DELAY=1000
```

**Scale Resources:**
```bash
# Increase CPU limits (Kubernetes)
resources:
  limits:
    cpu: "1000m"

# Use multiple replicas
replicas: 3
```

---

## 🔧 Debug Mode

### Enable Debug Logging
```bash
# Environment variable
export LOG_LEVEL=debug

# Or in .env file
echo "LOG_LEVEL=debug" >> .env

# Restart server
npm restart
```

### Debug Output Example
```
[DEBUG] 14:30:22 Starting retry operation {
  operationName: 'S3Upload',
  requestId: 'req-123',
  maxAttempts: 3,
  baseDelay: 1000
}

[DEBUG] 14:30:22 Uploading response to S3 {
  key: 'production/2024/06/24/production_20240624_143022_abc123.txt',
  bucket: 'your-bucket-name',
  contentLength: 1234,
  namespace: 'production',
  responseId: 'resp-456'
}
```

---

## 📊 Monitoring and Alerts

### Set Up Monitoring
```bash
# Health check endpoint
curl http://localhost:3000/health/detailed

# Metrics endpoint
curl http://localhost:3000/metrics

# Error statistics
curl http://localhost:3000/metrics/errors
```

### Alert Conditions
- Health check failures
- High error rates (>5%)
- Memory usage >80%
- S3 upload failures
- Response time >5 seconds

### Example Alert Script
```bash
#!/bin/bash
# alert-check.sh

HEALTH=$(curl -s http://localhost:3000/health | jq -r '.status')
ERRORS=$(curl -s http://localhost:3000/metrics/errors | jq '.statistics.criticalErrors')

if [ "$HEALTH" != "healthy" ] || [ "$ERRORS" -gt 5 ]; then
    echo "ALERT: S3 MCP Server issues detected"
    echo "Health: $HEALTH"
    echo "Critical Errors: $ERRORS"
    # Send notification (email, Slack, etc.)
fi
```

---

## 🆘 Emergency Procedures

### Server Won't Start
1. Check configuration: `npm run config:validate`
2. Verify dependencies: `npm install`
3. Check ports: `lsof -i :3000`
4. Review logs: `tail -f logs/mcp-server.log`

### High Error Rates
1. Check S3 service status
2. Verify AWS credentials
3. Review error logs: `npm run metrics:errors`
4. Restart server if needed

### Memory Leaks
1. Enable memory monitoring
2. Restart server
3. Check for large files
4. Review retention settings

### Data Loss Prevention
1. Enable S3 versioning
2. Set up cross-region replication
3. Regular backup verification
4. Monitor bucket policies

---

## 📞 Getting Help

### Log Collection
```bash
# Collect all relevant logs
mkdir debug-info
cp logs/*.log debug-info/
npm run config:show > debug-info/config.txt
npm run health:check > debug-info/health.txt
npm run metrics:show > debug-info/metrics.txt

# Create archive
tar -czf debug-info.tar.gz debug-info/
```

### Information to Include
- Server version
- Environment (development/staging/production)
- Error messages and stack traces
- Configuration (with sensitive data masked)
- Health check results
- Recent log entries

### Support Channels
- GitHub Issues: [Repository Issues](https://github.com/your-repo/issues)
- Documentation: [Full Documentation](README.md)
- Configuration Help: [Configuration Guide](CONFIGURATION.md)

---

## 🔄 Recovery Procedures

### Configuration Recovery
```bash
# Restore from backup
cp .env.backup .env

# Reset to defaults
cp .env.example .env
npm run config:validate
```

### Data Recovery
```bash
# List recent S3 uploads
aws s3 ls s3://your-bucket-name/ --recursive --human-readable

# Restore from S3 versioning
aws s3api list-object-versions --bucket your-bucket-name

# Download specific version
aws s3api get-object --bucket your-bucket-name --key your-key --version-id your-version-id file.txt
```

### Service Recovery
```bash
# Restart service
npm restart

# Or with Docker
docker restart s3-mcp-server

# Or with Kubernetes
kubectl rollout restart deployment/s3-mcp-server -n s3-mcp-server
```

For more information, see:
- [Configuration Guide](CONFIGURATION.md)
- [Deployment Guide](DEPLOYMENT.md)
- [API Documentation](API.md)
