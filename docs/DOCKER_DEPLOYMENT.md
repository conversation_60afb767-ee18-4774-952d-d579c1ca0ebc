# Docker Deployment Guide

This guide covers building and deploying the S3 MCP Server as a Docker container to Kubernetes.

## 🐳 **Docker Build Process**

### **Step 1: Build the Docker Image**

#### Option A: Using npm scripts (Recommended)
```bash
# Build image locally
npm run docker:build

# Build with custom tag
npm run docker:build -- -t v1.0.0

# Build and push to registry
npm run docker:build-push -- -r your-registry.com -t v1.0.0
```

#### Option B: Using the build script directly
```bash
# Basic build
./scripts/build-docker.sh

# Build with custom options
./scripts/build-docker.sh -t v1.0.0 -r your-registry.com --push

# Build for different platform
./scripts/build-docker.sh --platform linux/arm64 -t arm64-latest
```

#### Option C: Direct Docker commands
```bash
# Build the image
docker build -t s3-mcp-server:latest .

# Build for specific platform
docker build --platform linux/amd64 -t s3-mcp-server:latest .
```

### **Step 2: Test the Image Locally**

```bash
# Run with test credentials
docker run -d -p 3000:3000 \
  -e HTTP_MODE=true \
  -e AWS_ACCESS_KEY_ID=your-access-key \
  -e AWS_SECRET_ACCESS_KEY=your-secret-key \
  -e AWS_REGION=us-east-1 \
  -e S3_BUCKET_NAME=your-bucket-name \
  s3-mcp-server:latest

# Test the endpoints
curl http://localhost:3000/health
curl http://localhost:3000/
```

### **Step 3: Push to Registry**

#### Docker Hub
```bash
# Tag for Docker Hub
docker tag s3-mcp-server:latest your-username/s3-mcp-server:latest

# Push to Docker Hub
docker push your-username/s3-mcp-server:latest
```

#### Private Registry
```bash
# Tag for private registry
docker tag s3-mcp-server:latest your-registry.com/s3-mcp-server:latest

# Login to registry
docker login your-registry.com

# Push to registry
docker push your-registry.com/s3-mcp-server:latest
```

#### AWS ECR
```bash
# Get login token
aws ecr get-login-password --region us-east-1 | docker login --username AWS --password-stdin 123456789012.dkr.ecr.us-east-1.amazonaws.com

# Tag for ECR
docker tag s3-mcp-server:latest 123456789012.dkr.ecr.us-east-1.amazonaws.com/s3-mcp-server:latest

# Push to ECR
docker push 123456789012.dkr.ecr.us-east-1.amazonaws.com/s3-mcp-server:latest
```

---

## ☸️ **Kubernetes Deployment**

### **Step 1: Update Image in Deployment**

Edit `k8s/http-deployment.yaml` and update the image:

```yaml
containers:
- name: s3-mcp-server
  image: your-registry.com/s3-mcp-server:v1.0.0  # Update this line
  imagePullPolicy: Always
```

### **Step 2: Create Secrets**

```bash
# Create namespace
kubectl create namespace s3-mcp-server

# Create AWS credentials secret
kubectl create secret generic aws-credentials \
  --from-literal=access-key-id=YOUR_AWS_ACCESS_KEY \
  --from-literal=secret-access-key=YOUR_AWS_SECRET_KEY \
  --from-literal=bucket-name=your-s3-bucket-name \
  --namespace=s3-mcp-server

# Optional: Create image pull secret for private registry
kubectl create secret docker-registry regcred \
  --docker-server=your-registry.com \
  --docker-username=your-username \
  --docker-password=your-password \
  --docker-email=<EMAIL> \
  --namespace=s3-mcp-server
```

### **Step 3: Deploy to Kubernetes**

```bash
# Apply the deployment
kubectl apply -f k8s/http-deployment.yaml

# Check deployment status
kubectl get pods -n s3-mcp-server
kubectl get services -n s3-mcp-server
kubectl get ingress -n s3-mcp-server

# Check logs
kubectl logs -f deployment/s3-mcp-server -n s3-mcp-server
```

### **Step 4: Configure External Access**

#### Option A: LoadBalancer (Cloud providers)
```yaml
# In k8s/http-deployment.yaml, change service type:
spec:
  type: LoadBalancer
  ports:
  - name: http
    port: 80
    targetPort: 3000
```

#### Option B: Ingress (Recommended)
```yaml
# Update the ingress in k8s/http-deployment.yaml:
spec:
  rules:
  - host: your-actual-domain.com  # Replace with your domain
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: s3-mcp-service
            port:
              number: 80
```

#### Option C: NodePort (Development)
```yaml
# In k8s/http-deployment.yaml, change service type:
spec:
  type: NodePort
  ports:
  - name: http
    port: 80
    targetPort: 3000
    nodePort: 30000  # Access via http://node-ip:30000
```

---

## 🔧 **Production Configuration**

### **Environment Variables**

The container supports these environment variables:

```bash
# Required
HTTP_MODE=true                    # Enable HTTP bridge
AWS_ACCESS_KEY_ID=your-key
AWS_SECRET_ACCESS_KEY=your-secret
AWS_REGION=us-east-1
S3_BUCKET_NAME=your-bucket

# Optional
HTTP_PORT=3000                    # HTTP server port
NODE_ENV=production               # Environment
LOG_LEVEL=info                    # Logging level
CORS_ORIGINS=*                    # CORS origins
DEFAULT_NAMESPACE_PREFIX=NO_NAMESPACE
ENABLE_NAMESPACE_FORMAT_VALIDATION=true
```

### **Resource Limits**

Recommended resource configuration:

```yaml
resources:
  requests:
    memory: "256Mi"
    cpu: "250m"
  limits:
    memory: "512Mi"
    cpu: "500m"
```

### **Health Checks**

The container includes built-in health checks:

```yaml
livenessProbe:
  httpGet:
    path: /health
    port: 3000
  initialDelaySeconds: 30
  periodSeconds: 10

readinessProbe:
  httpGet:
    path: /health
    port: 3000
  initialDelaySeconds: 5
  periodSeconds: 5
```

---

## 📊 **Monitoring and Scaling**

### **Horizontal Pod Autoscaler**

```yaml
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: s3-mcp-hpa
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: s3-mcp-server
  minReplicas: 2
  maxReplicas: 10
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
```

### **Prometheus Monitoring**

```yaml
apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  name: s3-mcp-metrics
spec:
  selector:
    matchLabels:
      app: s3-mcp-server
  endpoints:
  - port: http
    path: /metrics
    interval: 30s
```

---

## 🚨 **Troubleshooting**

### **Common Issues**

#### 1. Image Pull Errors
```bash
# Check image pull secret
kubectl get secrets -n s3-mcp-server

# Check pod events
kubectl describe pod <pod-name> -n s3-mcp-server

# Verify image exists
docker pull your-registry.com/s3-mcp-server:latest
```

#### 2. Container Won't Start
```bash
# Check logs
kubectl logs <pod-name> -n s3-mcp-server

# Check environment variables
kubectl exec <pod-name> -n s3-mcp-server -- env

# Test locally
docker run -it --rm s3-mcp-server:latest sh
```

#### 3. Health Check Failures
```bash
# Test health endpoint
kubectl port-forward <pod-name> 3000:3000 -n s3-mcp-server
curl http://localhost:3000/health

# Check AWS credentials
kubectl get secret aws-credentials -n s3-mcp-server -o yaml
```

### **Debug Commands**

```bash
# Get pod shell
kubectl exec -it <pod-name> -n s3-mcp-server -- sh

# Check file system
kubectl exec <pod-name> -n s3-mcp-server -- ls -la /app

# Test S3 connectivity
kubectl exec <pod-name> -n s3-mcp-server -- \
  node -e "console.log(process.env.AWS_ACCESS_KEY_ID)"
```

---

## 🎯 **Complete Example**

Here's a complete example workflow:

```bash
# 1. Build and push image
npm run docker:build -- -r your-registry.com -t v1.0.0 --push

# 2. Update deployment
sed -i 's|image: s3-mcp-server:latest|image: your-registry.com/s3-mcp-server:v1.0.0|' k8s/http-deployment.yaml

# 3. Create secrets
kubectl create namespace s3-mcp-server
kubectl create secret generic aws-credentials \
  --from-literal=access-key-id=$AWS_ACCESS_KEY_ID \
  --from-literal=secret-access-key=$AWS_SECRET_ACCESS_KEY \
  --from-literal=bucket-name=$S3_BUCKET_NAME \
  --namespace=s3-mcp-server

# 4. Deploy
kubectl apply -f k8s/http-deployment.yaml

# 5. Get external URL
kubectl get ingress s3-mcp-ingress -n s3-mcp-server

# 6. Test deployment
curl https://your-domain.com/health
```

Your S3 MCP Server is now running in Kubernetes with HTTP bridge support! 🎉

For AI agent integration, see [AI Agent Integration Guide](AI_AGENT_INTEGRATION.md).
