# AI Agent Integration Guide

This guide shows you exactly how to integrate the S3 MCP Server with your AI agent using the HTTP Bridge for Kubernetes deployment.

## 🎯 **Complete Setup for Your Use Case**

### **Scenario**: 
- S3 MCP Server deployed on Kubernetes cluster
- AI agent running locally in your IDE
- Server accessible via internet URL

---

## 🚀 **Step 1: Deploy to Kubernetes**

### 1.1 Create AWS Credentials Secret
```bash
kubectl create namespace s3-mcp-server

kubectl create secret generic aws-credentials \
  --from-literal=access-key-id=YOUR_AWS_ACCESS_KEY \
  --from-literal=secret-access-key=YOUR_AWS_SECRET_KEY \
  --from-literal=bucket-name=your-s3-bucket-name \
  --namespace=s3-mcp-server
```

### 1.2 Deploy the HTTP Bridge
```bash
kubectl apply -f k8s/http-deployment.yaml
```

### 1.3 Get External URL
```bash
# If using LoadBalancer
kubectl get service s3-mcp-service -n s3-mcp-server

# If using Ingress (update your-domain.com in the YAML first)
kube<PERSON>l get ingress s3-mcp-ingress -n s3-mcp-server
```

Your server will be available at: `http://your-external-ip` or `https://your-domain.com`

---

## 🔌 **Step 2: Configure Your AI Agent**

### **Option A: Claude Desktop Integration**

Add to your Claude Desktop config file:

**macOS**: `~/Library/Application Support/Claude/claude_desktop_config.json`
**Windows**: `%APPDATA%/Claude/claude_desktop_config.json`

```json
{
  "mcpServers": {
    "s3-mcp-server": {
      "command": "node",
      "args": ["/path/to/s3-mcp-server/clients/mcp-http-client.js"],
      "env": {
        "MCP_SERVER_URL": "https://your-domain.com",
        "DEBUG": "false"
      }
    }
  }
}
```

### **Option B: Custom AI Agent Integration**

```typescript
import { Client } from '@modelcontextprotocol/sdk/client/index.js';
import { StdioClientTransport } from '@modelcontextprotocol/sdk/client/stdio.js';

class AIAgentWithS3 {
  private mcpClient: Client;

  async initialize() {
    const transport = new StdioClientTransport({
      command: 'node',
      args: ['/path/to/s3-mcp-server/clients/mcp-http-client.js'],
      env: {
        MCP_SERVER_URL: 'https://your-domain.com',
        REQUEST_TIMEOUT: '30000',
        DEBUG: 'false'
      }
    });

    this.mcpClient = new Client({
      name: "kubernetes-debugging-agent",
      version: "1.0.0"
    }, {
      capabilities: {}
    });

    await this.mcpClient.connect(transport);
  }

  async debugKubernetesPod(userQuery: string): Promise<string> {
    // Your AI generates response
    const aiResponse = await this.generateKubernetesAnalysis(userQuery);
    
    // Automatically capture in S3 with namespace detection
    await this.mcpClient.callTool({
      name: "capture_ai_response",
      arguments: {
        content: aiResponse,
        conversationId: this.getSessionId(),
        requestType: "kubernetes_debugging",
        metadata: {
          userQuery: userQuery,
          timestamp: new Date().toISOString(),
          severity: this.detectSeverity(aiResponse)
        }
      }
    });
    
    return aiResponse;
  }
}
```

---

## 📋 **Step 3: Test the Integration**

### 3.1 Test Server Health
```bash
curl https://your-domain.com/health
```

### 3.2 Test MCP Protocol
```bash
curl -X POST -H "Content-Type: application/json" \
  -d '{"jsonrpc":"2.0","id":1,"method":"tools/list"}' \
  https://your-domain.com/mcp
```

### 3.3 Test Tool Call
```bash
curl -X POST -H "Content-Type: application/json" \
  -d '{
    "jsonrpc": "2.0",
    "id": 2,
    "method": "tools/call",
    "params": {
      "name": "capture_ai_response",
      "arguments": {
        "content": "kubectl logs -n production web-server-12345 shows connection errors",
        "conversationId": "test-123",
        "requestType": "pod_troubleshooting"
      }
    }
  }' \
  https://your-domain.com/mcp
```

---

## 🎯 **Real-World Usage Examples**

### Example 1: Kubernetes Pod Debugging
```typescript
// User asks: "Why is my production pod failing?"
const userQuery = "kubectl logs -n production web-server-12345";

const aiResponse = `
## Pod Analysis: web-server-12345 (production namespace)

### Issues Found:
1. **Database Connection Failure**
   - Error: "Connection refused to postgres:5432"
   - Root cause: Database service is down

2. **Memory Pressure**
   - Current usage: 512MB/512MB (100%)
   - Recommendation: Increase memory limits

### Recommended Actions:
1. Check database service: \`kubectl get svc -n production\`
2. Increase memory: Update deployment with higher limits
3. Check network policies
`;

// This gets automatically stored in S3 as:
// s3://your-bucket/production/2024/06/24/production_20240624_143022_abc123.txt
```

### Example 2: Multi-Namespace Analysis
```typescript
const aiResponse = `
## Cross-Environment Pod Status

### Production (Namespace: production)
- web-server: 3/3 running ✅
- api-server: 2/3 running ⚠️ (1 CrashLoopBackOff)
- database: 1/1 running ✅

### Staging (Namespace: staging)  
- web-server: 2/2 running ✅
- api-server: 2/2 running ✅
- database: 1/1 running ✅

### Issues:
- Production api-server needs investigation
`;

// Automatically detects "production" as primary namespace
// Stored in: s3://your-bucket/production/2024/06/24/...
```

---

## 🔧 **Configuration Options**

### Environment Variables for HTTP Client
```bash
# Required
export MCP_SERVER_URL="https://your-domain.com"

# Optional
export REQUEST_TIMEOUT="30000"  # 30 seconds
export DEBUG="true"             # Enable debug logging
```

### Server Configuration
The server automatically:
- ✅ **Detects namespaces** from kubectl commands and log paths
- ✅ **Organizes files** by namespace and date
- ✅ **Handles unlimited namespaces** (production, staging, team-*, customer-*, etc.)
- ✅ **Provides rich metadata** (pod names, error patterns, metrics)
- ✅ **Scales automatically** with Kubernetes HPA

---

## 📊 **Monitoring Your Integration**

### Health Monitoring
```bash
# Check server health
curl https://your-domain.com/health/detailed

# Get metrics
curl https://your-domain.com/metrics

# View configuration
curl https://your-domain.com/config
```

### S3 Organization
Your debugging sessions will be organized as:
```
s3://your-bucket/
├── production/
│   └── 2024/06/24/
│       ├── production_20240624_143022_abc123.txt
│       └── production_20240624_144155_def456.txt
├── staging/
│   └── 2024/06/24/
│       └── staging_20240624_145030_ghi789.txt
├── team-frontend/
│   └── 2024/06/24/
│       └── team-frontend_20240624_150000_jkl012.txt
└── NO_NAMESPACE/
    └── 2024/06/24/
        └── NO_NAMESPACE_20240624_151000_mno345.txt
```

---

## 🚨 **Troubleshooting**

### Common Issues

**1. Connection Refused**
```bash
# Check if server is running
kubectl get pods -n s3-mcp-server

# Check service
kubectl get svc -n s3-mcp-server

# Check ingress
kubectl get ingress -n s3-mcp-server
```

**2. Authentication Errors**
```bash
# Verify AWS credentials
kubectl get secret aws-credentials -n s3-mcp-server -o yaml

# Test S3 access
kubectl exec -n s3-mcp-server deployment/s3-mcp-server -- \
  aws s3 ls s3://your-bucket-name
```

**3. MCP Client Issues**
```bash
# Test HTTP client directly
echo '{"jsonrpc":"2.0","id":1,"method":"tools/list"}' | \
  MCP_SERVER_URL=https://your-domain.com \
  node /path/to/clients/mcp-http-client.js
```

### Debug Mode
```bash
# Enable debug logging
export DEBUG=true

# Or in Kubernetes
kubectl set env deployment/s3-mcp-server LOG_LEVEL=debug -n s3-mcp-server
```

---

## 🎉 **You're Ready!**

Your AI agent can now:
- 🔍 **Debug Kubernetes pods** across unlimited namespaces
- 📁 **Automatically organize** debugging sessions in S3
- 🔄 **Scale seamlessly** with your Kubernetes cluster
- 📊 **Track patterns** and build a knowledge base
- 🌐 **Access remotely** via internet URL

The S3 MCP Server will automatically detect namespaces from your kubectl commands and organize everything perfectly for your debugging workflow!

For more advanced configuration, see:
- [Configuration Guide](CONFIGURATION.md)
- [API Documentation](API.md)
- [Troubleshooting Guide](TROUBLESHOOTING.md)
