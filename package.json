{"name": "s3-mcp-server", "version": "1.0.0", "description": "MCP server that captures AI agent responses and stores them in S3 with Kubernetes namespace organization", "main": "dist/server.js", "type": "module", "scripts": {"build": "tsc", "start": "node dist/server.js", "dev": "ts-node src/server.ts", "test": "jest", "test:watch": "jest --watch", "lint": "eslint src/**/*.ts", "clean": "rm -rf dist", "config": "tsc && node dist/src/config/cli.js", "config:validate": "npm run config validate", "config:show": "npm run config show", "config:env": "npm run config env", "config:test": "npm run config test"}, "keywords": ["mcp", "model-context-protocol", "s3", "kubernetes", "ai-agent", "debugging"], "author": "Your Name", "license": "MIT", "dependencies": {"@modelcontextprotocol/sdk": "^0.5.0", "@aws-sdk/client-s3": "^3.600.0", "uuid": "^10.0.0", "dotenv": "^16.4.5", "winston": "^3.13.0", "express": "^4.19.2"}, "devDependencies": {"@types/node": "^20.14.0", "@types/uuid": "^10.0.0", "@types/jest": "^29.5.12", "@types/express": "^4.17.21", "@typescript-eslint/eslint-plugin": "^7.13.0", "@typescript-eslint/parser": "^7.13.0", "eslint": "^8.57.0", "jest": "^29.7.0", "ts-jest": "^29.1.5", "ts-node": "^10.9.2", "typescript": "^5.5.2"}, "engines": {"node": ">=18.0.0"}}