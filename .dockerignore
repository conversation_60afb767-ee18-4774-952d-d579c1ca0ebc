# Node.js
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Build outputs (will be built inside container)
dist/

# Development files
.env*
!.env.example

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# OS files
.DS_Store
Thumbs.db

# Git
.git/
.gitignore

# Documentation (not needed in container)
docs/
*.md
!package*.json

# Test files
coverage/
.nyc_output/
test/
tests/
__tests__/

# Logs
logs/
*.log

# Runtime data
pids/
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/

# Dependency directories
jspm_packages/

# Optional npm cache directory
.npm

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# Docker files (not needed inside container)
Dockerfile*
.dockerignore
docker-compose*.yml

# Kubernetes files (not needed inside container)
k8s/
*.yaml
*.yml

# Scripts (not needed inside container)
scripts/

# Examples (not needed in production)
examples/
