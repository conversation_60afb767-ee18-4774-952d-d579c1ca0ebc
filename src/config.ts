/**
 * Configuration management for the S3 MCP Server
 */

import dotenv from 'dotenv';
import { ServerConfig } from './types.js';

// Load environment variables
dotenv.config();

/**
 * Validates required environment variables
 */
function validateConfig(): void {
  const required = [
    'AWS_ACCESS_KEY_ID',
    'AWS_SECRET_ACCESS_KEY',
    'AWS_REGION',
    'S3_BUCKET_NAME'
  ];

  const missing = required.filter(key => !process.env[key]);
  
  if (missing.length > 0) {
    throw new Error(`Missing required environment variables: ${missing.join(', ')}`);
  }
}

/**
 * Parses comma-separated string into array
 */
function parseStringArray(value: string | undefined, defaultValue: string[] = []): string[] {
  if (!value) return defaultValue;
  return value.split(',').map(item => item.trim()).filter(item => item.length > 0);
}

/**
 * Gets the server configuration from environment variables
 */
export function getConfig(): ServerConfig {
  validateConfig();

  return {
    aws: {
      accessKeyId: process.env['AWS_ACCESS_KEY_ID']!,
      secretAccessKey: process.env['AWS_SECRET_ACCESS_KEY']!,
      region: process.env['AWS_REGION']!,
      bucketName: process.env['S3_BUCKET_NAME']!,
    },
    mcp: {
      port: parseInt(process.env['MCP_SERVER_PORT'] || '3000', 10),
      serverName: process.env['MCP_SERVER_NAME'] || 's3-mcp-server',
    },
    kubernetes: {
      defaultNamespacePrefix: process.env['DEFAULT_NAMESPACE_PREFIX'] || 'NO_NAMESPACE',
      validationEnabled: process.env['NAMESPACE_VALIDATION_ENABLED'] === 'true',
      allowedNamespaces: parseStringArray(
        process.env['ALLOWED_NAMESPACES'],
        ['production', 'staging', 'development', 'kube-system']
      ),
    },
    storage: {
      retentionDays: parseInt(process.env['FILE_RETENTION_DAYS'] || '30', 10),
      maxFileSizeMB: parseInt(process.env['MAX_FILE_SIZE_MB'] || '10', 10),
      enableCompression: process.env['ENABLE_COMPRESSION'] === 'true',
      basePath: process.env['S3_BASE_PATH'] || 'mcp-responses',
      enableDateFolders: process.env['ENABLE_DATE_FOLDERS'] !== 'false',
      enableNamespaceFolders: process.env['ENABLE_NAMESPACE_FOLDERS'] !== 'false',
    },
    logging: {
      level: process.env['LOG_LEVEL'] || 'info',
      filePath: process.env['LOG_FILE_PATH'] || './logs/mcp-server.log',
      enableConsoleLogging: process.env['ENABLE_CONSOLE_LOGGING'] !== 'false',
    },
  };
}
