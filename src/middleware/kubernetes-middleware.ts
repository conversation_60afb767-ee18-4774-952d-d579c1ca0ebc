/**
 * Kubernetes-specific middleware for AI response processing
 */

import { AIResponse } from '../types.js';
import { InterceptorContext, InterceptorMiddleware } from '../interceptor.js';
import logger from '../logger.js';

/**
 * Middleware to detect and extract Kubernetes resource information
 */
export class KubernetesResourceMiddleware implements InterceptorMiddleware {
  name = 'kubernetes-resource-detection';
  priority = 15;

  async process(response: AIResponse, _context: InterceptorContext): Promise<AIResponse> {
    const resources = this.extractKubernetesResources(response.content);
    
    if (resources.length > 0) {
      response.metadata = {
        ...response.metadata,
        kubernetesResources: resources,
      };

      logger.debug('Detected Kubernetes resources', {
        responseId: response.id,
        resourceCount: resources.length,
        resources: resources.map(r => `${r.kind}/${r.name}`),
      });
    }

    return response;
  }

  private extractKubernetesResources(content: string): Array<{
    kind: string;
    name: string;
    namespace?: string;
  }> {
    const resources: Array<{ kind: string; name: string; namespace?: string }> = [];

    // Common Kubernetes resource patterns
    const resourcePatterns = [
      // kubectl get output: pod/name, service/name, etc.
      /(\w+)\/([a-z0-9\-]+)/gi,
      // Resource references: Pod my-pod-123
      /(Pod|Service|Deployment|StatefulSet|DaemonSet|Job|CronJob|ConfigMap|Secret|PersistentVolumeClaim|Ingress)\s+([a-z0-9\-]+)/gi,
      // YAML-style references
      /kind:\s*(\w+)\s*.*?name:\s*([a-z0-9\-]+)/gis,
    ];

    for (const pattern of resourcePatterns) {
      const matches = content.matchAll(pattern);
      for (const match of matches) {
        const kind = match[1];
        const name = match[2];

        if (kind && name && this.isValidKubernetesResource(kind)) {
          resources.push({ kind, name });
        }
      }
    }

    // Remove duplicates
    const uniqueResources = resources.filter((resource, index, self) =>
      index === self.findIndex(r => r.kind === resource.kind && r.name === resource.name)
    );

    return uniqueResources;
  }

  private isValidKubernetesResource(kind: string): boolean {
    const validKinds = [
      'Pod', 'Service', 'Deployment', 'StatefulSet', 'DaemonSet',
      'Job', 'CronJob', 'ConfigMap', 'Secret', 'PersistentVolumeClaim',
      'Ingress', 'NetworkPolicy', 'ServiceAccount', 'Role', 'RoleBinding',
      'ClusterRole', 'ClusterRoleBinding', 'Node', 'Namespace',
      'pod', 'service', 'deployment', 'statefulset', 'daemonset',
      'job', 'cronjob', 'configmap', 'secret', 'pvc', 'ingress',
    ];
    
    return validKinds.includes(kind);
  }
}

/**
 * Middleware to detect error patterns in Kubernetes logs
 */
export class KubernetesErrorMiddleware implements InterceptorMiddleware {
  name = 'kubernetes-error-detection';
  priority = 25;

  async process(response: AIResponse, _context: InterceptorContext): Promise<AIResponse> {
    const errors = this.extractErrorPatterns(response.content);
    
    if (errors.length > 0) {
      response.metadata = {
        ...response.metadata,
        kubernetesErrors: errors,
        hasErrors: true,
      };

      logger.info('Detected Kubernetes errors in response', {
        responseId: response.id,
        errorCount: errors.length,
        errorTypes: [...new Set(errors.map(e => e.type))],
      });
    }

    return response;
  }

  private extractErrorPatterns(content: string): Array<{
    type: string;
    message: string;
    severity: 'low' | 'medium' | 'high';
  }> {
    const errors: Array<{ type: string; message: string; severity: 'low' | 'medium' | 'high' }> = [];

    const errorPatterns = [
      // Pod failures
      { pattern: /CrashLoopBackOff/gi, type: 'CrashLoopBackOff', severity: 'high' as const },
      { pattern: /ImagePullBackOff/gi, type: 'ImagePullBackOff', severity: 'high' as const },
      { pattern: /ErrImagePull/gi, type: 'ErrImagePull', severity: 'high' as const },
      { pattern: /OOMKilled/gi, type: 'OOMKilled', severity: 'high' as const },
      { pattern: /Evicted/gi, type: 'Evicted', severity: 'medium' as const },
      
      // Resource issues
      { pattern: /Insufficient (cpu|memory|storage)/gi, type: 'InsufficientResources', severity: 'high' as const },
      { pattern: /Failed to pull image/gi, type: 'ImagePullFailed', severity: 'high' as const },
      { pattern: /Failed to mount volume/gi, type: 'VolumeMountFailed', severity: 'high' as const },
      
      // Network issues
      { pattern: /Connection refused/gi, type: 'ConnectionRefused', severity: 'medium' as const },
      { pattern: /Timeout/gi, type: 'Timeout', severity: 'medium' as const },
      { pattern: /DNS resolution failed/gi, type: 'DNSResolutionFailed', severity: 'medium' as const },
      
      // General errors
      { pattern: /Error:/gi, type: 'GeneralError', severity: 'low' as const },
      { pattern: /Failed:/gi, type: 'GeneralFailure', severity: 'low' as const },
      { pattern: /Exception:/gi, type: 'Exception', severity: 'medium' as const },
    ];

    for (const { pattern, type, severity } of errorPatterns) {
      const matches = content.matchAll(pattern);
      for (const match of matches) {
        // Extract surrounding context for the error message
        const matchIndex = match.index || 0;
        const start = Math.max(0, matchIndex - 50);
        const end = Math.min(content.length, matchIndex + match[0].length + 100);
        const message = content.substring(start, end).trim();
        
        errors.push({ type, message, severity });
      }
    }

    return errors;
  }
}

/**
 * Middleware to extract performance metrics from Kubernetes content
 */
export class KubernetesMetricsMiddleware implements InterceptorMiddleware {
  name = 'kubernetes-metrics-extraction';
  priority = 35;

  async process(response: AIResponse, _context: InterceptorContext): Promise<AIResponse> {
    const metrics = this.extractMetrics(response.content);
    
    if (Object.keys(metrics).length > 0) {
      response.metadata = {
        ...response.metadata,
        kubernetesMetrics: metrics,
      };

      logger.debug('Extracted Kubernetes metrics', {
        responseId: response.id,
        metricTypes: Object.keys(metrics),
      });
    }

    return response;
  }

  private extractMetrics(content: string): Record<string, any> {
    const metrics: Record<string, any> = {};

    // CPU and Memory usage patterns
    const cpuPattern = /(\d+(?:\.\d+)?)\s*(m?cores?|cpu)/gi;
    const memoryPattern = /(\d+(?:\.\d+)?)\s*(Mi|Gi|Ki|MB|GB|KB)/gi;
    const percentagePattern = /(\d+(?:\.\d+)?)%/gi;

    // Extract CPU metrics
    const cpuMatches = Array.from(content.matchAll(cpuPattern));
    if (cpuMatches.length > 0) {
      metrics['cpu'] = cpuMatches.map(match => ({
        value: match[1] ? parseFloat(match[1]) : 0,
        unit: match[2] || '',
      }));
    }

    // Extract memory metrics
    const memoryMatches = Array.from(content.matchAll(memoryPattern));
    if (memoryMatches.length > 0) {
      metrics['memory'] = memoryMatches.map(match => ({
        value: match[1] ? parseFloat(match[1]) : 0,
        unit: match[2] || '',
      }));
    }

    // Extract percentage metrics
    const percentageMatches = Array.from(content.matchAll(percentagePattern));
    if (percentageMatches.length > 0) {
      metrics['percentages'] = percentageMatches.map(match =>
        match[1] ? parseFloat(match[1]) : 0
      );
    }

    return metrics;
  }
}
