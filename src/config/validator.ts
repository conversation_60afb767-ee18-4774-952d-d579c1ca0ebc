/**
 * Configuration validation utilities
 */

import { ConfigSchema, ConfigValidationRule } from './schema.js';

export interface ValidationError {
  path: string;
  message: string;
  value?: any;
}

export interface ValidationResult {
  isValid: boolean;
  errors: ValidationError[];
  warnings: ValidationError[];
}

/**
 * Validates a configuration object against a schema
 */
export function validateConfig(config: any, schema: ConfigSchema, path = ''): ValidationResult {
  const errors: ValidationError[] = [];
  const warnings: ValidationError[] = [];

  function addError(subPath: string, message: string, value?: any) {
    errors.push({
      path: path ? `${path}.${subPath}` : subPath,
      message,
      value,
    });
  }

  function addWarning(subPath: string, message: string, value?: any) {
    warnings.push({
      path: path ? `${path}.${subPath}` : subPath,
      message,
      value,
    });
  }

  function validateValue(key: string, value: any, rule: ConfigValidationRule) {

    // Check if required
    if (rule.required && (value === undefined || value === null || value === '')) {
      addError(key, 'Required field is missing');
      return;
    }

    // Skip validation if value is undefined and not required
    if (value === undefined || value === null) {
      return;
    }

    // Type validation
    if (rule.type) {
      const actualType = rule.type === 'array' ? 'array' : typeof value;
      const expectedType = rule.type === 'array' ? 'array' : rule.type;
      
      if (rule.type === 'array' && !Array.isArray(value)) {
        addError(key, `Expected array, got ${typeof value}`, value);
        return;
      } else if (rule.type !== 'array' && typeof value !== rule.type) {
        addError(key, `Expected ${expectedType}, got ${actualType}`, value);
        return;
      }
    }

    // String validations
    if (typeof value === 'string') {
      if (rule.min !== undefined && value.length < rule.min) {
        addError(key, `String too short (min: ${rule.min}, actual: ${value.length})`, value);
      }
      if (rule.max !== undefined && value.length > rule.max) {
        addError(key, `String too long (max: ${rule.max}, actual: ${value.length})`, value);
      }
      if (rule.pattern && !rule.pattern.test(value)) {
        addError(key, `String does not match required pattern`, value);
      }
      if (rule.enum && !rule.enum.includes(value)) {
        addError(key, `Value not in allowed list: [${rule.enum.join(', ')}]`, value);
      }
    }

    // Number validations
    if (typeof value === 'number') {
      if (rule.min !== undefined && value < rule.min) {
        addError(key, `Number too small (min: ${rule.min}, actual: ${value})`, value);
      }
      if (rule.max !== undefined && value > rule.max) {
        addError(key, `Number too large (max: ${rule.max}, actual: ${value})`, value);
      }
    }

    // Custom validator
    if (rule.validator) {
      const result = rule.validator(value);
      if (result !== true) {
        addError(key, typeof result === 'string' ? result : 'Custom validation failed', value);
      }
    }
  }

  // Validate each property in the schema
  for (const [key, rule] of Object.entries(schema)) {
    const value = config[key];

    if (isValidationRule(rule)) {
      validateValue(key, value, rule);
    } else {
      // Nested object validation
      if (value && typeof value === 'object' && !Array.isArray(value)) {
        const nestedResult = validateConfig(value, rule, path ? `${path}.${key}` : key);
        errors.push(...nestedResult.errors);
        warnings.push(...nestedResult.warnings);
      } else if (value !== undefined) {
        addError(key, 'Expected object for nested configuration', value);
      }
    }
  }

  // Check for unknown properties (warnings)
  if (config && typeof config === 'object') {
    for (const key of Object.keys(config)) {
      if (!(key in schema)) {
        addWarning(key, 'Unknown configuration property', config[key]);
      }
    }
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings,
  };
}

/**
 * Type guard to check if a rule is a validation rule or nested schema
 */
function isValidationRule(rule: ConfigValidationRule | ConfigSchema): rule is ConfigValidationRule {
  return (
    'required' in rule ||
    'type' in rule ||
    'min' in rule ||
    'max' in rule ||
    'pattern' in rule ||
    'enum' in rule ||
    'validator' in rule
  );
}

/**
 * Formats validation errors for display
 */
export function formatValidationErrors(result: ValidationResult): string {
  const lines: string[] = [];

  if (result.errors.length > 0) {
    lines.push('Configuration Errors:');
    for (const error of result.errors) {
      lines.push(`  ❌ ${error.path}: ${error.message}`);
      if (error.value !== undefined) {
        lines.push(`     Current value: ${JSON.stringify(error.value)}`);
      }
    }
  }

  if (result.warnings.length > 0) {
    if (lines.length > 0) lines.push('');
    lines.push('Configuration Warnings:');
    for (const warning of result.warnings) {
      lines.push(`  ⚠️  ${warning.path}: ${warning.message}`);
      if (warning.value !== undefined) {
        lines.push(`     Current value: ${JSON.stringify(warning.value)}`);
      }
    }
  }

  return lines.join('\n');
}

/**
 * Validates environment variables before parsing
 */
export function validateEnvironmentVariables(): ValidationResult {
  const errors: ValidationError[] = [];
  const warnings: ValidationError[] = [];

  // Check for sensitive data in environment
  const sensitivePatterns = [
    { pattern: /password/i, message: 'Avoid using "password" in environment variable names' },
    { pattern: /secret/i, message: 'Consider using secure secret management for sensitive data' },
    { pattern: /key/i, message: 'Consider using secure key management for sensitive data' },
  ];

  for (const [key, value] of Object.entries(process.env)) {
    if (key.startsWith('AWS_') || key.startsWith('MCP_') || key.startsWith('S3_')) {
      // Check for empty values
      if (!value || value.trim() === '') {
        warnings.push({
          path: key,
          message: 'Environment variable is empty',
          value: value,
        });
      }

      // Check for sensitive patterns
      for (const { pattern, message } of sensitivePatterns) {
        if (pattern.test(key)) {
          warnings.push({
            path: key,
            message,
            value: '[REDACTED]',
          });
        }
      }

      // Check for development/test values in production
      if (process.env['NODE_ENV'] === 'production') {
        const testPatterns = [
          /test/i,
          /dev/i,
          /local/i,
          /example/i,
          /demo/i,
        ];

        for (const pattern of testPatterns) {
          if (value && pattern.test(value)) {
            warnings.push({
              path: key,
              message: 'Possible test/development value in production environment',
              value: '[REDACTED]',
            });
          }
        }
      }
    }
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings,
  };
}
