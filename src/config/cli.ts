#!/usr/bin/env node
/**
 * Configuration management CLI tool
 */

import { getConfigAsync, getMaskedConfig, clearConfigCache } from '../config.js';
import { validateEnvironmentVariables, formatValidationErrors } from './validator.js';
import { getCurrentEnvironment, validateEnvironmentRequirements } from './environments.js';
import { loadSecrets, getSecretSourcesForEnvironment } from './secrets.js';

interface CLICommand {
  name: string;
  description: string;
  handler: (args: string[]) => Promise<void>;
}

const commands: CLICommand[] = [
  {
    name: 'validate',
    description: 'Validate current configuration',
    handler: validateCommand,
  },
  {
    name: 'show',
    description: 'Show current configuration (masked)',
    handler: showCommand,
  },
  {
    name: 'env',
    description: 'Show environment information',
    handler: envCommand,
  },
  {
    name: 'secrets',
    description: 'Test secret loading',
    handler: secretsCommand,
  },
  {
    name: 'test',
    description: 'Test configuration loading',
    handler: testCommand,
  },
];

async function validateCommand(_args: string[]): Promise<void> {
  console.log('🔍 Validating configuration...\n');
  
  try {
    // Validate environment variables
    console.log('1. Validating environment variables...');
    const envValidation = validateEnvironmentVariables();
    
    if (envValidation.errors.length > 0) {
      console.error('❌ Environment validation failed:');
      console.error(formatValidationErrors(envValidation));
      return;
    }
    
    if (envValidation.warnings.length > 0) {
      console.warn('⚠️  Environment validation warnings:');
      console.warn(formatValidationErrors(envValidation));
    } else {
      console.log('✅ Environment variables validation passed');
    }
    
    // Validate environment requirements
    console.log('\n2. Validating environment requirements...');
    const environment = getCurrentEnvironment();
    const envRequirements = validateEnvironmentRequirements(environment);
    
    if (!envRequirements.isValid) {
      console.error(`❌ Missing required variables for ${environment}:`);
      for (const missing of envRequirements.missing) {
        console.error(`   - ${missing}`);
      }
      return;
    }
    
    console.log(`✅ Environment requirements for ${environment} satisfied`);
    
    // Validate full configuration
    console.log('\n3. Validating full configuration...');
    await getConfigAsync();
    console.log('✅ Configuration loaded and validated successfully');
    
    console.log('\n🎉 All validation checks passed!');
    
  } catch (error) {
    console.error('❌ Configuration validation failed:');
    console.error(error instanceof Error ? error.message : String(error));
    process.exit(1);
  }
}

async function showCommand(_args: string[]): Promise<void> {
  console.log('📋 Current configuration (sensitive values masked):\n');
  
  try {
    const maskedConfig = getMaskedConfig();
    console.log(JSON.stringify(maskedConfig, null, 2));
  } catch (error) {
    console.error('❌ Failed to load configuration:');
    console.error(error instanceof Error ? error.message : String(error));
    process.exit(1);
  }
}

async function envCommand(_args: string[]): Promise<void> {
  console.log('🌍 Environment information:\n');
  
  const environment = getCurrentEnvironment();
  console.log(`Current environment: ${environment}`);
  console.log(`NODE_ENV: ${process.env['NODE_ENV'] || 'undefined'}`);
  
  const envRequirements = validateEnvironmentRequirements(environment);
  console.log(`\nRequired variables for ${environment}:`);
  for (const variable of envRequirements.missing) {
    console.log(`  ❌ ${variable} (missing)`);
  }
  
  for (const variable of envRequirements.missing.length === 0 ? 
    ['AWS_ACCESS_KEY_ID', 'AWS_SECRET_ACCESS_KEY', 'AWS_REGION', 'S3_BUCKET_NAME'] : []) {
    if (process.env[variable]) {
      console.log(`  ✅ ${variable} (set)`);
    }
  }
  
  if (envRequirements.warnings.length > 0) {
    console.log('\nEnvironment warnings:');
    for (const warning of envRequirements.warnings) {
      console.log(`  ⚠️  ${warning}`);
    }
  }
}

async function secretsCommand(_args: string[]): Promise<void> {
  console.log('🔐 Testing secret loading...\n');
  
  try {
    const environment = getCurrentEnvironment();
    const secretSources = getSecretSourcesForEnvironment(environment);
    
    console.log(`Secret sources for ${environment}:`);
    for (const [key, source] of Object.entries(secretSources)) {
      console.log(`  ${key}: ${source.type} (${source.key || source.path || 'default'})`);
    }
    
    console.log('\nLoading secrets...');
    const secrets = await loadSecrets(secretSources);
    
    console.log('Loaded secrets:');
    for (const [key, value] of Object.entries(secrets)) {
      const maskedValue = value.length > 4 ? 
        value.substring(0, 2) + '*'.repeat(value.length - 4) + value.substring(value.length - 2) :
        '*'.repeat(value.length);
      console.log(`  ✅ ${key}: ${maskedValue}`);
    }
    
    if (Object.keys(secrets).length === 0) {
      console.log('  ⚠️  No secrets loaded (using environment variables)');
    }
    
  } catch (error) {
    console.error('❌ Failed to load secrets:');
    console.error(error instanceof Error ? error.message : String(error));
    process.exit(1);
  }
}

async function testCommand(_args: string[]): Promise<void> {
  console.log('🧪 Testing configuration loading...\n');
  
  try {
    console.log('1. Clearing configuration cache...');
    clearConfigCache();
    console.log('✅ Cache cleared');
    
    console.log('\n2. Loading configuration...');
    const startTime = Date.now();
    const config = await getConfigAsync();
    const loadTime = Date.now() - startTime;
    
    console.log(`✅ Configuration loaded in ${loadTime}ms`);
    
    console.log('\n3. Configuration summary:');
    console.log(`   Environment: ${getCurrentEnvironment()}`);
    console.log(`   AWS Region: ${config.aws.region}`);
    console.log(`   S3 Bucket: ${config.aws.bucketName}`);
    console.log(`   MCP Server: ${config.mcp.serverName}:${config.mcp.port}`);
    console.log(`   Log Level: ${config.logging.level}`);
    
    console.log('\n🎉 Configuration test completed successfully!');
    
  } catch (error) {
    console.error('❌ Configuration test failed:');
    console.error(error instanceof Error ? error.message : String(error));
    process.exit(1);
  }
}

function showHelp(): void {
  console.log('S3 MCP Server Configuration CLI\n');
  console.log('Usage: npm run config <command>\n');
  console.log('Commands:');
  
  for (const command of commands) {
    console.log(`  ${command.name.padEnd(12)} ${command.description}`);
  }
  
  console.log('\nExamples:');
  console.log('  npm run config validate    # Validate current configuration');
  console.log('  npm run config show        # Show current configuration');
  console.log('  npm run config env         # Show environment information');
  console.log('  npm run config secrets     # Test secret loading');
  console.log('  npm run config test        # Test configuration loading');
}

async function main(): Promise<void> {
  const args = process.argv.slice(2);
  
  if (args.length === 0 || args[0] === 'help' || args[0] === '--help' || args[0] === '-h') {
    showHelp();
    return;
  }
  
  const commandName = args[0];
  const command = commands.find(cmd => cmd.name === commandName);
  
  if (!command) {
    console.error(`❌ Unknown command: ${commandName}`);
    console.error('Run "npm run config help" for available commands');
    process.exit(1);
  }
  
  try {
    await command.handler(args.slice(1));
  } catch (error) {
    console.error('❌ Command failed:');
    console.error(error instanceof Error ? error.message : String(error));
    process.exit(1);
  }
}

// Run CLI if this file is executed directly
main().catch(error => {
  console.error('❌ CLI failed:');
  console.error(error);
  process.exit(1);
});
