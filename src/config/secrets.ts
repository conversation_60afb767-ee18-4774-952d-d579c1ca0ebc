/**
 * Secrets management and secure configuration handling
 */

import { readFileSync, existsSync } from 'fs';
import { join } from 'path';

export interface SecretSource {
  type: 'env' | 'file' | 'aws-secrets-manager' | 'kubernetes-secret';
  path?: string;
  key?: string;
  region?: string;
}

export interface SecretConfig {
  [key: string]: SecretSource;
}

/**
 * Default secret sources configuration
 */
export const defaultSecretSources: SecretConfig = {
  'aws.accessKeyId': {
    type: 'env',
    key: 'AWS_ACCESS_KEY_ID',
  },
  'aws.secretAccessKey': {
    type: 'env',
    key: 'AWS_SECRET_ACCESS_KEY',
  },
  'aws.region': {
    type: 'env',
    key: 'AWS_REGION',
  },
  'aws.bucketName': {
    type: 'env',
    key: 'S3_BUCKET_NAME',
  },
};

/**
 * Retrieves a secret value from the specified source
 */
export async function getSecret(source: SecretSource): Promise<string | undefined> {
  switch (source.type) {
    case 'env':
      return getEnvironmentSecret(source);
    
    case 'file':
      return getFileSecret(source);
    
    case 'aws-secrets-manager':
      return getAWSSecret(source);
    
    case 'kubernetes-secret':
      return getKubernetesSecret(source);
    
    default:
      throw new Error(`Unsupported secret source type: ${(source as any).type}`);
  }
}

/**
 * Gets secret from environment variable
 */
function getEnvironmentSecret(source: SecretSource): string | undefined {
  if (!source.key) {
    throw new Error('Environment secret source requires a key');
  }
  
  return process.env[source.key];
}

/**
 * Gets secret from file system
 */
function getFileSecret(source: SecretSource): string | undefined {
  if (!source.path) {
    throw new Error('File secret source requires a path');
  }
  
  try {
    if (!existsSync(source.path)) {
      return undefined;
    }
    
    const content = readFileSync(source.path, 'utf-8').trim();
    return content || undefined;
  } catch (error) {
    console.warn(`Failed to read secret from file ${source.path}:`, error);
    return undefined;
  }
}

/**
 * Gets secret from AWS Secrets Manager
 */
async function getAWSSecret(source: SecretSource): Promise<string | undefined> {
  if (!source.key) {
    throw new Error('AWS Secrets Manager source requires a key (secret name)');
  }
  
  try {
    // This would require AWS SDK for Secrets Manager
    // For now, we'll return undefined and log a warning
    console.warn('AWS Secrets Manager integration not implemented yet');
    return undefined;
  } catch (error) {
    console.warn(`Failed to retrieve AWS secret ${source.key}:`, error);
    return undefined;
  }
}

/**
 * Gets secret from Kubernetes secret
 */
async function getKubernetesSecret(source: SecretSource): Promise<string | undefined> {
  if (!source.path) {
    throw new Error('Kubernetes secret source requires a path');
  }
  
  try {
    // In Kubernetes, secrets are mounted as files
    const secretPath = join('/var/run/secrets', source.path);
    
    if (!existsSync(secretPath)) {
      return undefined;
    }
    
    const content = readFileSync(secretPath, 'utf-8').trim();
    return content || undefined;
  } catch (error) {
    console.warn(`Failed to read Kubernetes secret from ${source.path}:`, error);
    return undefined;
  }
}

/**
 * Loads all secrets based on configuration
 */
export async function loadSecrets(secretConfig: SecretConfig = defaultSecretSources): Promise<Record<string, string>> {
  const secrets: Record<string, string> = {};
  
  for (const [configPath, source] of Object.entries(secretConfig)) {
    try {
      const value = await getSecret(source);
      if (value) {
        secrets[configPath] = value;
      }
    } catch (error) {
      console.warn(`Failed to load secret for ${configPath}:`, error);
    }
  }
  
  return secrets;
}

/**
 * Sets configuration values from loaded secrets
 */
export function applySecrets(config: any, secrets: Record<string, string>): any {
  const result = JSON.parse(JSON.stringify(config)); // Deep clone
  
  for (const [path, value] of Object.entries(secrets)) {
    setNestedValue(result, path, value);
  }
  
  return result;
}

/**
 * Sets a nested value in an object using dot notation
 */
function setNestedValue(obj: any, path: string, value: any): void {
  const keys = path.split('.');
  let current = obj;

  for (let i = 0; i < keys.length - 1; i++) {
    const key = keys[i];
    if (!key) continue;

    if (!(key in current) || typeof current[key] !== 'object') {
      current[key] = {};
    }
    current = current[key];
  }

  const lastKey = keys[keys.length - 1];
  if (lastKey) {
    current[lastKey] = value;
  }
}

/**
 * Masks sensitive values in configuration for logging
 */
export function maskSensitiveConfig(config: any): any {
  const masked = JSON.parse(JSON.stringify(config));
  
  const sensitiveKeys = [
    'accessKeyId',
    'secretAccessKey',
    'password',
    'token',
    'key',
    'secret',
    'credential',
  ];
  
  function maskObject(obj: any): void {
    for (const [key, value] of Object.entries(obj)) {
      if (typeof value === 'object' && value !== null) {
        maskObject(value);
      } else if (typeof value === 'string') {
        const lowerKey = key.toLowerCase();
        if (sensitiveKeys.some(sensitive => lowerKey.includes(sensitive))) {
          obj[key] = maskValue(value);
        }
      }
    }
  }
  
  maskObject(masked);
  return masked;
}

/**
 * Masks a sensitive value for display
 */
function maskValue(value: string): string {
  if (value.length <= 4) {
    return '*'.repeat(value.length);
  }
  
  const visibleChars = 2;
  const maskedLength = value.length - (visibleChars * 2);
  
  return value.substring(0, visibleChars) + 
         '*'.repeat(maskedLength) + 
         value.substring(value.length - visibleChars);
}

/**
 * Validates that all required secrets are available
 */
export function validateSecrets(secrets: Record<string, string>, required: string[]): {
  isValid: boolean;
  missing: string[];
} {
  const missing = required.filter(key => !secrets[key]);
  
  return {
    isValid: missing.length === 0,
    missing,
  };
}

/**
 * Gets secret source configuration for different environments
 */
export function getSecretSourcesForEnvironment(environment: string): SecretConfig {
  const base = { ...defaultSecretSources };
  
  switch (environment) {
    case 'production':
      // In production, prefer AWS Secrets Manager or Kubernetes secrets
      return {
        ...base,
        'aws.accessKeyId': {
          type: 'kubernetes-secret',
          path: 'aws-credentials/access-key-id',
        },
        'aws.secretAccessKey': {
          type: 'kubernetes-secret',
          path: 'aws-credentials/secret-access-key',
        },
      };
    
    case 'staging':
      // In staging, use file-based secrets
      return {
        ...base,
        'aws.accessKeyId': {
          type: 'file',
          path: '/etc/secrets/aws-access-key-id',
        },
        'aws.secretAccessKey': {
          type: 'file',
          path: '/etc/secrets/aws-secret-access-key',
        },
      };
    
    default:
      // Development and test use environment variables
      return base;
  }
}
