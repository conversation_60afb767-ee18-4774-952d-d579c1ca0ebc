/**
 * Retry mechanism with exponential backoff and jitter
 */

import logger from '../logger.js';
import { MCPError, ErrorClassifier } from './error-types.js';

export interface RetryOptions {
  maxAttempts: number;
  baseDelay: number;
  maxDelay: number;
  backoffMultiplier: number;
  jitter: boolean;
  retryCondition?: (error: Error) => boolean;
  onRetry?: (error: Error, attempt: number) => void;
}

export interface RetryResult<T> {
  success: boolean;
  result?: T;
  error?: Error | undefined;
  attempts: number;
  totalTime: number;
}

export class RetryManager {
  private static defaultOptions: RetryOptions = {
    maxAttempts: 3,
    baseDelay: 1000,
    maxDelay: 30000,
    backoffMultiplier: 2,
    jitter: true,
    retryCondition: (error: Error) => ErrorClassifier.isRetryable(error),
  };

  /**
   * Execute a function with retry logic
   */
  static async execute<T>(
    operation: () => Promise<T>,
    options: Partial<RetryOptions> = {},
    context?: { operationName?: string; requestId?: string }
  ): Promise<RetryResult<T>> {
    const config = { ...this.defaultOptions, ...options };
    const startTime = Date.now();
    let lastError: Error | undefined;

    logger.debug('Starting retry operation', {
      operationName: context?.operationName,
      requestId: context?.requestId,
      maxAttempts: config.maxAttempts,
      baseDelay: config.baseDelay,
    });

    for (let attempt = 1; attempt <= config.maxAttempts; attempt++) {
      try {
        const result = await operation();
        const totalTime = Date.now() - startTime;

        logger.debug('Retry operation succeeded', {
          operationName: context?.operationName,
          requestId: context?.requestId,
          attempt,
          totalTime,
        });

        return {
          success: true,
          result,
          attempts: attempt,
          totalTime,
        };
      } catch (error) {
        lastError = error instanceof Error ? error : new Error(String(error));
        
        logger.warn('Retry operation failed', {
          operationName: context?.operationName,
          requestId: context?.requestId,
          attempt,
          error: lastError.message,
          isRetryable: config.retryCondition!(lastError),
        });

        // Check if we should retry
        if (attempt === config.maxAttempts || !config.retryCondition!(lastError)) {
          break;
        }

        // Calculate delay with exponential backoff and jitter
        const delay = this.calculateDelay(attempt, config);
        
        logger.debug('Retrying operation after delay', {
          operationName: context?.operationName,
          requestId: context?.requestId,
          attempt,
          delay,
          nextAttempt: attempt + 1,
        });

        // Call retry callback if provided
        if (config.onRetry) {
          config.onRetry(lastError, attempt);
        }

        // Wait before retrying
        await this.sleep(delay);
      }
    }

    const totalTime = Date.now() - startTime;

    logger.error('Retry operation failed after all attempts', {
      operationName: context?.operationName,
      requestId: context?.requestId,
      maxAttempts: config.maxAttempts,
      totalTime,
      finalError: lastError?.message,
    });

    return {
      success: false,
      error: lastError,
      attempts: config.maxAttempts,
      totalTime,
    };
  }

  /**
   * Calculate delay with exponential backoff and optional jitter
   */
  private static calculateDelay(attempt: number, options: RetryOptions): number {
    let delay = options.baseDelay * Math.pow(options.backoffMultiplier, attempt - 1);
    
    // Apply maximum delay limit
    delay = Math.min(delay, options.maxDelay);
    
    // Add jitter to prevent thundering herd
    if (options.jitter) {
      delay = delay * (0.5 + Math.random() * 0.5);
    }
    
    return Math.floor(delay);
  }

  /**
   * Sleep for specified milliseconds
   */
  private static sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Create a retry wrapper for a function
   */
  static wrap<T extends any[], R>(
    fn: (...args: T) => Promise<R>,
    options: Partial<RetryOptions> = {},
    operationName?: string
  ): (...args: T) => Promise<R> {
    return async (...args: T): Promise<R> => {
      const result = await this.execute(
        () => fn(...args),
        options,
        operationName ? { operationName } : {}
      );

      if (result.success) {
        return result.result!;
      } else {
        throw result.error || new Error('Operation failed after retries');
      }
    };
  }
}

/**
 * Decorator for adding retry logic to methods
 */
export function retry(options: Partial<RetryOptions> = {}) {
  return function (target: any, propertyKey: string, descriptor: PropertyDescriptor) {
    const originalMethod = descriptor.value;
    const operationName = `${target.constructor.name}.${propertyKey}`;

    descriptor.value = async function (...args: any[]) {
      const result = await RetryManager.execute(
        () => originalMethod.apply(this, args),
        options,
        { operationName }
      );

      if (result.success) {
        return result.result;
      } else {
        throw result.error || new Error(`${operationName} failed after retries`);
      }
    };

    return descriptor;
  };
}

/**
 * Circuit breaker pattern for preventing cascading failures
 */
export class CircuitBreaker {
  private failures = 0;
  private lastFailureTime = 0;
  private state: 'CLOSED' | 'OPEN' | 'HALF_OPEN' = 'CLOSED';

  constructor(
    private readonly failureThreshold: number = 5,
    private readonly recoveryTimeout: number = 60000,
    private readonly name: string = 'CircuitBreaker'
  ) {}

  async execute<T>(operation: () => Promise<T>): Promise<T> {
    if (this.state === 'OPEN') {
      if (Date.now() - this.lastFailureTime > this.recoveryTimeout) {
        this.state = 'HALF_OPEN';
        logger.info('Circuit breaker transitioning to HALF_OPEN', { name: this.name });
      } else {
        throw new MCPError(
          'CIRCUIT_BREAKER_OPEN' as any,
          `Circuit breaker ${this.name} is OPEN`,
          'HIGH' as any,
          { metadata: { circuitBreakerName: this.name } }
        );
      }
    }

    try {
      const result = await operation();
      
      if (this.state === 'HALF_OPEN') {
        this.reset();
      }
      
      return result;
    } catch (error) {
      this.recordFailure();
      throw error;
    }
  }

  private recordFailure(): void {
    this.failures++;
    this.lastFailureTime = Date.now();

    if (this.failures >= this.failureThreshold) {
      this.state = 'OPEN';
      logger.warn('Circuit breaker opened due to failures', {
        name: this.name,
        failures: this.failures,
        threshold: this.failureThreshold,
      });
    }
  }

  private reset(): void {
    this.failures = 0;
    this.state = 'CLOSED';
    logger.info('Circuit breaker reset to CLOSED', { name: this.name });
  }

  getState(): { state: string; failures: number; lastFailureTime: number } {
    return {
      state: this.state,
      failures: this.failures,
      lastFailureTime: this.lastFailureTime,
    };
  }
}
