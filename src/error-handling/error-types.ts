/**
 * Custom error types for the S3 MCP Server
 */

export enum ErrorCode {
  // Configuration errors
  CONFIG_INVALID = 'CONFIG_INVALID',
  CONFIG_MISSING = 'CONFIG_MISSING',
  
  // AWS/S3 errors
  S3_CONNECTION_FAILED = 'S3_CONNECTION_FAILED',
  S3_UPLOAD_FAILED = 'S3_UPLOAD_FAILED',
  S3_BUCKET_NOT_FOUND = 'S3_BUCKET_NOT_FOUND',
  S3_ACCESS_DENIED = 'S3_ACCESS_DENIED',
  S3_QUOTA_EXCEEDED = 'S3_QUOTA_EXCEEDED',
  
  // MCP protocol errors
  MCP_PROTOCOL_ERROR = 'MCP_PROTOCOL_ERROR',
  MCP_TOOL_NOT_FOUND = 'MCP_TOOL_NOT_FOUND',
  MCP_INVALID_REQUEST = 'MCP_INVALID_REQUEST',
  
  // Processing errors
  RESPONSE_PROCESSING_FAILED = 'RESPONSE_PROCESSING_FAILED',
  NAMESPACE_EXTRACTION_FAILED = 'NAMESPACE_EXTRACTION_FAILED',
  CONTENT_VALIDATION_FAILED = 'CONTENT_VALIDATION_FAILED',
  
  // Network errors
  NETWORK_TIMEOUT = 'NETWORK_TIMEOUT',
  NETWORK_CONNECTION_FAILED = 'NETWORK_CONNECTION_FAILED',
  
  // Validation errors
  VALIDATION_FAILED = 'VALIDATION_FAILED',
  INVALID_INPUT = 'INVALID_INPUT',
  
  // System errors
  SYSTEM_ERROR = 'SYSTEM_ERROR',
  RESOURCE_EXHAUSTED = 'RESOURCE_EXHAUSTED',
  RATE_LIMIT_EXCEEDED = 'RATE_LIMIT_EXCEEDED',
}

export enum ErrorSeverity {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  CRITICAL = 'critical',
}

export interface ErrorContext {
  requestId?: string;
  conversationId?: string;
  namespace?: string;
  operation?: string;
  userId?: string;
  timestamp?: Date;
  metadata?: Record<string, any>;
}

export class MCPError extends Error {
  public readonly code: ErrorCode;
  public readonly severity: ErrorSeverity;
  public readonly context: ErrorContext;
  public readonly isRetryable: boolean;
  public readonly originalError?: Error | undefined;

  constructor(
    code: ErrorCode,
    message: string,
    severity: ErrorSeverity = ErrorSeverity.MEDIUM,
    context: ErrorContext = {},
    isRetryable: boolean = false,
    originalError?: Error
  ) {
    super(message);
    this.name = 'MCPError';
    this.code = code;
    this.severity = severity;
    this.context = {
      ...context,
      timestamp: context.timestamp || new Date(),
    };
    this.isRetryable = isRetryable;
    this.originalError = originalError;

    // Maintain proper stack trace
    if (Error.captureStackTrace) {
      Error.captureStackTrace(this, MCPError);
    }
  }

  toJSON() {
    return {
      name: this.name,
      message: this.message,
      code: this.code,
      severity: this.severity,
      context: this.context,
      isRetryable: this.isRetryable,
      stack: this.stack,
      originalError: this.originalError ? {
        name: this.originalError.name,
        message: this.originalError.message,
        stack: this.originalError.stack,
      } : undefined,
    };
  }
}

/**
 * Factory functions for common error types
 */
export class ErrorFactory {
  static configurationError(message: string, context?: ErrorContext): MCPError {
    return new MCPError(
      ErrorCode.CONFIG_INVALID,
      message,
      ErrorSeverity.HIGH,
      context,
      false
    );
  }

  static s3Error(message: string, context?: ErrorContext, originalError?: Error): MCPError {
    const isRetryable = this.isS3ErrorRetryable(originalError);
    const severity = this.getS3ErrorSeverity(originalError);
    
    return new MCPError(
      ErrorCode.S3_UPLOAD_FAILED,
      message,
      severity,
      context,
      isRetryable,
      originalError
    );
  }

  static processingError(message: string, context?: ErrorContext, originalError?: Error): MCPError {
    return new MCPError(
      ErrorCode.RESPONSE_PROCESSING_FAILED,
      message,
      ErrorSeverity.MEDIUM,
      context,
      true,
      originalError
    );
  }

  static networkError(message: string, context?: ErrorContext, originalError?: Error): MCPError {
    return new MCPError(
      ErrorCode.NETWORK_CONNECTION_FAILED,
      message,
      ErrorSeverity.HIGH,
      context,
      true,
      originalError
    );
  }

  static validationError(message: string, context?: ErrorContext): MCPError {
    return new MCPError(
      ErrorCode.VALIDATION_FAILED,
      message,
      ErrorSeverity.LOW,
      context,
      false
    );
  }

  private static isS3ErrorRetryable(error?: Error): boolean {
    if (!error) return true;
    
    const retryableErrors = [
      'RequestTimeout',
      'ServiceUnavailable',
      'Throttling',
      'InternalError',
      'SlowDown',
    ];
    
    return retryableErrors.some(retryableError => 
      error.message.includes(retryableError) || error.name.includes(retryableError)
    );
  }

  private static getS3ErrorSeverity(error?: Error): ErrorSeverity {
    if (!error) return ErrorSeverity.MEDIUM;
    
    if (error.message.includes('AccessDenied') || error.message.includes('Forbidden')) {
      return ErrorSeverity.HIGH;
    }
    
    if (error.message.includes('NoSuchBucket') || error.message.includes('NotFound')) {
      return ErrorSeverity.CRITICAL;
    }
    
    return ErrorSeverity.MEDIUM;
  }
}

/**
 * Error classification utilities
 */
export class ErrorClassifier {
  static isRetryable(error: Error | MCPError): boolean {
    if (error instanceof MCPError) {
      return error.isRetryable;
    }
    
    // Check for common retryable error patterns
    const retryablePatterns = [
      /timeout/i,
      /connection/i,
      /network/i,
      /temporary/i,
      /throttl/i,
      /rate limit/i,
      /service unavailable/i,
    ];
    
    return retryablePatterns.some(pattern => 
      pattern.test(error.message) || pattern.test(error.name)
    );
  }

  static getSeverity(error: Error | MCPError): ErrorSeverity {
    if (error instanceof MCPError) {
      return error.severity;
    }
    
    // Classify based on error type and message
    if (error.name === 'ValidationError' || error.message.includes('validation')) {
      return ErrorSeverity.LOW;
    }
    
    if (error.name === 'TypeError' || error.name === 'ReferenceError') {
      return ErrorSeverity.HIGH;
    }
    
    if (error.message.includes('ENOTFOUND') || error.message.includes('ECONNREFUSED')) {
      return ErrorSeverity.HIGH;
    }
    
    return ErrorSeverity.MEDIUM;
  }

  static shouldAlert(error: Error | MCPError): boolean {
    const severity = this.getSeverity(error);
    return severity === ErrorSeverity.HIGH || severity === ErrorSeverity.CRITICAL;
  }
}
