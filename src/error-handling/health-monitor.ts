/**
 * Health monitoring and alerting system
 */

import enhancedLogger from './enhanced-logger.js';
import { getConfig } from '../config.js';

export interface HealthCheck {
  name: string;
  check: () => Promise<HealthCheckResult>;
  interval: number;
  timeout: number;
  critical: boolean;
}

export interface HealthCheckResult {
  status: 'healthy' | 'unhealthy' | 'degraded';
  message?: string;
  details?: Record<string, any>;
  timestamp: Date;
  duration: number;
}

export interface SystemHealth {
  overall: 'healthy' | 'unhealthy' | 'degraded';
  checks: Record<string, HealthCheckResult>;
  timestamp: Date;
  uptime: number;
}

export class HealthMonitor {
  private checks: Map<string, HealthCheck> = new Map();
  private results: Map<string, HealthCheckResult> = new Map();
  private intervals: Map<string, NodeJS.Timeout> = new Map();
  private startTime = Date.now();
  private logger = enhancedLogger.child({ component: 'HealthMonitor' });

  constructor() {
    this.logger.info('Health monitor initialized');
  }

  /**
   * Register a health check
   */
  registerCheck(check: HealthCheck): void {
    this.checks.set(check.name, check);
    this.logger.info('Health check registered', {
      name: check.name,
      interval: check.interval,
      critical: check.critical,
    });

    // Start periodic checking
    this.startPeriodicCheck(check);
  }

  /**
   * Unregister a health check
   */
  unregisterCheck(name: string): void {
    this.checks.delete(name);
    this.results.delete(name);
    
    const interval = this.intervals.get(name);
    if (interval) {
      clearInterval(interval);
      this.intervals.delete(name);
    }

    this.logger.info('Health check unregistered', { name });
  }

  /**
   * Run a specific health check
   */
  async runCheck(name: string): Promise<HealthCheckResult> {
    const check = this.checks.get(name);
    if (!check) {
      throw new Error(`Health check '${name}' not found`);
    }

    const startTime = Date.now();
    
    try {
      // Run check with timeout
      const result = await Promise.race([
        check.check(),
        this.createTimeoutPromise(check.timeout),
      ]);

      const duration = Date.now() - startTime;
      const finalResult = {
        ...result,
        timestamp: new Date(),
        duration,
      };

      this.results.set(name, finalResult);
      
      this.logger.logHealthCheck(name, finalResult.status, {
        message: finalResult.message,
        duration,
        details: finalResult.details,
      });

      return finalResult;
    } catch (error) {
      const duration = Date.now() - startTime;
      const result: HealthCheckResult = {
        status: 'unhealthy',
        message: error instanceof Error ? error.message : String(error),
        timestamp: new Date(),
        duration,
      };

      this.results.set(name, result);
      
      this.logger.logHealthCheck(name, 'unhealthy', {
        error: result.message,
        duration,
      });

      return result;
    }
  }

  /**
   * Run all health checks
   */
  async runAllChecks(): Promise<Record<string, HealthCheckResult>> {
    const results: Record<string, HealthCheckResult> = {};
    
    const promises = Array.from(this.checks.keys()).map(async (name) => {
      try {
        const result = await this.runCheck(name);
        results[name] = result;
      } catch (error) {
        results[name] = {
          status: 'unhealthy',
          message: error instanceof Error ? error.message : String(error),
          timestamp: new Date(),
          duration: 0,
        };
      }
    });

    await Promise.allSettled(promises);
    return results;
  }

  /**
   * Get current system health
   */
  async getSystemHealth(): Promise<SystemHealth> {
    const checks = await this.runAllChecks();
    const overall = this.calculateOverallHealth(checks);
    
    return {
      overall,
      checks,
      timestamp: new Date(),
      uptime: Date.now() - this.startTime,
    };
  }

  /**
   * Get health status without running checks
   */
  getCurrentHealth(): SystemHealth {
    const checks = Object.fromEntries(this.results);
    const overall = this.calculateOverallHealth(checks);
    
    return {
      overall,
      checks,
      timestamp: new Date(),
      uptime: Date.now() - this.startTime,
    };
  }

  /**
   * Start periodic checking for a health check
   */
  private startPeriodicCheck(check: HealthCheck): void {
    // Run initial check
    this.runCheck(check.name).catch((error) => {
      this.logger.error('Initial health check failed', {
        name: check.name,
        error: error.message,
      });
    });

    // Set up periodic checking
    const interval = setInterval(() => {
      this.runCheck(check.name).catch((error) => {
        this.logger.error('Periodic health check failed', {
          name: check.name,
          error: error.message,
        });
      });
    }, check.interval);

    this.intervals.set(check.name, interval);
  }

  /**
   * Create a timeout promise
   */
  private createTimeoutPromise(timeout: number): Promise<never> {
    return new Promise((_, reject) => {
      setTimeout(() => {
        reject(new Error(`Health check timed out after ${timeout}ms`));
      }, timeout);
    });
  }

  /**
   * Calculate overall system health
   */
  private calculateOverallHealth(checks: Record<string, HealthCheckResult>): 'healthy' | 'unhealthy' | 'degraded' {
    const results = Object.values(checks);
    
    if (results.length === 0) {
      return 'healthy';
    }

    const criticalResults = results.filter((_, index) => {
      const checkName = Object.keys(checks)[index];
      const check = this.checks.get(checkName!);
      return check?.critical;
    });

    // If any critical check is unhealthy, system is unhealthy
    if (criticalResults.some(result => result.status === 'unhealthy')) {
      return 'unhealthy';
    }

    // If any check is unhealthy or degraded, system is degraded
    if (results.some(result => result.status === 'unhealthy' || result.status === 'degraded')) {
      return 'degraded';
    }

    return 'healthy';
  }

  /**
   * Stop all health checks
   */
  stop(): void {
    for (const interval of this.intervals.values()) {
      clearInterval(interval);
    }
    this.intervals.clear();
    this.logger.info('Health monitor stopped');
  }
}

/**
 * Default health checks for the MCP server
 */
export function createDefaultHealthChecks(s3Client?: any): HealthCheck[] {
  const config = getConfig();
  
  const checks: HealthCheck[] = [
    // Memory usage check
    {
      name: 'memory',
      check: async () => {
        const usage = process.memoryUsage();
        const usedMB = Math.round(usage.heapUsed / 1024 / 1024);
        const totalMB = Math.round(usage.heapTotal / 1024 / 1024);
        const usagePercent = (usedMB / totalMB) * 100;

        if (usagePercent > 90) {
          return {
            status: 'unhealthy',
            message: `High memory usage: ${usagePercent.toFixed(1)}%`,
            details: { usedMB, totalMB, usagePercent },
            timestamp: new Date(),
            duration: 0,
          };
        } else if (usagePercent > 75) {
          return {
            status: 'degraded',
            message: `Elevated memory usage: ${usagePercent.toFixed(1)}%`,
            details: { usedMB, totalMB, usagePercent },
            timestamp: new Date(),
            duration: 0,
          };
        }

        return {
          status: 'healthy',
          message: `Memory usage: ${usagePercent.toFixed(1)}%`,
          details: { usedMB, totalMB, usagePercent },
          timestamp: new Date(),
          duration: 0,
        };
      },
      interval: 30000, // 30 seconds
      timeout: 5000,
      critical: true,
    },

    // S3 connectivity check
    {
      name: 's3_connectivity',
      check: async () => {
        if (!s3Client) {
          return {
            status: 'degraded',
            message: 'S3 client not available',
            timestamp: new Date(),
            duration: 0,
          };
        }

        const result = await s3Client.testConnection();
        
        return {
          status: result.success ? 'healthy' : 'unhealthy',
          message: result.success ? 'S3 connection successful' : result.error,
          details: { bucket: config.aws.bucketName, region: config.aws.region },
          timestamp: new Date(),
          duration: 0,
        };
      },
      interval: 60000, // 1 minute
      timeout: 10000,
      critical: true,
    },
  ];

  return checks;
}

/**
 * Global health monitor instance
 */
export const healthMonitor = new HealthMonitor();
