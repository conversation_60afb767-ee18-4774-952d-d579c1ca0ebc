/**
 * HTTP Bridge for MCP Server
 * Provides HTTP/REST endpoints that proxy to the MCP server
 */

import express from 'express';
import cors from 'cors';

import { getConfig } from './config.js';
import { S3Client } from './s3-client.js';
import { AIResponseInterceptor } from './interceptor.js';
import {
  KubernetesResourceMiddleware,
  KubernetesErrorMiddleware,
  KubernetesMetricsMiddleware
} from './middleware/kubernetes-middleware.js';
import enhancedLogger from './error-handling/enhanced-logger.js';
import { errorReporter } from './error-handling/error-reporter.js';
import { healthMonitor, createDefaultHealthChecks } from './error-handling/health-monitor.js';
import { ErrorFactory } from './error-handling/error-types.js';

export class HTTPBridge {
  private app: express.Application;
  private s3Client: S3Client;
  private interceptor: AIResponseInterceptor;
  private config: ReturnType<typeof getConfig>;
  private logger = enhancedLogger.child({ component: 'HTTPBridge' });

  constructor() {
    this.config = getConfig();
    this.app = express();
    this.s3Client = new S3Client();
    this.interceptor = new AIResponseInterceptor();
    
    this.setupMiddleware();
    this.setupKubernetesMiddleware();
    this.setupMCPServer();
    this.setupRoutes();
    this.setupHealthMonitoring();
  }

  private setupMiddleware(): void {
    // CORS configuration
    this.app.use(cors({
      origin: process.env['CORS_ORIGINS']?.split(',') || '*',
      methods: ['GET', 'POST', 'OPTIONS'],
      allowedHeaders: ['Content-Type', 'Authorization', 'X-Request-ID'],
      credentials: true
    }));

    // Body parsing
    this.app.use(express.json({ limit: '10mb' }));
    this.app.use(express.urlencoded({ extended: true, limit: '10mb' }));

    // Request logging
    this.app.use((req, _res, next) => {
      const requestId = req.headers['x-request-id'] || `req_${Date.now()}_${Math.random().toString(36).substring(2, 8)}`;
      (req as any).requestId = requestId as string;
      
      this.logger.http(`${req.method} ${req.path}`, {
        requestId,
        userAgent: req.headers['user-agent'],
        ip: req.ip,
        contentLength: req.headers['content-length']
      });
      
      next();
    });

    // Error handling middleware
    this.app.use((err: any, req: express.Request, res: express.Response, _next: express.NextFunction) => {
      const error = ErrorFactory.processingError(
        `HTTP request failed: ${err.message}`,
        {
          requestId: (req as any).requestId,
          operation: 'httpRequest',
          metadata: { path: req.path, method: req.method }
        },
        err
      );

      this.logger.logError(error, {
        requestId: (req as any).requestId,
        path: req.path,
        method: req.method
      });

      errorReporter.reportError(error, {
        operation: 'httpRequest',
        requestId: (req as any).requestId
      });

      res.status(500).json({
        error: 'Internal Server Error',
        message: error.message,
        requestId: (req as any).requestId,
        timestamp: new Date().toISOString()
      });
    });
  }

  private setupKubernetesMiddleware(): void {
    this.interceptor.addMiddleware(new KubernetesResourceMiddleware());
    this.interceptor.addMiddleware(new KubernetesErrorMiddleware());
    this.interceptor.addMiddleware(new KubernetesMetricsMiddleware());
  }

  private setupMCPServer(): void {
    // We'll handle MCP protocol manually in the HTTP endpoints
    // This avoids complex type issues with the MCP SDK
  }

  private setupRoutes(): void {
    // Root endpoint
    this.app.get('/', (_req, res) => {
      res.json({
        name: 'S3 MCP Server HTTP Bridge',
        version: '1.0.0',
        description: 'HTTP bridge for Model Context Protocol server with S3 storage',
        endpoints: {
          mcp: '/mcp',
          tools: '/tools/capture_ai_response',
          health: '/health',
          metrics: '/metrics'
        },
        documentation: 'https://github.com/your-repo/s3-mcp-server'
      });
    });

    // MCP Protocol endpoint (full MCP compatibility)
    this.app.post('/mcp', async (req, res) => {
      try {
        const startTime = Date.now();
        const request = req.body;

        let result;
        if (request.method === 'tools/list') {
          result = {
            jsonrpc: '2.0',
            id: request.id,
            result: {
              tools: [
                {
                  name: 'capture_ai_response',
                  description: 'Capture and store AI agent responses in S3 with Kubernetes namespace organization',
                  inputSchema: {
                    type: 'object',
                    properties: {
                      content: {
                        type: 'string',
                        description: 'The AI response content to store'
                      },
                      namespace: {
                        type: 'string',
                        description: 'Kubernetes namespace (auto-detected if not provided)'
                      },
                      conversationId: {
                        type: 'string',
                        description: 'Unique conversation identifier'
                      },
                      requestType: {
                        type: 'string',
                        description: 'Type of request (e.g., "pod_log_analysis")'
                      },
                      metadata: {
                        type: 'object',
                        description: 'Additional metadata to store'
                      }
                    },
                    required: ['content']
                  }
                }
              ]
            }
          };
        } else if (request.method === 'tools/call' && request.params?.name === 'capture_ai_response') {
          const toolResult = await this.handleCaptureResponse(request.params.arguments);
          result = {
            jsonrpc: '2.0',
            id: request.id,
            result: toolResult
          };
        } else {
          result = {
            jsonrpc: '2.0',
            id: request.id,
            error: {
              code: -32601,
              message: 'Method not found',
              data: { method: request.method }
            }
          };
        }

        const duration = Date.now() - startTime;
        this.logger.logPerformance('MCPRequest', duration, {
          requestId: (req as any).requestId,
          method: request.method,
          toolName: request.params?.name
        });

        res.json(result);
      } catch (error) {
        const mcpError = ErrorFactory.processingError(
          `MCP request failed: ${error instanceof Error ? error.message : String(error)}`,
          {
            requestId: (req as any).requestId,
            operation: 'mcpRequest'
          },
          error instanceof Error ? error : undefined
        );

        this.logger.logError(mcpError, { requestId: (req as any).requestId });
        await errorReporter.reportError(mcpError, { requestId: (req as any).requestId });

        res.status(500).json({
          jsonrpc: '2.0',
          id: req.body.id,
          error: {
            code: -32603,
            message: 'Internal error',
            data: {
              details: mcpError.message,
              requestId: (req as any).requestId
            }
          }
        });
      }
    });

    // Direct tool endpoint (REST-style)
    this.app.post('/tools/capture_ai_response', async (req, res) => {
      try {
        const startTime = Date.now();
        const result = await this.handleCaptureResponse(req.body);
        const duration = Date.now() - startTime;

        this.logger.logPerformance('DirectToolCall', duration, {
          requestId: (req as any).requestId,
          contentLength: req.body.content?.length
        });

        // Return simplified response for direct API calls
        if (result.content && result.content[0]) {
          const content = result.content[0].text;
          const successMatch = content.match(/S3 Key: ([^\n]+)/);
          const namespaceMatch = content.match(/Namespace: ([^\s]+)/);
          
          res.json({
            success: true,
            message: 'Response captured successfully',
            s3Key: successMatch ? successMatch[1] : null,
            namespace: namespaceMatch ? namespaceMatch[1] : null,
            requestId: (req as any).requestId,
            timestamp: new Date().toISOString(),
            processingTime: duration
          });
        } else {
          throw new Error('Unexpected response format');
        }
      } catch (error) {
        const toolError = ErrorFactory.processingError(
          `Tool call failed: ${error instanceof Error ? error.message : String(error)}`,
          {
            requestId: (req as any).requestId,
            operation: 'directToolCall'
          },
          error instanceof Error ? error : undefined
        );

        this.logger.logError(toolError, { requestId: (req as any).requestId });
        await errorReporter.reportError(toolError, { requestId: (req as any).requestId });

        res.status(500).json({
          success: false,
          error: toolError.message,
          requestId: (req as any).requestId,
          timestamp: new Date().toISOString()
        });
      }
    });

    // Health endpoints
    this.app.get('/health', async (_req, res) => {
      try {
        const health = await healthMonitor.getSystemHealth();
        const statusCode = health.overall === 'healthy' ? 200 : 
                          health.overall === 'degraded' ? 200 : 503;
        
        res.status(statusCode).json(health);
      } catch (error) {
        res.status(503).json({
          overall: 'unhealthy',
          error: error instanceof Error ? error.message : String(error),
          timestamp: new Date().toISOString()
        });
      }
    });

    this.app.get('/health/detailed', async (_req, res) => {
      try {
        const health = await healthMonitor.getSystemHealth();
        res.json(health);
      } catch (error) {
        res.status(503).json({
          overall: 'unhealthy',
          error: error instanceof Error ? error.message : String(error),
          timestamp: new Date().toISOString()
        });
      }
    });

    // Metrics endpoints
    this.app.get('/metrics', (_req, res) => {
      const stats = this.s3Client.getStats();
      const errorStats = errorReporter.getStatistics();

      // Prometheus format
      const metrics = [
        `# HELP s3_mcp_uploads_total Total number of S3 uploads`,
        `# TYPE s3_mcp_uploads_total counter`,
        `s3_mcp_uploads_total{status="success"} ${stats.successfulUploads}`,
        `s3_mcp_uploads_total{status="failure"} ${stats.failedUploads}`,
        ``,
        `# HELP s3_mcp_upload_duration_seconds Upload duration in seconds`,
        `# TYPE s3_mcp_upload_duration_seconds gauge`,
        `s3_mcp_upload_duration_seconds ${stats.averageUploadTime / 1000}`,
        ``,
        `# HELP s3_mcp_errors_total Total number of errors`,
        `# TYPE s3_mcp_errors_total counter`,
        `s3_mcp_errors_total{severity="critical"} ${errorStats.criticalErrors}`,
        `s3_mcp_errors_total{severity="all"} ${errorStats.totalReports}`,
      ].join('\n');

      res.set('Content-Type', 'text/plain');
      res.send(metrics);
    });

    this.app.get('/metrics/json', (_req, res) => {
      res.json({
        s3: this.s3Client.getStats(),
        logger: enhancedLogger.getMetrics(),
        performance: enhancedLogger.getPerformanceMetrics(),
        errors: errorReporter.getStatistics(),
        interceptor: this.interceptor.getStats()
      });
    });

    // Configuration endpoint (masked)
    this.app.get('/config', (_req, res) => {
      const config = { ...this.config };
      
      // Mask sensitive values
      if (config.aws?.accessKeyId) {
        config.aws.accessKeyId = config.aws.accessKeyId.substring(0, 4) + '*'.repeat(config.aws.accessKeyId.length - 4);
      }
      if (config.aws?.secretAccessKey) {
        config.aws.secretAccessKey = '*'.repeat(config.aws.secretAccessKey.length);
      }

      res.json(config);
    });
  }

  private setupHealthMonitoring(): void {
    const healthChecks = createDefaultHealthChecks(this.s3Client);
    healthChecks.forEach(check => healthMonitor.registerCheck(check));
  }

  private async handleCaptureResponse(args: any): Promise<any> {
    const startTime = Date.now();
    
    try {
      this.logger.info('Processing AI response capture via HTTP', {
        contentLength: args.content?.length,
        namespace: args.namespace,
        conversationId: args.conversationId,
      });

      // Process through interceptor
      const aiResponse = await this.interceptor.interceptResponse(args.content, {
        conversationId: args.conversationId,
        metadata: {
          requestType: args.requestType,
          ...args.metadata
        },
      });

      // Override namespace if explicitly provided
      if (args.namespace) {
        aiResponse.namespace = {
          namespace: args.namespace,
          confidence: 'high',
          source: 'parameter',
        };
      }

      // Upload to S3
      const uploadResult = await this.s3Client.uploadResponse(aiResponse);

      if (uploadResult.success) {
        const duration = Date.now() - startTime;
        this.logger.logPerformance('CaptureAIResponse', duration, {
          key: uploadResult.key,
          bucket: uploadResult.bucket,
          namespace: aiResponse.namespace.namespace,
          responseId: aiResponse.id,
          contentLength: args.content?.length,
        });

        this.logger.info('Successfully stored AI response in S3 via HTTP', {
          key: uploadResult.key,
          bucket: uploadResult.bucket,
          namespace: aiResponse.namespace.namespace,
          responseId: aiResponse.id,
          duration,
        });

        return {
          content: [
            {
              type: 'text',
              text: `✅ Successfully stored AI response in S3\n\n` +
                    `**Details:**\n` +
                    `- S3 Key: ${uploadResult.key}\n` +
                    `- Bucket: ${uploadResult.bucket}\n` +
                    `- Namespace: ${aiResponse.namespace.namespace} (${aiResponse.namespace.confidence} confidence)\n` +
                    `- Upload Time: ${uploadResult.uploadTime}ms\n` +
                    `- File Size: ${(uploadResult.size! / 1024).toFixed(1)} KB\n\n` +
                    `**Detected Information:**\n` +
                    `- Pod Names: ${aiResponse.podNames?.join(', ') || 'None detected'}\n` +
                    `- Kubernetes Errors: ${aiResponse.metadata?.['kubernetesErrors']?.length || 0} detected\n` +
                    `- Metrics: ${aiResponse.metadata?.['kubernetesMetrics']?.length || 0} detected`,
            },
          ],
        };
      } else {
        const duration = Date.now() - startTime;
        const error = ErrorFactory.s3Error(
          `Failed to store AI response in S3: ${uploadResult.error}`,
          {
            operation: 'captureAIResponse',
            requestId: aiResponse.id,
            namespace: aiResponse.namespace.namespace,
          }
        );

        this.logger.logError(error, {
          responseId: aiResponse.id,
          duration,
        });

        await errorReporter.reportError(error, {
          operation: 'captureAIResponse',
          responseId: aiResponse.id,
        });

        return {
          content: [
            {
              type: 'text',
              text: `❌ Failed to store AI response in S3\n\n` +
                    `**Error:** ${error.message}\n` +
                    `**Error ID:** ${error.context.requestId || 'unknown'}\n` +
                    `**Timestamp:** ${error.context.timestamp?.toISOString()}\n\n` +
                    `Please check the server logs for more details.`,
            },
          ],
        };
      }

    } catch (error) {
      const duration = Date.now() - startTime;
      const mcpError = ErrorFactory.processingError(
        `Error processing AI response capture: ${error instanceof Error ? error.message : String(error)}`,
        {
          operation: 'captureAIResponse',
          ...(args.conversationId && { conversationId: args.conversationId }),
        },
        error instanceof Error ? error : undefined
      );

      this.logger.logError(mcpError, {
        contentLength: args.content?.length,
        namespace: args.namespace,
        duration,
      });

      await errorReporter.reportError(mcpError, {
        operation: 'captureAIResponse',
        contentLength: args.content?.length,
      });

      return {
        content: [
          {
            type: 'text',
            text: `❌ Error processing AI response capture\n\n` +
                  `**Error:** ${mcpError.message}\n` +
                  `**Error ID:** ${mcpError.context.requestId || 'unknown'}\n` +
                  `**Timestamp:** ${mcpError.context.timestamp?.toISOString()}\n\n` +
                  `Please check the server logs for more details.`,
          },
        ],
      };
    }
  }

  public start(port: number = 3000): void {
    this.app.listen(port, '0.0.0.0', () => {
      this.logger.info(`HTTP Bridge started`, {
        port,
        environment: process.env['NODE_ENV'] || 'development',
        endpoints: {
          mcp: `http://localhost:${port}/mcp`,
          tools: `http://localhost:${port}/tools/capture_ai_response`,
          health: `http://localhost:${port}/health`,
          metrics: `http://localhost:${port}/metrics`
        }
      });
    });
  }

  public stop(): void {
    healthMonitor.stop();
    this.logger.info('HTTP Bridge stopped');
  }
}

// Extend Express Request interface
declare global {
  namespace Express {
    interface Request {
      requestId?: string;
    }
  }
}
