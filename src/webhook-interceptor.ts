/**
 * Webhook-based AI Response Interceptor
 * Provides HTTP endpoints for real-time AI response interception
 */

import express, { Request, Response } from 'express';
import { createServer, Server } from 'http';
import { AIResponseInterceptor, InterceptorContext } from './interceptor.js';
import { getConfig } from './config.js';
import logger from './logger.js';

export interface WebhookConfig {
  port: number;
  path: string;
  secret?: string | undefined;
  enableCors: boolean;
  maxPayloadSize: string;
}

export interface WebhookPayload {
  content: string;
  conversationId?: string;
  sessionId?: string;
  userId?: string;
  namespace?: string;
  podNames?: string[];
  requestType?: string;
  metadata?: Record<string, any>;
  isStreaming?: boolean;
  streamId?: string;
  isComplete?: boolean;
}

export class WebhookInterceptor {
  private app: express.Application;
  private server: Server | null = null;
  private interceptor: AIResponseInterceptor;
  private _config: ReturnType<typeof getConfig>;
  private webhookConfig: WebhookConfig;

  constructor(webhookConfig?: Partial<WebhookConfig>) {
    this._config = getConfig();
    this.interceptor = new AIResponseInterceptor();
    this.app = express();
    
    this.webhookConfig = {
      port: webhookConfig?.port || 3001,
      path: webhookConfig?.path || '/webhook/ai-response',
      secret: webhookConfig?.secret || undefined,
      enableCors: webhookConfig?.enableCors ?? true,
      maxPayloadSize: webhookConfig?.maxPayloadSize || '10mb',
    };

    this.setupMiddleware();
    this.setupRoutes();
    this.setupErrorHandling();
  }

  /**
   * Start the webhook server
   */
  async start(): Promise<void> {
    return new Promise((resolve, reject) => {
      try {
        this.server = createServer(this.app);
        
        this.server.listen(this.webhookConfig.port, () => {
          logger.info('Webhook interceptor started', {
            port: this.webhookConfig.port,
            path: this.webhookConfig.path,
          });
          resolve();
        });

        this.server.on('error', (error) => {
          logger.error('Webhook server error', { error });
          reject(error);
        });
      } catch (error) {
        reject(error);
      }
    });
  }

  /**
   * Stop the webhook server
   */
  async stop(): Promise<void> {
    return new Promise((resolve) => {
      if (this.server) {
        this.server.close(() => {
          logger.info('Webhook interceptor stopped');
          resolve();
        });
      } else {
        resolve();
      }
    });
  }

  /**
   * Get the interceptor instance for adding custom middleware
   */
  getInterceptor(): AIResponseInterceptor {
    return this.interceptor;
  }

  /**
   * Setup Express middleware
   */
  private setupMiddleware(): void {
    // CORS
    if (this.webhookConfig.enableCors) {
      this.app.use((req, res, next) => {
        res.header('Access-Control-Allow-Origin', '*');
        res.header('Access-Control-Allow-Methods', 'POST, OPTIONS');
        res.header('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-Webhook-Secret');
        
        if (req.method === 'OPTIONS') {
          res.sendStatus(200);
          return;
        }
        
        next();
      });
    }

    // Body parsing
    this.app.use(express.json({ limit: this.webhookConfig.maxPayloadSize }));
    this.app.use(express.urlencoded({ extended: true, limit: this.webhookConfig.maxPayloadSize }));

    // Request logging
    this.app.use((req, _res, next) => {
      logger.debug('Webhook request received', {
        method: req.method,
        path: req.path,
        contentLength: req.get('content-length'),
        userAgent: req.get('user-agent'),
      });
      next();
    });

    // Secret validation
    if (this.webhookConfig.secret) {
      this.app.use(this.webhookConfig.path, (req, res, next) => {
        const providedSecret = req.get('X-Webhook-Secret') || req.body.secret;
        
        if (providedSecret !== this.webhookConfig.secret) {
          logger.warn('Webhook request with invalid secret', {
            ip: req.ip,
            userAgent: req.get('user-agent'),
          });
          res.status(401).json({ error: 'Invalid webhook secret' });
          return;
        }
        
        next();
      });
    }
  }

  /**
   * Setup Express routes
   */
  private setupRoutes(): void {
    // Health check endpoint
    this.app.get('/health', (_req, res) => {
      const stats = this.interceptor.getStats();
      res.json({
        status: 'healthy',
        timestamp: new Date().toISOString(),
        stats,
      });
    });

    // Main webhook endpoint for AI responses
    this.app.post(this.webhookConfig.path, async (req, res) => {
      try {
        await this.handleWebhookRequest(req, res);
      } catch (error) {
        logger.error('Webhook request processing failed', {
          error: error instanceof Error ? error.message : String(error),
          body: req.body,
        });
        
        res.status(500).json({
          error: 'Internal server error',
          message: error instanceof Error ? error.message : 'Unknown error',
        });
      }
    });

    // Batch endpoint for multiple responses
    this.app.post(`${this.webhookConfig.path}/batch`, async (req, res) => {
      try {
        await this.handleBatchWebhookRequest(req, res);
      } catch (error) {
        logger.error('Batch webhook request processing failed', {
          error: error instanceof Error ? error.message : String(error),
        });
        
        res.status(500).json({
          error: 'Internal server error',
          message: error instanceof Error ? error.message : 'Unknown error',
        });
      }
    });

    // Statistics endpoint
    this.app.get('/stats', (_req, res) => {
      const stats = this.interceptor.getStats();
      res.json(stats);
    });
  }

  /**
   * Handle single webhook request
   */
  private async handleWebhookRequest(req: Request, res: Response): Promise<void> {
    const payload: WebhookPayload = req.body;
    
    if (!payload.content) {
      res.status(400).json({ error: 'Missing required field: content' });
      return;
    }

    const context: Partial<InterceptorContext> = {
      conversationId: payload.conversationId || undefined,
      sessionId: payload.sessionId || undefined,
      userId: payload.userId || undefined,
      metadata: {
        ...payload.metadata,
        namespace: payload.namespace,
        podNames: payload.podNames,
        requestType: payload.requestType,
        source: 'webhook',
        ip: req.ip,
        userAgent: req.get('user-agent'),
      },
    };

    let result;
    
    if (payload.isStreaming && payload.streamId) {
      // Handle streaming response
      result = await this.interceptor.interceptStreamChunk(
        payload.content,
        payload.streamId,
        payload.isComplete || false,
        context
      );
    } else {
      // Handle complete response
      result = await this.interceptor.interceptResponse(payload.content, context);
    }

    res.json({
      success: true,
      responseId: result.id,
      namespace: 'namespace' in result ? result.namespace : undefined,
      timestamp: result.timestamp,
    });
  }

  /**
   * Handle batch webhook request
   */
  private async handleBatchWebhookRequest(req: Request, res: Response): Promise<void> {
    const payloads: WebhookPayload[] = req.body.responses || req.body;
    
    if (!Array.isArray(payloads)) {
      res.status(400).json({ error: 'Expected array of responses' });
      return;
    }

    const results = [];
    
    for (const payload of payloads) {
      try {
        if (!payload.content) {
          results.push({ error: 'Missing content', payload });
          continue;
        }

        const context: Partial<InterceptorContext> = {
          conversationId: payload.conversationId || undefined,
          sessionId: payload.sessionId || undefined,
          userId: payload.userId || undefined,
          metadata: {
            ...payload.metadata,
            namespace: payload.namespace,
            podNames: payload.podNames,
            requestType: payload.requestType,
            source: 'webhook-batch',
            ip: req.ip,
            userAgent: req.get('user-agent'),
          },
        };

        const result = await this.interceptor.interceptResponse(payload.content, context);
        results.push({
          success: true,
          responseId: result.id,
          namespace: result.namespace,
          timestamp: result.timestamp,
        });
      } catch (error) {
        results.push({
          error: error instanceof Error ? error.message : 'Unknown error',
          payload,
        });
      }
    }

    res.json({
      success: true,
      processed: results.length,
      results,
    });
  }

  /**
   * Setup error handling
   */
  private setupErrorHandling(): void {
    // 404 handler
    this.app.use((req, res) => {
      res.status(404).json({
        error: 'Not found',
        path: req.path,
        method: req.method,
      });
    });

    // Global error handler
    this.app.use((error: Error, req: Request, res: Response, _next: any) => {
      logger.error('Express error handler', {
        error: error.message,
        stack: error.stack,
        path: req.path,
        method: req.method,
      });

      res.status(500).json({
        error: 'Internal server error',
        message: error.message,
      });
    });
  }
}
