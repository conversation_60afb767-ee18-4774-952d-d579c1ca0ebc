/**
 * Main MCP Server implementation
 * Captures AI agent responses and stores them in S3 with Kubernetes namespace organization
 */

import { Server } from '@modelcontextprotocol/sdk/server/index.js';
import { StdioServerTransport } from '@modelcontextprotocol/sdk/server/stdio.js';
import { HTTPBridge } from './http-bridge.js';
import {
  CallToolRequestSchema,
  ListToolsRequestSchema,
  LoggingMessageNotificationSchema,
} from '@modelcontextprotocol/sdk/types.js';

import logger from './logger.js';
import { getConfig } from './config.js';

import { S3Client } from './s3-client.js';
import { AIResponseInterceptor } from './interceptor.js';
import {
  KubernetesResourceMiddleware,
  KubernetesErrorMiddleware,
  KubernetesMetricsMiddleware
} from './middleware/kubernetes-middleware.js';

// Enhanced error handling and monitoring
import enhancedLogger from './error-handling/enhanced-logger.js';
import { errorReporter } from './error-handling/error-reporter.js';
import { healthMonitor, createDefaultHealthChecks } from './error-handling/health-monitor.js';
import { ErrorFactory } from './error-handling/error-types.js';

class S3MCPServer {
  private server: Server;
  private config: ReturnType<typeof getConfig>;
  private s3Client: S3Client;
  private interceptor: AIResponseInterceptor;
  private logger = enhancedLogger.child({ component: 'S3MCPServer' });

  constructor() {
    this.config = getConfig();
    this.s3Client = new S3Client();
    this.interceptor = new AIResponseInterceptor();

    // Setup error handling and monitoring
    this.setupErrorHandling();

    // Setup Kubernetes-specific middleware
    this.setupKubernetesMiddleware();

    // Initialize MCP Server
    this.server = new Server(
      {
        name: this.config.mcp.serverName,
        version: '1.0.0',
      },
      {
        capabilities: {
          tools: {},
          logging: {},
        },
      }
    );

    this.setupHandlers();
  }

  private setupHandlers(): void {
    // Handle tool listing
    this.server.setRequestHandler(ListToolsRequestSchema, async () => {
      logger.debug('Received tools/list request');
      return {
        tools: [
          {
            name: 'capture_ai_response',
            description: 'Captures AI agent responses and stores them in S3 with Kubernetes namespace organization',
            inputSchema: {
              type: 'object',
              properties: {
                content: {
                  type: 'string',
                  description: 'The AI response content to capture and store',
                },
                namespace: {
                  type: 'string',
                  description: 'Kubernetes namespace (optional, will be auto-detected if not provided)',
                },
                conversationId: {
                  type: 'string',
                  description: 'Unique identifier for the conversation (optional)',
                },
                sessionId: {
                  type: 'string',
                  description: 'Session identifier for grouping related conversations (optional)',
                },
                userId: {
                  type: 'string',
                  description: 'User identifier for tracking user-specific interactions (optional)',
                },
                requestType: {
                  type: 'string',
                  description: 'Type of request being processed (e.g., pod_log_analysis)',
                },
                podNames: {
                  type: 'array',
                  items: { type: 'string' },
                  description: 'Names of Kubernetes pods being analyzed (optional)',
                },
                metadata: {
                  type: 'object',
                  description: 'Additional metadata to store with the response (optional)',
                },
              },
              required: ['content'],
            },
          },
        ],
      };
    });

    // Handle tool calls
    this.server.setRequestHandler(CallToolRequestSchema, async (request) => {
      const { name, arguments: args } = request.params;

      logger.info('Received tool call', { name, args });

      if (name === 'capture_ai_response') {
        return await this.handleCaptureResponse(args);
      }

      throw new Error(`Unknown tool: ${name}`);
    });

    // Handle logging messages
    this.server.setNotificationHandler(LoggingMessageNotificationSchema, async (notification) => {
      const { level, data } = notification.params;
      logger.log(level, 'MCP Client Log', data);
    });

    // Set up initialization callback
    this.server.oninitialized = () => {
      logger.info('MCP Server initialized successfully', {
        serverName: this.config.mcp.serverName,
        clientCapabilities: this.server.getClientCapabilities(),
      });
    };
  }

  private async handleCaptureResponse(args: any): Promise<any> {
    const startTime = Date.now();

    try {
      this.logger.info('Processing AI response capture', {
        contentLength: args.content?.length,
        namespace: args.namespace,
        conversationId: args.conversationId,
      });

      // Process the response through the interceptor for enhanced processing
      const aiResponse = await this.interceptor.interceptResponse(
        args.content,
        {
          conversationId: args.conversationId,
          sessionId: args.sessionId,
          userId: args.userId,
          metadata: {
            namespace: args.namespace,
            requestType: args.requestType,
            podNames: args.podNames,
            ...args.metadata,
          },
        }
      );

      // Upload to S3
      const uploadResult = await this.s3Client.uploadResponse(aiResponse);

      if (uploadResult.success) {
        const duration = Date.now() - startTime;
        this.logger.logPerformance('CaptureAIResponse', duration, {
          key: uploadResult.key,
          bucket: uploadResult.bucket,
          namespace: aiResponse.namespace.namespace,
          responseId: aiResponse.id,
          contentLength: args.content?.length,
        });

        this.logger.info('Successfully stored AI response in S3', {
          key: uploadResult.key,
          bucket: uploadResult.bucket,
          namespace: aiResponse.namespace.namespace,
          responseId: aiResponse.id,
          duration,
        });

        return {
          content: [
            {
              type: 'text',
              text: `✅ AI response captured and stored successfully!\n\n` +
                    `📁 **Storage Details:**\n` +
                    `- Bucket: ${uploadResult.bucket}\n` +
                    `- Key: ${uploadResult.key}\n` +
                    `- Namespace: ${aiResponse.namespace.namespace}\n` +
                    `- Response ID: ${aiResponse.id}\n` +
                    `- Timestamp: ${aiResponse.timestamp.toISOString()}\n\n` +
                    `🔍 **Namespace Detection:**\n` +
                    `- Source: ${aiResponse.namespace.source}\n` +
                    `- Confidence: ${aiResponse.namespace.confidence}\n` +
                    (aiResponse.namespace.extractedFrom ? `- Extracted from: ${aiResponse.namespace.extractedFrom}\n` : '') +
                    `\n📊 **Content Summary:**\n` +
                    `- Length: ${args.content.length} characters\n` +
                    (aiResponse.podNames?.length ? `- Pod Names: ${aiResponse.podNames.join(', ')}\n` : '') +
                    (aiResponse.requestType ? `- Request Type: ${aiResponse.requestType}\n` : ''),
            },
          ],
        };
      } else {
        const duration = Date.now() - startTime;
        const error = ErrorFactory.s3Error(
          `Failed to store AI response in S3: ${uploadResult.error}`,
          {
            operation: 'captureAIResponse',
            requestId: aiResponse.id,
            namespace: aiResponse.namespace.namespace,
          }
        );

        this.logger.logError(error, {
          responseId: aiResponse.id,
          duration,
        });

        await errorReporter.reportError(error, {
          operation: 'captureAIResponse',
          responseId: aiResponse.id,
        });

        return {
          content: [
            {
              type: 'text',
              text: `❌ Failed to store AI response in S3\n\n` +
                    `**Error:** ${uploadResult.error}\n` +
                    `**Response ID:** ${aiResponse.id}\n` +
                    `**Timestamp:** ${aiResponse.timestamp.toISOString()}\n\n` +
                    `The response was processed but could not be uploaded to S3. ` +
                    `Please check your AWS credentials and S3 configuration.`,
            },
          ],
        };
      }
    } catch (error) {
      const duration = Date.now() - startTime;
      const mcpError = ErrorFactory.processingError(
        `Error processing AI response capture: ${error instanceof Error ? error.message : String(error)}`,
        {
          operation: 'captureAIResponse',
          ...(args.conversationId && { conversationId: args.conversationId }),
        },
        error instanceof Error ? error : undefined
      );

      this.logger.logError(mcpError, {
        contentLength: args.content?.length,
        namespace: args.namespace,
        duration,
      });

      await errorReporter.reportError(mcpError, {
        operation: 'captureAIResponse',
        contentLength: args.content?.length,
      });

      return {
        content: [
          {
            type: 'text',
            text: `❌ Error processing AI response capture\n\n` +
                  `**Error:** ${mcpError.message}\n` +
                  `**Error ID:** ${mcpError.context.requestId || 'unknown'}\n` +
                  `**Timestamp:** ${mcpError.context.timestamp?.toISOString()}\n\n` +
                  `Please check the server logs for more details.`,
          },
        ],
      };
    }
  }

  /**
   * Setup error handling and monitoring
   */
  private setupErrorHandling(): void {
    // Setup health monitoring
    const healthChecks = createDefaultHealthChecks(this.s3Client);
    healthChecks.forEach(check => healthMonitor.registerCheck(check));

    // Setup global error handlers
    process.on('uncaughtException', (error) => {
      this.logger.logError(ErrorFactory.configurationError(
        `Uncaught exception: ${error.message}`,
        { operation: 'uncaughtException' }
      ));
      errorReporter.reportError(error, { type: 'uncaughtException' });
    });

    process.on('unhandledRejection', (reason) => {
      const error = reason instanceof Error ? reason : new Error(String(reason));
      this.logger.logError(ErrorFactory.configurationError(
        `Unhandled rejection: ${error.message}`,
        { operation: 'unhandledRejection' }
      ));
      errorReporter.reportError(error, { type: 'unhandledRejection' });
    });

    this.logger.info('Error handling and monitoring configured', {
      healthChecks: healthChecks.length,
    });
  }

  /**
   * Setup Kubernetes-specific middleware for enhanced processing
   */
  private setupKubernetesMiddleware(): void {
    // Add Kubernetes resource detection middleware
    this.interceptor.addMiddleware(new KubernetesResourceMiddleware());

    // Add Kubernetes error detection middleware
    this.interceptor.addMiddleware(new KubernetesErrorMiddleware());

    // Add Kubernetes metrics extraction middleware
    this.interceptor.addMiddleware(new KubernetesMetricsMiddleware());

    this.logger.info('Kubernetes middleware configured', {
      middlewareCount: this.interceptor.getStats().middlewareCount,
    });
  }

  async start(): Promise<void> {
    try {
      logger.info('Starting S3 MCP Server...', {
        serverName: this.config.mcp.serverName,
        bucketName: this.config.aws.bucketName,
        defaultNamespace: this.config.kubernetes.defaultNamespacePrefix,
      });

      const transport = new StdioServerTransport();
      await this.server.connect(transport);

      logger.info('S3 MCP Server started successfully on stdio transport');
    } catch (error) {
      logger.error('Failed to start S3 MCP Server', { error });
      throw error;
    }
  }

  async stop(): Promise<void> {
    try {
      logger.info('Stopping S3 MCP Server...');
      await this.server.close();
      logger.info('S3 MCP Server stopped successfully');
    } catch (error) {
      logger.error('Error stopping S3 MCP Server', { error });
      throw error;
    }
  }
}

// Create and start the server
async function main(): Promise<void> {
  // Check if we should run in HTTP mode
  const httpMode = process.env['HTTP_MODE'] === 'true' || process.argv.includes('--http');
  const httpPort = parseInt(process.env['HTTP_PORT'] || '3000');

  if (httpMode) {
    // Start HTTP Bridge
    logger.info('Starting S3 MCP Server in HTTP mode', { port: httpPort });
    const httpBridge = new HTTPBridge();
    httpBridge.start(httpPort);

    // Graceful shutdown for HTTP mode
    const shutdown = (signal: string) => {
      logger.info(`Received ${signal}, shutting down HTTP bridge gracefully...`);
      httpBridge.stop();
      process.exit(0);
    };

    process.on('SIGINT', () => shutdown('SIGINT'));
    process.on('SIGTERM', () => shutdown('SIGTERM'));

    // Keep the process alive
    process.on('exit', () => {
      httpBridge.stop();
    });
  } else {
    // Start traditional MCP stdio server
    logger.info('Starting S3 MCP Server in stdio mode');
    const mcpServer = new S3MCPServer();

    // Handle graceful shutdown for stdio mode
    const shutdown = async (signal: string) => {
      logger.info(`Received ${signal}, shutting down gracefully...`);
      try {
        await mcpServer.stop();
        process.exit(0);
      } catch (error) {
        logger.error('Error during shutdown', { error });
        process.exit(1);
      }
    };

    process.on('SIGINT', () => shutdown('SIGINT'));
    process.on('SIGTERM', () => shutdown('SIGTERM'));

    await mcpServer.start();
  }
}

// Start the server
if (require.main === module) {
  main().catch((error) => {
    logger.error('Failed to start server:', error);
    process.exit(1);
  });
}
