{"bucket":"test-s3-bucket","level":"info","message":"S3 Client initialized","region":"us-east-1","service":"s3-mcp-server","timestamp":"2025-06-24 10:27:13"}
{"level":"error","message":"AWS S3 Error [object Object]","service":"s3-mcp-server","timestamp":"2025-06-24 10:27:14"}
{"bucket":"test-s3-bucket","error":"UnknownError","level":"error","message":"S3 connection test failed","region":"us-east-1","service":"s3-mcp-server","timestamp":"2025-06-24 10:27:14"}
{"level":"error","message":"AWS S3 Error [object Object]","service":"s3-mcp-server","timestamp":"2025-06-24 10:27:14"}
{"error":"The bucket you are attempting to access must be addressed using the specified endpoint. Please send all future requests to this endpoint.","level":"error","message":"Failed to upload response to S3","namespace":"production","responseId":"123e4567-e89b-12d3-a456-426614174000","service":"s3-mcp-server","timestamp":"2025-06-24 10:27:14","uploadTime":99}
{"level":"error","message":"AWS S3 Error [object Object]","service":"s3-mcp-server","timestamp":"2025-06-24 10:27:14"}
{"error":"The bucket you are attempting to access must be addressed using the specified endpoint. Please send all future requests to this endpoint.","level":"error","message":"Failed to upload response to S3","namespace":"staging","responseId":"abc12345-def6-7890-ghij-klmnopqrstuv","service":"s3-mcp-server","timestamp":"2025-06-24 10:27:14","uploadTime":272}
{"level":"error","message":"AWS S3 Error [object Object]","service":"s3-mcp-server","timestamp":"2025-06-24 10:27:14"}
{"error":"The bucket you are attempting to access must be addressed using the specified endpoint. Please send all future requests to this endpoint.","level":"error","message":"Failed to upload response to S3","namespace":"NO_NAMESPACE","responseId":"xyz98765-abc1-2345-defg-hijklmnopqrs","service":"s3-mcp-server","timestamp":"2025-06-24 10:27:14","uploadTime":268}
{"count":3,"level":"info","message":"Starting batch upload","service":"s3-mcp-server","timestamp":"2025-06-24 10:27:14"}
{"level":"error","message":"AWS S3 Error [object Object]","service":"s3-mcp-server","timestamp":"2025-06-24 10:27:15"}
{"error":"The bucket you are attempting to access must be addressed using the specified endpoint. Please send all future requests to this endpoint.","level":"error","message":"Failed to upload response to S3","namespace":"NO_NAMESPACE","responseId":"xyz98765-abc1-2345-defg-hijklmnopqrs","service":"s3-mcp-server","timestamp":"2025-06-24 10:27:15","uploadTime":278}
{"level":"error","message":"AWS S3 Error [object Object]","service":"s3-mcp-server","timestamp":"2025-06-24 10:27:15"}
{"error":"The bucket you are attempting to access must be addressed using the specified endpoint. Please send all future requests to this endpoint.","level":"error","message":"Failed to upload response to S3","namespace":"staging","responseId":"abc12345-def6-7890-ghij-klmnopqrstuv","service":"s3-mcp-server","timestamp":"2025-06-24 10:27:15","uploadTime":280}
{"level":"error","message":"AWS S3 Error [object Object]","service":"s3-mcp-server","timestamp":"2025-06-24 10:27:15"}
{"error":"The bucket you are attempting to access must be addressed using the specified endpoint. Please send all future requests to this endpoint.","level":"error","message":"Failed to upload response to S3","namespace":"production","responseId":"123e4567-e89b-12d3-a456-426614174000","service":"s3-mcp-server","timestamp":"2025-06-24 10:27:15","uploadTime":280}
{"failed":3,"level":"info","message":"Batch upload completed","service":"s3-mcp-server","successful":0,"timestamp":"2025-06-24 10:27:15","total":3}
