{"level":"info","message":"Added middleware to interceptor","name":"namespace-enrichment","priority":10,"service":"s3-mcp-server","timestamp":"2025-06-24 10:58:07"}
{"level":"info","message":"Added middleware to interceptor","name":"pod-enrichment","priority":20,"service":"s3-mcp-server","timestamp":"2025-06-24 10:58:07"}
{"level":"info","message":"Added middleware to interceptor","name":"content-filter","priority":30,"service":"s3-mcp-server","timestamp":"2025-06-24 10:58:07"}
{"level":"info","message":"Added middleware to interceptor","name":"kubernetes-resource-detection","priority":15,"service":"s3-mcp-server","timestamp":"2025-06-24 10:58:07"}
{"level":"info","message":"Added middleware to interceptor","name":"kubernetes-error-detection","priority":25,"service":"s3-mcp-server","timestamp":"2025-06-24 10:58:07"}
{"level":"info","message":"Added middleware to interceptor","name":"kubernetes-metrics-extraction","priority":35,"service":"s3-mcp-server","timestamp":"2025-06-24 10:58:07"}
{"contentLength":62,"conversationId":"test-conv-1","level":"debug","message":"Intercepting AI response","service":"s3-mcp-server","timestamp":"2025-06-24 10:58:07"}
{"contentLength":62,"context":{"conversationId":"test-conv-1","requestType":"pod_log_analysis"},"id":"a0b80eb9-559b-4b01-b669-c377a1708bfc","level":"debug","message":"Processing AI response","service":"s3-mcp-server","timestamp":"2025-06-24 10:58:07"}
{"id":"a0b80eb9-559b-4b01-b669-c377a1708bfc","level":"info","message":"AI response processed","namespace":"production","namespaceConfidence":"high","namespaceSource":"command","podCount":1,"service":"s3-mcp-server","timestamp":"2025-06-24 10:58:07"}
{"contentLength":83,"conversationId":"test-conv-2","level":"debug","message":"Intercepting AI response","service":"s3-mcp-server","timestamp":"2025-06-24 10:58:07"}
{"conversationId":"test-conv-2","level":"info","message":"Detected namespace for conversation","namespace":"api-service-67890","service":"s3-mcp-server","source":"content","timestamp":"2025-06-24 10:58:07"}
{"contentLength":83,"context":{"conversationId":"test-conv-2","requestType":"log_analysis"},"id":"d5aed1fd-6e58-4eda-8a66-f353c54f2b28","level":"debug","message":"Processing AI response","service":"s3-mcp-server","timestamp":"2025-06-24 10:58:07"}
{"id":"d5aed1fd-6e58-4eda-8a66-f353c54f2b28","level":"info","message":"AI response processed","namespace":"api-service-67890","namespaceConfidence":"medium","namespaceSource":"content","podCount":0,"service":"s3-mcp-server","timestamp":"2025-06-24 10:58:07"}
{"contentLength":61,"conversationId":"test-conv-3","level":"debug","message":"Intercepting AI response","service":"s3-mcp-server","timestamp":"2025-06-24 10:58:07"}
{"contentLength":61,"context":{"conversationId":"test-conv-3","requestType":"troubleshooting"},"id":"5b6e2ff7-e245-4356-8482-56af93517f25","level":"debug","message":"Processing AI response","service":"s3-mcp-server","timestamp":"2025-06-24 10:58:07"}
{"id":"5b6e2ff7-e245-4356-8482-56af93517f25","level":"info","message":"AI response processed","namespace":"team-alpha-backend","namespaceConfidence":"high","namespaceSource":"command","podCount":0,"service":"s3-mcp-server","timestamp":"2025-06-24 10:58:07"}
{"errorCount":1,"errorTypes":["CrashLoopBackOff"],"level":"info","message":"Detected Kubernetes errors in response","responseId":"5b6e2ff7-e245-4356-8482-56af93517f25","service":"s3-mcp-server","timestamp":"2025-06-24 10:58:07"}
{"contentLength":71,"conversationId":"test-conv-4","level":"debug","message":"Intercepting AI response","service":"s3-mcp-server","timestamp":"2025-06-24 10:58:07"}
{"contentLength":71,"context":{"conversationId":"test-conv-4","requestType":"performance_analysis"},"id":"b7760da1-b296-4c20-84b8-581ca0a7a976","level":"debug","message":"Processing AI response","service":"s3-mcp-server","timestamp":"2025-06-24 10:58:07"}
{"id":"b7760da1-b296-4c20-84b8-581ca0a7a976","level":"info","message":"AI response processed","namespace":"DEV_NAMESPACE","namespaceConfidence":"low","namespaceSource":"default","podCount":1,"service":"s3-mcp-server","timestamp":"2025-06-24 10:58:07"}
{"level":"debug","message":"Detected Kubernetes resources","resourceCount":1,"resources":["Pod/feature-branch-testing-env-worker-123"],"responseId":"b7760da1-b296-4c20-84b8-581ca0a7a976","service":"s3-mcp-server","timestamp":"2025-06-24 10:58:07"}
{"contentLength":80,"conversationId":"test-conv-5","level":"debug","message":"Intercepting AI response","service":"s3-mcp-server","timestamp":"2025-06-24 10:58:07"}
{"conversationId":"test-conv-5","level":"info","message":"Detected namespace for conversation","namespace":"558bd4d5db-xyz","service":"s3-mcp-server","source":"content","timestamp":"2025-06-24 10:58:07"}
{"contentLength":80,"context":{"conversationId":"test-conv-5","requestType":"system_troubleshooting"},"id":"5d03ff2c-e1e1-4cc9-b1f9-cec47c6b3fb2","level":"debug","message":"Processing AI response","service":"s3-mcp-server","timestamp":"2025-06-24 10:58:07"}
{"id":"5d03ff2c-e1e1-4cc9-b1f9-cec47c6b3fb2","level":"info","message":"AI response processed","namespace":"kube-system","namespaceConfidence":"high","namespaceSource":"command","podCount":0,"service":"s3-mcp-server","timestamp":"2025-06-24 10:58:07"}
{"contentLength":68,"conversationId":"test-conv-6","level":"debug","message":"Intercepting AI response","service":"s3-mcp-server","timestamp":"2025-06-24 10:58:07"}
{"conversationId":"test-conv-6","level":"info","message":"Detected namespace for conversation","namespace":"observability-monitoring","service":"s3-mcp-server","source":"content","timestamp":"2025-06-24 10:58:07"}
{"contentLength":68,"context":{"conversationId":"test-conv-6","requestType":"monitoring_analysis"},"id":"d2ded860-721e-4f91-a140-a2d99f485d36","level":"debug","message":"Processing AI response","service":"s3-mcp-server","timestamp":"2025-06-24 10:58:07"}
{"id":"d2ded860-721e-4f91-a140-a2d99f485d36","level":"info","message":"AI response processed","namespace":"observability-monitoring","namespaceConfidence":"medium","namespaceSource":"content","podCount":0,"service":"s3-mcp-server","timestamp":"2025-06-24 10:58:07"}
{"contentLength":66,"conversationId":"test-conv-7","level":"debug","message":"Intercepting AI response","service":"s3-mcp-server","timestamp":"2025-06-24 10:58:07"}
{"contentLength":66,"context":{"conversationId":"test-conv-7","requestType":"deployment_troubleshooting"},"id":"a8e855f5-36da-44b4-a1da-bd1e9be44464","level":"debug","message":"Processing AI response","service":"s3-mcp-server","timestamp":"2025-06-24 10:58:07"}
{"id":"a8e855f5-36da-44b4-a1da-bd1e9be44464","level":"info","message":"AI response processed","namespace":"dev-feature-xyz-123","namespaceConfidence":"high","namespaceSource":"command","podCount":1,"service":"s3-mcp-server","timestamp":"2025-06-24 10:58:07"}
{"level":"debug","message":"Detected Kubernetes resources","resourceCount":1,"resources":["pod/-n"],"responseId":"a8e855f5-36da-44b4-a1da-bd1e9be44464","service":"s3-mcp-server","timestamp":"2025-06-24 10:58:07"}
{"errorCount":1,"errorTypes":["ImagePullBackOff"],"level":"info","message":"Detected Kubernetes errors in response","responseId":"a8e855f5-36da-44b4-a1da-bd1e9be44464","service":"s3-mcp-server","timestamp":"2025-06-24 10:58:07"}
{"contentLength":69,"conversationId":"test-conv-8","level":"debug","message":"Intercepting AI response","service":"s3-mcp-server","timestamp":"2025-06-24 10:58:07"}
{"contentLength":69,"context":{"conversationId":"test-conv-8","requestType":"customer_support"},"id":"47eea111-7d44-45d7-a708-e9167413d8fe","level":"debug","message":"Processing AI response","service":"s3-mcp-server","timestamp":"2025-06-24 10:58:07"}
{"id":"47eea111-7d44-45d7-a708-e9167413d8fe","level":"info","message":"AI response processed","namespace":"DEV_NAMESPACE","namespaceConfidence":"low","namespaceSource":"default","podCount":0,"service":"s3-mcp-server","timestamp":"2025-06-24 10:58:07"}
{"contentLength":70,"conversationId":"test-conv-9","level":"debug","message":"Intercepting AI response","service":"s3-mcp-server","timestamp":"2025-06-24 10:58:07"}
{"conversationId":"test-conv-9","level":"info","message":"Detected namespace for conversation","namespace":"context","service":"s3-mcp-server","source":"content","timestamp":"2025-06-24 10:58:07"}
{"contentLength":70,"context":{"conversationId":"test-conv-9","requestType":"general_analysis"},"id":"57bd376a-cddb-4afa-8796-2f434d4a4f8e","level":"debug","message":"Processing AI response","service":"s3-mcp-server","timestamp":"2025-06-24 10:58:07"}
{"id":"57bd376a-cddb-4afa-8796-2f434d4a4f8e","level":"info","message":"AI response processed","namespace":"context","namespaceConfidence":"medium","namespaceSource":"content","podCount":0,"service":"s3-mcp-server","timestamp":"2025-06-24 10:58:07"}
{"level":"info","message":"Starting S3 MCP Server in HTTP mode","port":3000,"service":"s3-mcp-server","timestamp":"2025-06-24 12:47:30"}
{"timestamp":"2025-06-24 12:47:30.041","level":"info","service":"s3-mcp-server","message":"Alert rule registered","component":"ErrorReporter","environment":"development","version":"1.0.0","name":"critical_errors","cooldown":300000,"enabled":true}
{"level":"info","message":"Added middleware to interceptor","name":"namespace-enrichment","priority":10,"service":"s3-mcp-server","timestamp":"2025-06-24 12:47:30"}
{"level":"info","message":"Added middleware to interceptor","name":"pod-enrichment","priority":20,"service":"s3-mcp-server","timestamp":"2025-06-24 12:47:30"}
{"level":"info","message":"Added middleware to interceptor","name":"content-filter","priority":30,"service":"s3-mcp-server","timestamp":"2025-06-24 12:47:30"}
{"level":"info","message":"Added middleware to interceptor","name":"kubernetes-resource-detection","priority":15,"service":"s3-mcp-server","timestamp":"2025-06-24 12:47:30"}
{"level":"info","message":"Added middleware to interceptor","name":"kubernetes-error-detection","priority":25,"service":"s3-mcp-server","timestamp":"2025-06-24 12:47:30"}
{"level":"info","message":"Added middleware to interceptor","name":"kubernetes-metrics-extraction","priority":35,"service":"s3-mcp-server","timestamp":"2025-06-24 12:47:30"}
{"timestamp":"2025-06-24 12:47:30.046","level":"info","service":"s3-mcp-server","message":"Alert rule registered","component":"ErrorReporter","environment":"development","version":"1.0.0","name":"high_frequency_errors","cooldown":600000,"enabled":true}
{"timestamp":"2025-06-24 12:47:30.046","level":"info","service":"s3-mcp-server","message":"Alert rule registered","component":"ErrorReporter","environment":"development","version":"1.0.0","name":"s3_connection_failures","cooldown":900000,"enabled":true}
{"timestamp":"2025-06-24 12:47:30.046","level":"info","service":"s3-mcp-server","message":"Error reporter initialized","component":"ErrorReporter","environment":"development","version":"1.0.0"}
{"timestamp":"2025-06-24 12:47:30.046","level":"info","service":"s3-mcp-server","message":"Health monitor initialized","component":"HealthMonitor","environment":"development","version":"1.0.0"}
{"timestamp":"2025-06-24 12:47:30.050","level":"info","service":"s3-mcp-server","message":"S3 Client initialized","component":"S3Client","environment":"development","version":"1.0.0","region":"us-east-1","bucket":"test-s3-bucket-development","circuitBreakerEnabled":true}
{"timestamp":"2025-06-24 12:47:30.053","level":"info","service":"s3-mcp-server","message":"Health check registered","component":"HealthMonitor","environment":"development","version":"1.0.0","name":"memory","interval":30000,"critical":true}
{"timestamp":"2025-06-24 12:47:30.053","level":"info","service":"s3-mcp-server","message":"Health check registered","component":"HealthMonitor","environment":"development","version":"1.0.0","name":"s3_connectivity","interval":60000,"critical":true}
{"timestamp":"2025-06-24 12:47:30.058","level":"info","service":"s3-mcp-server","message":"Health Check: memory is healthy","component":"memory","environment":"development","version":"1.0.0","status":"healthy","details":{"message":"Memory usage: 71.0%","duration":5,"details":{"usedMB":22,"totalMB":31,"usagePercent":70.96774193548387}},"healthCheck":true}
{"timestamp":"2025-06-24 12:47:30.058","level":"debug","service":"s3-mcp-server","message":"AWS S3 Debug @smithy/property-provider -> Not found in ENV: NODE_USE_ARN_REGION_ENV_NAME","component":"S3Client","environment":"development","version":"1.0.0"}
{"timestamp":"2025-06-24 12:47:30.060","level":"info","service":"s3-mcp-server","message":"HTTP Bridge started","component":"HTTPBridge","environment":"development","version":"1.0.0","port":3000,"endpoints":{"mcp":"http://localhost:3000/mcp","tools":"http://localhost:3000/tools/capture_ai_response","health":"http://localhost:3000/health","metrics":"http://localhost:3000/metrics"}}
{"timestamp":"2025-06-24 12:47:30.064","level":"debug","service":"s3-mcp-server","message":"AWS S3 Debug @smithy/property-provider -> Not found in config files w/ profile [default]: NODE_USE_ARN_REGION_INI_NAME","component":"S3Client","environment":"development","version":"1.0.0"}
{"timestamp":"2025-06-24 12:47:30.065","level":"debug","service":"s3-mcp-server","message":"AWS S3 Debug @smithy/property-provider -> Not found in ENV: NODE_DISABLE_S, _EXPRESS_SESSION_AUTH_ENV_NAME","component":"S3Client","environment":"development","version":"1.0.0"}
{"timestamp":"2025-06-24 12:47:30.065","level":"debug","service":"s3-mcp-server","message":"AWS S3 Debug @smithy/property-provider -> Not found in config files w/ profile [default]: NODE_DISABLE_S, _EXPRESS_SESSION_AUTH_INI_NAME","component":"S3Client","environment":"development","version":"1.0.0"}
{"timestamp":"2025-06-24 12:47:30.065","level":"debug","service":"s3-mcp-server","message":"AWS S3 Debug @smithy/property-provider -> Not found in ENV: ENV_USE_FIPS_ENDPOINT","component":"S3Client","environment":"development","version":"1.0.0"}
{"timestamp":"2025-06-24 12:47:30.065","level":"debug","service":"s3-mcp-server","message":"AWS S3 Debug @smithy/property-provider -> Not found in config files w/ profile [default]: CONFIG_USE_FIPS_ENDPOINT","component":"S3Client","environment":"development","version":"1.0.0"}
{"timestamp":"2025-06-24 12:47:30.066","level":"debug","service":"s3-mcp-server","message":"AWS S3 Debug @smithy/property-provider -> Not found in ENV: ENV_USE_DUALSTACK_ENDPOINT","component":"S3Client","environment":"development","version":"1.0.0"}
{"timestamp":"2025-06-24 12:47:30.066","level":"debug","service":"s3-mcp-server","message":"AWS S3 Debug @smithy/property-provider -> Not found in config files w/ profile [default]: CONFIG_USE_DUALSTACK_ENDPOINT","component":"S3Client","environment":"development","version":"1.0.0"}
{"timestamp":"2025-06-24 12:47:30.067","level":"debug","service":"s3-mcp-server","message":"AWS S3 Debug @smithy/property-provider -> Not found in ENV: NODE_AUTH_SCHEME_PREFERENCE_ENV_KEY","component":"S3Client","environment":"development","version":"1.0.0"}
{"timestamp":"2025-06-24 12:47:30.068","level":"debug","service":"s3-mcp-server","message":"AWS S3 Debug @smithy/property-provider -> Not found in config files w/ profile [default]: NODE_AUTH_SCHEME_PREFERENCE_CONFIG_KEY","component":"S3Client","environment":"development","version":"1.0.0"}
{"timestamp":"2025-06-24 12:47:30.069","level":"debug","service":"s3-mcp-server","message":"AWS S3 Debug @smithy/property-provider -> Not found in ENV: UA_APP_ID_ENV_NAME","component":"S3Client","environment":"development","version":"1.0.0"}
{"timestamp":"2025-06-24 12:47:30.070","level":"debug","service":"s3-mcp-server","message":"AWS S3 Debug @smithy/property-provider -> Not found in config files w/ profile [default]: UA_APP_ID_INI_NAME, UA_APP_ID_INI_NAME_DEPRECATED","component":"S3Client","environment":"development","version":"1.0.0"}
{"timestamp":"2025-06-24 12:47:30.398","level":"error","service":"s3-mcp-server","message":"AWS S3 Error [object Object]","component":"S3Client","environment":"development","version":"1.0.0"}
{"timestamp":"2025-06-24 12:47:30.399","level":"warn","service":"s3-mcp-server","message":"MCPError: S3 connection test failed: UnknownError","component":"S3Client","environment":"development","version":"1.0.0","bucket":"test-s3-bucket-development","region":"us-east-1","duration":346,"error":{"name":"MCPError","message":"S3 connection test failed: UnknownError","code":"S3_UPLOAD_FAILED","severity":"medium","context":{"operation":"testConnection","timestamp":"2025-06-24T11:47:30.399Z"},"isRetryable":false,"stack":"MCPError: S3 connection test failed: UnknownError\n    at Function.s3Error (file:///Users/<USER>/Desktop/kubera-new/hack-day/s3-mcp2/dist/src/error-handling/error-types.js:90:16)\n    at S3Client.testConnection (file:///Users/<USER>/Desktop/kubera-new/hack-day/s3-mcp2/dist/src/s3-client.js:328:43)\n    at processTicksAndRejections (node:internal/process/task_queues:96:5)\n    at async Object.check (file:///Users/<USER>/Desktop/kubera-new/hack-day/s3-mcp2/dist/src/error-handling/health-monitor.js:256:32)\n    at async HealthMonitor.runCheck (file:///Users/<USER>/Desktop/kubera-new/hack-day/s3-mcp2/dist/src/error-handling/health-monitor.js:52:28)","originalError":{"name":"403","message":"UnknownError","stack":"403: UnknownError\n    at throwDefaultError (/Users/<USER>/Desktop/kubera-new/hack-day/s3-mcp2/node_modules/@smithy/smithy-client/dist-cjs/index.js:388:20)\n    at /Users/<USER>/Desktop/kubera-new/hack-day/s3-mcp2/node_modules/@smithy/smithy-client/dist-cjs/index.js:397:5\n    at de_CommandError (/Users/<USER>/Desktop/kubera-new/hack-day/s3-mcp2/node_modules/@aws-sdk/client-s3/dist-cjs/index.js:5019:14)\n    at processTicksAndRejections (node:internal/process/task_queues:96:5)\n    at async /Users/<USER>/Desktop/kubera-new/hack-day/s3-mcp2/node_modules/@smithy/middleware-serde/dist-cjs/index.js:36:20\n    at async /Users/<USER>/Desktop/kubera-new/hack-day/s3-mcp2/node_modules/@aws-sdk/middleware-sdk-s3/dist-cjs/index.js:484:18\n    at async /Users/<USER>/Desktop/kubera-new/hack-day/s3-mcp2/node_modules/@smithy/middleware-retry/dist-cjs/index.js:320:38\n    at async /Users/<USER>/Desktop/kubera-new/hack-day/s3-mcp2/node_modules/@aws-sdk/middleware-sdk-s3/dist-cjs/index.js:110:22\n    at async /Users/<USER>/Desktop/kubera-new/hack-day/s3-mcp2/node_modules/@aws-sdk/middleware-sdk-s3/dist-cjs/index.js:137:14\n    at async /Users/<USER>/Desktop/kubera-new/hack-day/s3-mcp2/node_modules/@aws-sdk/middleware-logger/dist-cjs/index.js:33:22"}}}
{"timestamp":"2025-06-24 12:47:30.400","level":"warn","service":"s3-mcp-server","message":"Health Check: s3_connectivity is unhealthy","component":"s3_connectivity","environment":"development","version":"1.0.0","status":"unhealthy","details":{"message":"S3 connection test failed: UnknownError","duration":347,"details":{"bucket":"test-s3-bucket-development","region":"us-east-1"}},"healthCheck":true}
{"timestamp":"2025-06-24 12:48:00.055","level":"warn","service":"s3-mcp-server","message":"Health Check: memory is degraded","component":"memory","environment":"development","version":"1.0.0","status":"degraded","details":{"message":"Elevated memory usage: 84.2%","duration":0,"details":{"usedMB":16,"totalMB":19,"usagePercent":84.21052631578947}},"healthCheck":true}
{"timestamp":"2025-06-24 12:48:30.061","level":"warn","service":"s3-mcp-server","message":"Health Check: memory is degraded","component":"memory","environment":"development","version":"1.0.0","status":"degraded","details":{"message":"Elevated memory usage: 84.2%","duration":2,"details":{"usedMB":16,"totalMB":19,"usagePercent":84.21052631578947}},"healthCheck":true}
{"timestamp":"2025-06-24 12:48:30.372","level":"error","service":"s3-mcp-server","message":"AWS S3 Error [object Object]","component":"S3Client","environment":"development","version":"1.0.0"}
{"timestamp":"2025-06-24 12:48:30.374","level":"warn","service":"s3-mcp-server","message":"MCPError: S3 connection test failed: UnknownError","component":"S3Client","environment":"development","version":"1.0.0","bucket":"test-s3-bucket-development","region":"us-east-1","duration":311,"error":{"name":"MCPError","message":"S3 connection test failed: UnknownError","code":"S3_UPLOAD_FAILED","severity":"medium","context":{"operation":"testConnection","timestamp":"2025-06-24T11:48:30.374Z"},"isRetryable":false,"stack":"MCPError: S3 connection test failed: UnknownError\n    at Function.s3Error (file:///Users/<USER>/Desktop/kubera-new/hack-day/s3-mcp2/dist/src/error-handling/error-types.js:90:16)\n    at S3Client.testConnection (file:///Users/<USER>/Desktop/kubera-new/hack-day/s3-mcp2/dist/src/s3-client.js:328:43)\n    at processTicksAndRejections (node:internal/process/task_queues:96:5)\n    at async Object.check (file:///Users/<USER>/Desktop/kubera-new/hack-day/s3-mcp2/dist/src/error-handling/health-monitor.js:256:32)\n    at async HealthMonitor.runCheck (file:///Users/<USER>/Desktop/kubera-new/hack-day/s3-mcp2/dist/src/error-handling/health-monitor.js:52:28)","originalError":{"name":"403","message":"UnknownError","stack":"403: UnknownError\n    at throwDefaultError (/Users/<USER>/Desktop/kubera-new/hack-day/s3-mcp2/node_modules/@smithy/smithy-client/dist-cjs/index.js:388:20)\n    at /Users/<USER>/Desktop/kubera-new/hack-day/s3-mcp2/node_modules/@smithy/smithy-client/dist-cjs/index.js:397:5\n    at de_CommandError (/Users/<USER>/Desktop/kubera-new/hack-day/s3-mcp2/node_modules/@aws-sdk/client-s3/dist-cjs/index.js:5019:14)\n    at processTicksAndRejections (node:internal/process/task_queues:96:5)\n    at async /Users/<USER>/Desktop/kubera-new/hack-day/s3-mcp2/node_modules/@smithy/middleware-serde/dist-cjs/index.js:36:20\n    at async /Users/<USER>/Desktop/kubera-new/hack-day/s3-mcp2/node_modules/@aws-sdk/middleware-sdk-s3/dist-cjs/index.js:484:18\n    at async /Users/<USER>/Desktop/kubera-new/hack-day/s3-mcp2/node_modules/@smithy/middleware-retry/dist-cjs/index.js:320:38\n    at async /Users/<USER>/Desktop/kubera-new/hack-day/s3-mcp2/node_modules/@aws-sdk/middleware-sdk-s3/dist-cjs/index.js:110:22\n    at async /Users/<USER>/Desktop/kubera-new/hack-day/s3-mcp2/node_modules/@aws-sdk/middleware-sdk-s3/dist-cjs/index.js:137:14\n    at async /Users/<USER>/Desktop/kubera-new/hack-day/s3-mcp2/node_modules/@aws-sdk/middleware-logger/dist-cjs/index.js:33:22"}}}
{"timestamp":"2025-06-24 12:48:30.375","level":"warn","service":"s3-mcp-server","message":"Health Check: s3_connectivity is unhealthy","component":"s3_connectivity","environment":"development","version":"1.0.0","status":"unhealthy","details":{"message":"S3 connection test failed: UnknownError","duration":313,"details":{"bucket":"test-s3-bucket-development","region":"us-east-1"}},"healthCheck":true}
{"timestamp":"2025-06-24 12:49:00.059","level":"warn","service":"s3-mcp-server","message":"Health Check: memory is degraded","component":"memory","environment":"development","version":"1.0.0","status":"degraded","details":{"message":"Elevated memory usage: 89.5%","duration":0,"details":{"usedMB":17,"totalMB":19,"usagePercent":89.47368421052632}},"healthCheck":true}
{"timestamp":"2025-06-24 12:49:30.062","level":"warn","service":"s3-mcp-server","message":"Health Check: memory is degraded","component":"memory","environment":"development","version":"1.0.0","status":"degraded","details":{"message":"Elevated memory usage: 89.5%","duration":1,"details":{"usedMB":17,"totalMB":19,"usagePercent":89.47368421052632}},"healthCheck":true}
{"timestamp":"2025-06-24 12:49:30.368","level":"error","service":"s3-mcp-server","message":"AWS S3 Error [object Object]","component":"S3Client","environment":"development","version":"1.0.0"}
{"timestamp":"2025-06-24 12:49:30.369","level":"warn","service":"s3-mcp-server","message":"MCPError: S3 connection test failed: UnknownError","component":"S3Client","environment":"development","version":"1.0.0","bucket":"test-s3-bucket-development","region":"us-east-1","duration":305,"error":{"name":"MCPError","message":"S3 connection test failed: UnknownError","code":"S3_UPLOAD_FAILED","severity":"medium","context":{"operation":"testConnection","timestamp":"2025-06-24T11:49:30.369Z"},"isRetryable":false,"stack":"MCPError: S3 connection test failed: UnknownError\n    at Function.s3Error (file:///Users/<USER>/Desktop/kubera-new/hack-day/s3-mcp2/dist/src/error-handling/error-types.js:90:16)\n    at S3Client.testConnection (file:///Users/<USER>/Desktop/kubera-new/hack-day/s3-mcp2/dist/src/s3-client.js:328:43)\n    at processTicksAndRejections (node:internal/process/task_queues:96:5)\n    at async Object.check (file:///Users/<USER>/Desktop/kubera-new/hack-day/s3-mcp2/dist/src/error-handling/health-monitor.js:256:32)\n    at async HealthMonitor.runCheck (file:///Users/<USER>/Desktop/kubera-new/hack-day/s3-mcp2/dist/src/error-handling/health-monitor.js:52:28)","originalError":{"name":"403","message":"UnknownError","stack":"403: UnknownError\n    at throwDefaultError (/Users/<USER>/Desktop/kubera-new/hack-day/s3-mcp2/node_modules/@smithy/smithy-client/dist-cjs/index.js:388:20)\n    at /Users/<USER>/Desktop/kubera-new/hack-day/s3-mcp2/node_modules/@smithy/smithy-client/dist-cjs/index.js:397:5\n    at de_CommandError (/Users/<USER>/Desktop/kubera-new/hack-day/s3-mcp2/node_modules/@aws-sdk/client-s3/dist-cjs/index.js:5019:14)\n    at processTicksAndRejections (node:internal/process/task_queues:96:5)\n    at async /Users/<USER>/Desktop/kubera-new/hack-day/s3-mcp2/node_modules/@smithy/middleware-serde/dist-cjs/index.js:36:20\n    at async /Users/<USER>/Desktop/kubera-new/hack-day/s3-mcp2/node_modules/@aws-sdk/middleware-sdk-s3/dist-cjs/index.js:484:18\n    at async /Users/<USER>/Desktop/kubera-new/hack-day/s3-mcp2/node_modules/@smithy/middleware-retry/dist-cjs/index.js:320:38\n    at async /Users/<USER>/Desktop/kubera-new/hack-day/s3-mcp2/node_modules/@aws-sdk/middleware-sdk-s3/dist-cjs/index.js:110:22\n    at async /Users/<USER>/Desktop/kubera-new/hack-day/s3-mcp2/node_modules/@aws-sdk/middleware-sdk-s3/dist-cjs/index.js:137:14\n    at async /Users/<USER>/Desktop/kubera-new/hack-day/s3-mcp2/node_modules/@aws-sdk/middleware-logger/dist-cjs/index.js:33:22"}}}
{"timestamp":"2025-06-24 12:49:30.370","level":"warn","service":"s3-mcp-server","message":"Health Check: s3_connectivity is unhealthy","component":"s3_connectivity","environment":"development","version":"1.0.0","status":"unhealthy","details":{"message":"S3 connection test failed: UnknownError","duration":306,"details":{"bucket":"test-s3-bucket-development","region":"us-east-1"}},"healthCheck":true}
{"timestamp":"2025-06-24 12:50:00.063","level":"warn","service":"s3-mcp-server","message":"Health Check: memory is unhealthy","component":"memory","environment":"development","version":"1.0.0","status":"unhealthy","details":{"message":"High memory usage: 94.7%","duration":1,"details":{"usedMB":18,"totalMB":19,"usagePercent":94.73684210526315}},"healthCheck":true}
{"timestamp":"2025-06-24 12:50:30.067","level":"warn","service":"s3-mcp-server","message":"Health Check: memory is degraded","component":"memory","environment":"development","version":"1.0.0","status":"degraded","details":{"message":"Elevated memory usage: 89.5%","duration":2,"details":{"usedMB":17,"totalMB":19,"usagePercent":89.47368421052632}},"healthCheck":true}
{"timestamp":"2025-06-24 12:50:30.377","level":"error","service":"s3-mcp-server","message":"AWS S3 Error [object Object]","component":"S3Client","environment":"development","version":"1.0.0"}
{"timestamp":"2025-06-24 12:50:30.377","level":"warn","service":"s3-mcp-server","message":"MCPError: S3 connection test failed: UnknownError","component":"S3Client","environment":"development","version":"1.0.0","bucket":"test-s3-bucket-development","region":"us-east-1","duration":308,"error":{"name":"MCPError","message":"S3 connection test failed: UnknownError","code":"S3_UPLOAD_FAILED","severity":"medium","context":{"operation":"testConnection","timestamp":"2025-06-24T11:50:30.377Z"},"isRetryable":false,"stack":"MCPError: S3 connection test failed: UnknownError\n    at Function.s3Error (file:///Users/<USER>/Desktop/kubera-new/hack-day/s3-mcp2/dist/src/error-handling/error-types.js:90:16)\n    at S3Client.testConnection (file:///Users/<USER>/Desktop/kubera-new/hack-day/s3-mcp2/dist/src/s3-client.js:328:43)\n    at processTicksAndRejections (node:internal/process/task_queues:96:5)\n    at async Object.check (file:///Users/<USER>/Desktop/kubera-new/hack-day/s3-mcp2/dist/src/error-handling/health-monitor.js:256:32)\n    at async HealthMonitor.runCheck (file:///Users/<USER>/Desktop/kubera-new/hack-day/s3-mcp2/dist/src/error-handling/health-monitor.js:52:28)","originalError":{"name":"403","message":"UnknownError","stack":"403: UnknownError\n    at throwDefaultError (/Users/<USER>/Desktop/kubera-new/hack-day/s3-mcp2/node_modules/@smithy/smithy-client/dist-cjs/index.js:388:20)\n    at /Users/<USER>/Desktop/kubera-new/hack-day/s3-mcp2/node_modules/@smithy/smithy-client/dist-cjs/index.js:397:5\n    at de_CommandError (/Users/<USER>/Desktop/kubera-new/hack-day/s3-mcp2/node_modules/@aws-sdk/client-s3/dist-cjs/index.js:5019:14)\n    at processTicksAndRejections (node:internal/process/task_queues:96:5)\n    at async /Users/<USER>/Desktop/kubera-new/hack-day/s3-mcp2/node_modules/@smithy/middleware-serde/dist-cjs/index.js:36:20\n    at async /Users/<USER>/Desktop/kubera-new/hack-day/s3-mcp2/node_modules/@aws-sdk/middleware-sdk-s3/dist-cjs/index.js:484:18\n    at async /Users/<USER>/Desktop/kubera-new/hack-day/s3-mcp2/node_modules/@smithy/middleware-retry/dist-cjs/index.js:320:38\n    at async /Users/<USER>/Desktop/kubera-new/hack-day/s3-mcp2/node_modules/@aws-sdk/middleware-sdk-s3/dist-cjs/index.js:110:22\n    at async /Users/<USER>/Desktop/kubera-new/hack-day/s3-mcp2/node_modules/@aws-sdk/middleware-sdk-s3/dist-cjs/index.js:137:14\n    at async /Users/<USER>/Desktop/kubera-new/hack-day/s3-mcp2/node_modules/@aws-sdk/middleware-logger/dist-cjs/index.js:33:22"}}}
{"timestamp":"2025-06-24 12:50:30.378","level":"warn","service":"s3-mcp-server","message":"Health Check: s3_connectivity is unhealthy","component":"s3_connectivity","environment":"development","version":"1.0.0","status":"unhealthy","details":{"message":"S3 connection test failed: UnknownError","duration":309,"details":{"bucket":"test-s3-bucket-development","region":"us-east-1"}},"healthCheck":true}
{"timestamp":"2025-06-24 12:50:31.674","level":"http","service":"s3-mcp-server","message":"GET /health","component":"HTTPBridge","environment":"development","version":"1.0.0","requestId":"req_1750765831674_0286fb","userAgent":"curl/8.7.1","ip":"127.0.0.1"}
{"timestamp":"2025-06-24 12:50:31.675","level":"warn","service":"s3-mcp-server","message":"Health Check: memory is unhealthy","component":"memory","environment":"development","version":"1.0.0","status":"unhealthy","details":{"message":"High memory usage: 94.7%","duration":0,"details":{"usedMB":18,"totalMB":19,"usagePercent":94.73684210526315}},"healthCheck":true}
{"timestamp":"2025-06-24 12:50:31.766","level":"error","service":"s3-mcp-server","message":"AWS S3 Error [object Object]","component":"S3Client","environment":"development","version":"1.0.0"}
{"timestamp":"2025-06-24 12:50:31.766","level":"warn","service":"s3-mcp-server","message":"MCPError: S3 connection test failed: UnknownError","component":"S3Client","environment":"development","version":"1.0.0","bucket":"test-s3-bucket-development","region":"us-east-1","duration":91,"error":{"name":"MCPError","message":"S3 connection test failed: UnknownError","code":"S3_UPLOAD_FAILED","severity":"medium","context":{"operation":"testConnection","timestamp":"2025-06-24T11:50:31.766Z"},"isRetryable":false,"stack":"MCPError: S3 connection test failed: UnknownError\n    at Function.s3Error (file:///Users/<USER>/Desktop/kubera-new/hack-day/s3-mcp2/dist/src/error-handling/error-types.js:90:16)\n    at S3Client.testConnection (file:///Users/<USER>/Desktop/kubera-new/hack-day/s3-mcp2/dist/src/s3-client.js:328:43)\n    at processTicksAndRejections (node:internal/process/task_queues:96:5)\n    at async Object.check (file:///Users/<USER>/Desktop/kubera-new/hack-day/s3-mcp2/dist/src/error-handling/health-monitor.js:256:32)\n    at async HealthMonitor.runCheck (file:///Users/<USER>/Desktop/kubera-new/hack-day/s3-mcp2/dist/src/error-handling/health-monitor.js:52:28)\n    at async file:///Users/<USER>/Desktop/kubera-new/hack-day/s3-mcp2/dist/src/error-handling/health-monitor.js:93:32","originalError":{"name":"403","message":"UnknownError","stack":"403: UnknownError\n    at throwDefaultError (/Users/<USER>/Desktop/kubera-new/hack-day/s3-mcp2/node_modules/@smithy/smithy-client/dist-cjs/index.js:388:20)\n    at /Users/<USER>/Desktop/kubera-new/hack-day/s3-mcp2/node_modules/@smithy/smithy-client/dist-cjs/index.js:397:5\n    at de_CommandError (/Users/<USER>/Desktop/kubera-new/hack-day/s3-mcp2/node_modules/@aws-sdk/client-s3/dist-cjs/index.js:5019:14)\n    at processTicksAndRejections (node:internal/process/task_queues:96:5)\n    at async /Users/<USER>/Desktop/kubera-new/hack-day/s3-mcp2/node_modules/@smithy/middleware-serde/dist-cjs/index.js:36:20\n    at async /Users/<USER>/Desktop/kubera-new/hack-day/s3-mcp2/node_modules/@aws-sdk/middleware-sdk-s3/dist-cjs/index.js:484:18\n    at async /Users/<USER>/Desktop/kubera-new/hack-day/s3-mcp2/node_modules/@smithy/middleware-retry/dist-cjs/index.js:320:38\n    at async /Users/<USER>/Desktop/kubera-new/hack-day/s3-mcp2/node_modules/@aws-sdk/middleware-sdk-s3/dist-cjs/index.js:110:22\n    at async /Users/<USER>/Desktop/kubera-new/hack-day/s3-mcp2/node_modules/@aws-sdk/middleware-sdk-s3/dist-cjs/index.js:137:14\n    at async /Users/<USER>/Desktop/kubera-new/hack-day/s3-mcp2/node_modules/@aws-sdk/middleware-logger/dist-cjs/index.js:33:22"}}}
{"timestamp":"2025-06-24 12:50:31.766","level":"warn","service":"s3-mcp-server","message":"Health Check: s3_connectivity is unhealthy","component":"s3_connectivity","environment":"development","version":"1.0.0","status":"unhealthy","details":{"message":"S3 connection test failed: UnknownError","duration":91,"details":{"bucket":"test-s3-bucket-development","region":"us-east-1"}},"healthCheck":true}
{"timestamp":"2025-06-24 12:50:41.500","level":"http","service":"s3-mcp-server","message":"GET /","component":"HTTPBridge","environment":"development","version":"1.0.0","requestId":"req_1750765841500_xfl9cq","userAgent":"curl/8.7.1","ip":"127.0.0.1"}
{"timestamp":"2025-06-24 12:50:52.916","level":"http","service":"s3-mcp-server","message":"POST /mcp","component":"HTTPBridge","environment":"development","version":"1.0.0","requestId":"req_1750765852916_4lpzsu","userAgent":"curl/8.7.1","ip":"127.0.0.1","contentLength":"46"}
{"timestamp":"2025-06-24 12:50:52.917","level":"info","service":"s3-mcp-server","message":"Performance: MCPRequest","component":"HTTPBridge","environment":"development","version":"1.0.0","requestId":"req_1750765852916_4lpzsu","method":"tools/list","operation":"MCPRequest","duration":0,"performanceLog":true}
{"timestamp":"2025-06-24 12:51:00.067","level":"warn","service":"s3-mcp-server","message":"Health Check: memory is degraded","component":"memory","environment":"development","version":"1.0.0","status":"degraded","details":{"message":"Elevated memory usage: 90.0%","duration":0,"details":{"usedMB":18,"totalMB":20,"usagePercent":90}},"healthCheck":true}
{"timestamp":"2025-06-24 12:51:30.071","level":"warn","service":"s3-mcp-server","message":"Health Check: memory is degraded","component":"memory","environment":"development","version":"1.0.0","status":"degraded","details":{"message":"Elevated memory usage: 90.0%","duration":1,"details":{"usedMB":18,"totalMB":20,"usagePercent":90}},"healthCheck":true}
{"timestamp":"2025-06-24 12:51:30.396","level":"error","service":"s3-mcp-server","message":"AWS S3 Error [object Object]","component":"S3Client","environment":"development","version":"1.0.0"}
{"timestamp":"2025-06-24 12:51:30.397","level":"warn","service":"s3-mcp-server","message":"MCPError: S3 connection test failed: UnknownError","component":"S3Client","environment":"development","version":"1.0.0","bucket":"test-s3-bucket-development","region":"us-east-1","duration":324,"error":{"name":"MCPError","message":"S3 connection test failed: UnknownError","code":"S3_UPLOAD_FAILED","severity":"medium","context":{"operation":"testConnection","timestamp":"2025-06-24T11:51:30.397Z"},"isRetryable":false,"stack":"MCPError: S3 connection test failed: UnknownError\n    at Function.s3Error (file:///Users/<USER>/Desktop/kubera-new/hack-day/s3-mcp2/dist/src/error-handling/error-types.js:90:16)\n    at S3Client.testConnection (file:///Users/<USER>/Desktop/kubera-new/hack-day/s3-mcp2/dist/src/s3-client.js:328:43)\n    at processTicksAndRejections (node:internal/process/task_queues:96:5)\n    at async Object.check (file:///Users/<USER>/Desktop/kubera-new/hack-day/s3-mcp2/dist/src/error-handling/health-monitor.js:256:32)\n    at async HealthMonitor.runCheck (file:///Users/<USER>/Desktop/kubera-new/hack-day/s3-mcp2/dist/src/error-handling/health-monitor.js:52:28)","originalError":{"name":"403","message":"UnknownError","stack":"403: UnknownError\n    at throwDefaultError (/Users/<USER>/Desktop/kubera-new/hack-day/s3-mcp2/node_modules/@smithy/smithy-client/dist-cjs/index.js:388:20)\n    at /Users/<USER>/Desktop/kubera-new/hack-day/s3-mcp2/node_modules/@smithy/smithy-client/dist-cjs/index.js:397:5\n    at de_CommandError (/Users/<USER>/Desktop/kubera-new/hack-day/s3-mcp2/node_modules/@aws-sdk/client-s3/dist-cjs/index.js:5019:14)\n    at processTicksAndRejections (node:internal/process/task_queues:96:5)\n    at async /Users/<USER>/Desktop/kubera-new/hack-day/s3-mcp2/node_modules/@smithy/middleware-serde/dist-cjs/index.js:36:20\n    at async /Users/<USER>/Desktop/kubera-new/hack-day/s3-mcp2/node_modules/@aws-sdk/middleware-sdk-s3/dist-cjs/index.js:484:18\n    at async /Users/<USER>/Desktop/kubera-new/hack-day/s3-mcp2/node_modules/@smithy/middleware-retry/dist-cjs/index.js:320:38\n    at async /Users/<USER>/Desktop/kubera-new/hack-day/s3-mcp2/node_modules/@aws-sdk/middleware-sdk-s3/dist-cjs/index.js:110:22\n    at async /Users/<USER>/Desktop/kubera-new/hack-day/s3-mcp2/node_modules/@aws-sdk/middleware-sdk-s3/dist-cjs/index.js:137:14\n    at async /Users/<USER>/Desktop/kubera-new/hack-day/s3-mcp2/node_modules/@aws-sdk/middleware-logger/dist-cjs/index.js:33:22"}}}
{"timestamp":"2025-06-24 12:51:30.398","level":"warn","service":"s3-mcp-server","message":"Health Check: s3_connectivity is unhealthy","component":"s3_connectivity","environment":"development","version":"1.0.0","status":"unhealthy","details":{"message":"S3 connection test failed: UnknownError","duration":325,"details":{"bucket":"test-s3-bucket-development","region":"us-east-1"}},"healthCheck":true}
{"timestamp":"2025-06-24 12:52:00.016","level":"warn","service":"s3-mcp-server","message":"Health Check: memory is degraded","component":"memory","environment":"development","version":"1.0.0","status":"degraded","details":{"message":"Elevated memory usage: 90.0%","duration":2,"details":{"usedMB":18,"totalMB":20,"usagePercent":90}},"healthCheck":true}
{"timestamp":"2025-06-24 12:52:30.020","level":"warn","service":"s3-mcp-server","message":"Health Check: memory is degraded","component":"memory","environment":"development","version":"1.0.0","status":"degraded","details":{"message":"Elevated memory usage: 90.0%","duration":1,"details":{"usedMB":18,"totalMB":20,"usagePercent":90}},"healthCheck":true}
{"timestamp":"2025-06-24 12:52:30.307","level":"error","service":"s3-mcp-server","message":"AWS S3 Error [object Object]","component":"S3Client","environment":"development","version":"1.0.0"}
{"timestamp":"2025-06-24 12:52:30.308","level":"warn","service":"s3-mcp-server","message":"MCPError: S3 connection test failed: UnknownError","component":"S3Client","environment":"development","version":"1.0.0","bucket":"test-s3-bucket-development","region":"us-east-1","duration":282,"error":{"name":"MCPError","message":"S3 connection test failed: UnknownError","code":"S3_UPLOAD_FAILED","severity":"medium","context":{"operation":"testConnection","timestamp":"2025-06-24T11:52:30.308Z"},"isRetryable":false,"stack":"MCPError: S3 connection test failed: UnknownError\n    at Function.s3Error (file:///Users/<USER>/Desktop/kubera-new/hack-day/s3-mcp2/dist/src/error-handling/error-types.js:90:16)\n    at S3Client.testConnection (file:///Users/<USER>/Desktop/kubera-new/hack-day/s3-mcp2/dist/src/s3-client.js:328:43)\n    at processTicksAndRejections (node:internal/process/task_queues:96:5)\n    at async Object.check (file:///Users/<USER>/Desktop/kubera-new/hack-day/s3-mcp2/dist/src/error-handling/health-monitor.js:256:32)\n    at async HealthMonitor.runCheck (file:///Users/<USER>/Desktop/kubera-new/hack-day/s3-mcp2/dist/src/error-handling/health-monitor.js:52:28)","originalError":{"name":"403","message":"UnknownError","stack":"403: UnknownError\n    at throwDefaultError (/Users/<USER>/Desktop/kubera-new/hack-day/s3-mcp2/node_modules/@smithy/smithy-client/dist-cjs/index.js:388:20)\n    at /Users/<USER>/Desktop/kubera-new/hack-day/s3-mcp2/node_modules/@smithy/smithy-client/dist-cjs/index.js:397:5\n    at de_CommandError (/Users/<USER>/Desktop/kubera-new/hack-day/s3-mcp2/node_modules/@aws-sdk/client-s3/dist-cjs/index.js:5019:14)\n    at processTicksAndRejections (node:internal/process/task_queues:96:5)\n    at async /Users/<USER>/Desktop/kubera-new/hack-day/s3-mcp2/node_modules/@smithy/middleware-serde/dist-cjs/index.js:36:20\n    at async /Users/<USER>/Desktop/kubera-new/hack-day/s3-mcp2/node_modules/@aws-sdk/middleware-sdk-s3/dist-cjs/index.js:484:18\n    at async /Users/<USER>/Desktop/kubera-new/hack-day/s3-mcp2/node_modules/@smithy/middleware-retry/dist-cjs/index.js:320:38\n    at async /Users/<USER>/Desktop/kubera-new/hack-day/s3-mcp2/node_modules/@aws-sdk/middleware-sdk-s3/dist-cjs/index.js:110:22\n    at async /Users/<USER>/Desktop/kubera-new/hack-day/s3-mcp2/node_modules/@aws-sdk/middleware-sdk-s3/dist-cjs/index.js:137:14\n    at async /Users/<USER>/Desktop/kubera-new/hack-day/s3-mcp2/node_modules/@aws-sdk/middleware-logger/dist-cjs/index.js:33:22"}}}
{"timestamp":"2025-06-24 12:52:30.308","level":"warn","service":"s3-mcp-server","message":"Health Check: s3_connectivity is unhealthy","component":"s3_connectivity","environment":"development","version":"1.0.0","status":"unhealthy","details":{"message":"S3 connection test failed: UnknownError","duration":282,"details":{"bucket":"test-s3-bucket-development","region":"us-east-1"}},"healthCheck":true}
{"timestamp":"2025-06-24 12:53:00.018","level":"warn","service":"s3-mcp-server","message":"Health Check: memory is degraded","component":"memory","environment":"development","version":"1.0.0","status":"degraded","details":{"message":"Elevated memory usage: 90.0%","duration":0,"details":{"usedMB":18,"totalMB":20,"usagePercent":90}},"healthCheck":true}
{"timestamp":"2025-06-24 12:53:30.021","level":"warn","service":"s3-mcp-server","message":"Health Check: memory is degraded","component":"memory","environment":"development","version":"1.0.0","status":"degraded","details":{"message":"Elevated memory usage: 90.0%","duration":1,"details":{"usedMB":18,"totalMB":20,"usagePercent":90}},"healthCheck":true}
{"timestamp":"2025-06-24 12:53:30.360","level":"error","service":"s3-mcp-server","message":"AWS S3 Error [object Object]","component":"S3Client","environment":"development","version":"1.0.0"}
{"timestamp":"2025-06-24 12:53:30.361","level":"warn","service":"s3-mcp-server","message":"MCPError: S3 connection test failed: UnknownError","component":"S3Client","environment":"development","version":"1.0.0","bucket":"test-s3-bucket-development","region":"us-east-1","duration":335,"error":{"name":"MCPError","message":"S3 connection test failed: UnknownError","code":"S3_UPLOAD_FAILED","severity":"medium","context":{"operation":"testConnection","timestamp":"2025-06-24T11:53:30.361Z"},"isRetryable":false,"stack":"MCPError: S3 connection test failed: UnknownError\n    at Function.s3Error (file:///Users/<USER>/Desktop/kubera-new/hack-day/s3-mcp2/dist/src/error-handling/error-types.js:90:16)\n    at S3Client.testConnection (file:///Users/<USER>/Desktop/kubera-new/hack-day/s3-mcp2/dist/src/s3-client.js:328:43)\n    at processTicksAndRejections (node:internal/process/task_queues:96:5)\n    at async Object.check (file:///Users/<USER>/Desktop/kubera-new/hack-day/s3-mcp2/dist/src/error-handling/health-monitor.js:256:32)\n    at async HealthMonitor.runCheck (file:///Users/<USER>/Desktop/kubera-new/hack-day/s3-mcp2/dist/src/error-handling/health-monitor.js:52:28)","originalError":{"name":"403","message":"UnknownError","stack":"403: UnknownError\n    at throwDefaultError (/Users/<USER>/Desktop/kubera-new/hack-day/s3-mcp2/node_modules/@smithy/smithy-client/dist-cjs/index.js:388:20)\n    at /Users/<USER>/Desktop/kubera-new/hack-day/s3-mcp2/node_modules/@smithy/smithy-client/dist-cjs/index.js:397:5\n    at de_CommandError (/Users/<USER>/Desktop/kubera-new/hack-day/s3-mcp2/node_modules/@aws-sdk/client-s3/dist-cjs/index.js:5019:14)\n    at processTicksAndRejections (node:internal/process/task_queues:96:5)\n    at async /Users/<USER>/Desktop/kubera-new/hack-day/s3-mcp2/node_modules/@smithy/middleware-serde/dist-cjs/index.js:36:20\n    at async /Users/<USER>/Desktop/kubera-new/hack-day/s3-mcp2/node_modules/@aws-sdk/middleware-sdk-s3/dist-cjs/index.js:484:18\n    at async /Users/<USER>/Desktop/kubera-new/hack-day/s3-mcp2/node_modules/@smithy/middleware-retry/dist-cjs/index.js:320:38\n    at async /Users/<USER>/Desktop/kubera-new/hack-day/s3-mcp2/node_modules/@aws-sdk/middleware-sdk-s3/dist-cjs/index.js:110:22\n    at async /Users/<USER>/Desktop/kubera-new/hack-day/s3-mcp2/node_modules/@aws-sdk/middleware-sdk-s3/dist-cjs/index.js:137:14\n    at async /Users/<USER>/Desktop/kubera-new/hack-day/s3-mcp2/node_modules/@aws-sdk/middleware-logger/dist-cjs/index.js:33:22"}}}
{"timestamp":"2025-06-24 12:53:30.362","level":"warn","service":"s3-mcp-server","message":"Health Check: s3_connectivity is unhealthy","component":"s3_connectivity","environment":"development","version":"1.0.0","status":"unhealthy","details":{"message":"S3 connection test failed: UnknownError","duration":336,"details":{"bucket":"test-s3-bucket-development","region":"us-east-1"}},"healthCheck":true}
{"timestamp":"2025-06-24 12:53:56.104","level":"http","service":"s3-mcp-server","message":"POST /mcp","component":"HTTPBridge","environment":"development","version":"1.0.0","requestId":"req_1750766036104_zahhjr","userAgent":"curl/8.7.1","ip":"127.0.0.1","contentLength":"256"}
{"contentLength":79,"conversationId":"test-123","level":"debug","message":"Intercepting AI response","service":"s3-mcp-server","timestamp":"2025-06-24 12:53:56"}
{"timestamp":"2025-06-24 12:53:56.105","level":"info","service":"s3-mcp-server","message":"Processing AI response capture via HTTP","component":"HTTPBridge","environment":"development","version":"1.0.0","contentLength":79,"conversationId":"test-123"}
{"timestamp":"2025-06-24 12:53:56.109","level":"debug","service":"s3-mcp-server","message":"Uploading response to S3","component":"S3Client","environment":"development","version":"1.0.0","key":"dev-mcp-responses/production/2025-06-24/production_20250624_115356_70157f72.txt","bucket":"test-s3-bucket-development","contentLength":469,"namespace":"production","responseId":"70157f72-8eff-40fe-8f1c-4287014110bb"}
{"timestamp":"2025-06-24 12:53:56.110","level":"debug","service":"s3-mcp-server","message":"AWS S3 Debug @smithy/property-provider -> Not found in ENV: ENV_REQUEST_CHECKSUM_CALCULATION","component":"S3Client","environment":"development","version":"1.0.0"}
{"timestamp":"2025-06-24 12:53:56.111","level":"debug","service":"s3-mcp-server","message":"AWS S3 Debug @smithy/property-provider -> Not found in config files w/ profile [default]: CONFIG_REQUEST_CHECKSUM_CALCULATION","component":"S3Client","environment":"development","version":"1.0.0"}
{"timestamp":"2025-06-24 12:53:56.111","level":"debug","service":"s3-mcp-server","message":"AWS S3 Debug @smithy/property-provider -> Not found in ENV: ENV_RESPONSE_CHECKSUM_VALIDATION","component":"S3Client","environment":"development","version":"1.0.0"}
{"timestamp":"2025-06-24 12:53:56.111","level":"debug","service":"s3-mcp-server","message":"AWS S3 Debug @smithy/property-provider -> Not found in config files w/ profile [default]: CONFIG_RESPONSE_CHECKSUM_VALIDATION","component":"S3Client","environment":"development","version":"1.0.0"}
{"contentLength":79,"context":{"conversationId":"test-123","requestType":"pod_troubleshooting"},"id":"70157f72-8eff-40fe-8f1c-4287014110bb","level":"debug","message":"Processing AI response","service":"s3-mcp-server","timestamp":"2025-06-24 12:53:56"}
{"id":"70157f72-8eff-40fe-8f1c-4287014110bb","level":"info","message":"AI response processed","namespace":"production","namespaceConfidence":"high","namespaceSource":"command","podCount":1,"service":"s3-mcp-server","timestamp":"2025-06-24 12:53:56"}
{"timestamp":"2025-06-24 12:53:56.386","level":"error","service":"s3-mcp-server","message":"AWS S3 Error [object Object]","component":"S3Client","environment":"development","version":"1.0.0"}
{"timestamp":"2025-06-24 12:53:56.386","level":"warn","service":"s3-mcp-server","message":"MCPError: Failed to upload response to S3: The AWS Access Key Id you provided does not exist in our records.","component":"S3Client","environment":"development","version":"1.0.0","responseId":"70157f72-8eff-40fe-8f1c-4287014110bb","namespace":"production","uploadTime":278,"error":{"name":"MCPError","message":"Failed to upload response to S3: The AWS Access Key Id you provided does not exist in our records.","code":"S3_UPLOAD_FAILED","severity":"medium","context":{"requestId":"70157f72-8eff-40fe-8f1c-4287014110bb","conversationId":"test-123","namespace":"production","operation":"uploadResponse","timestamp":"2025-06-24T11:53:56.386Z"},"isRetryable":false,"stack":"MCPError: Failed to upload response to S3: The AWS Access Key Id you provided does not exist in our records.\n    at Function.s3Error (file:///Users/<USER>/Desktop/kubera-new/hack-day/s3-mcp2/dist/src/error-handling/error-types.js:90:16)\n    at S3Client.uploadResponse (file:///Users/<USER>/Desktop/kubera-new/hack-day/s3-mcp2/dist/src/s3-client.js:156:43)\n    at processTicksAndRejections (node:internal/process/task_queues:96:5)\n    at async HTTPBridge.handleCaptureResponse (file:///Users/<USER>/Desktop/kubera-new/hack-day/s3-mcp2/dist/src/http-bridge.js:345:34)\n    at async file:///Users/<USER>/Desktop/kubera-new/hack-day/s3-mcp2/dist/src/http-bridge.js:151:40","originalError":{"name":"InvalidAccessKeyId","message":"The AWS Access Key Id you provided does not exist in our records.","stack":"InvalidAccessKeyId: The AWS Access Key Id you provided does not exist in our records.\n    at throwDefaultError (/Users/<USER>/Desktop/kubera-new/hack-day/s3-mcp2/node_modules/@smithy/smithy-client/dist-cjs/index.js:388:20)\n    at /Users/<USER>/Desktop/kubera-new/hack-day/s3-mcp2/node_modules/@smithy/smithy-client/dist-cjs/index.js:397:5\n    at de_CommandError (/Users/<USER>/Desktop/kubera-new/hack-day/s3-mcp2/node_modules/@aws-sdk/client-s3/dist-cjs/index.js:5019:14)\n    at processTicksAndRejections (node:internal/process/task_queues:96:5)\n    at async /Users/<USER>/Desktop/kubera-new/hack-day/s3-mcp2/node_modules/@smithy/middleware-serde/dist-cjs/index.js:36:20\n    at async /Users/<USER>/Desktop/kubera-new/hack-day/s3-mcp2/node_modules/@aws-sdk/middleware-sdk-s3/dist-cjs/index.js:484:18\n    at async /Users/<USER>/Desktop/kubera-new/hack-day/s3-mcp2/node_modules/@smithy/middleware-retry/dist-cjs/index.js:320:38\n    at async /Users/<USER>/Desktop/kubera-new/hack-day/s3-mcp2/node_modules/@aws-sdk/middleware-flexible-checksums/dist-cjs/index.js:318:18\n    at async /Users/<USER>/Desktop/kubera-new/hack-day/s3-mcp2/node_modules/@aws-sdk/middleware-sdk-s3/dist-cjs/index.js:110:22\n    at async /Users/<USER>/Desktop/kubera-new/hack-day/s3-mcp2/node_modules/@aws-sdk/middleware-sdk-s3/dist-cjs/index.js:137:14"}}}
{"timestamp":"2025-06-24 12:53:56.387","level":"warn","service":"s3-mcp-server","message":"MCPError: Failed to store AI response in S3: Failed to upload response to S3: The AWS Access Key Id you provided does not exist in our records.","component":"HTTPBridge","environment":"development","version":"1.0.0","responseId":"70157f72-8eff-40fe-8f1c-4287014110bb","duration":282,"error":{"name":"MCPError","message":"Failed to store AI response in S3: Failed to upload response to S3: The AWS Access Key Id you provided does not exist in our records.","code":"S3_UPLOAD_FAILED","severity":"medium","context":{"operation":"captureAIResponse","requestId":"70157f72-8eff-40fe-8f1c-4287014110bb","namespace":"production","timestamp":"2025-06-24T11:53:56.387Z"},"isRetryable":true,"stack":"MCPError: Failed to store AI response in S3: Failed to upload response to S3: The AWS Access Key Id you provided does not exist in our records.\n    at Function.s3Error (file:///Users/<USER>/Desktop/kubera-new/hack-day/s3-mcp2/dist/src/error-handling/error-types.js:90:16)\n    at HTTPBridge.handleCaptureResponse (file:///Users/<USER>/Desktop/kubera-new/hack-day/s3-mcp2/dist/src/http-bridge.js:383:44)\n    at processTicksAndRejections (node:internal/process/task_queues:96:5)\n    at async file:///Users/<USER>/Desktop/kubera-new/hack-day/s3-mcp2/dist/src/http-bridge.js:151:40"}}
{"timestamp":"2025-06-24 12:53:56.387","level":"warn","service":"s3-mcp-server","message":"MCPError: Failed to store AI response in S3: Failed to upload response to S3: The AWS Access Key Id you provided does not exist in our records.","component":"ErrorReporter","environment":"development","version":"1.0.0","operation":"captureAIResponse","responseId":"70157f72-8eff-40fe-8f1c-4287014110bb","reportId":"err_1750766036387_90poyp","fingerprint":"MCPError|S3_UPLOAD_FAILED|captureAIResponse|Failed_to_store_AI_response_in_S3__Failed_to_upload_response_to_S3__The_AWS_Access_Key_Id_you_provid","count":1,"error":{"name":"MCPError","message":"Failed to store AI response in S3: Failed to upload response to S3: The AWS Access Key Id you provided does not exist in our records.","code":"S3_UPLOAD_FAILED","severity":"medium","context":{"operation":"captureAIResponse","requestId":"70157f72-8eff-40fe-8f1c-4287014110bb","namespace":"production","timestamp":"2025-06-24T11:53:56.387Z"},"isRetryable":true,"stack":"MCPError: Failed to store AI response in S3: Failed to upload response to S3: The AWS Access Key Id you provided does not exist in our records.\n    at Function.s3Error (file:///Users/<USER>/Desktop/kubera-new/hack-day/s3-mcp2/dist/src/error-handling/error-types.js:90:16)\n    at HTTPBridge.handleCaptureResponse (file:///Users/<USER>/Desktop/kubera-new/hack-day/s3-mcp2/dist/src/http-bridge.js:383:44)\n    at processTicksAndRejections (node:internal/process/task_queues:96:5)\n    at async file:///Users/<USER>/Desktop/kubera-new/hack-day/s3-mcp2/dist/src/http-bridge.js:151:40"}}
{"timestamp":"2025-06-24 12:53:56.388","level":"info","service":"s3-mcp-server","message":"Performance: MCPRequest","component":"HTTPBridge","environment":"development","version":"1.0.0","requestId":"req_1750766036104_zahhjr","method":"tools/call","toolName":"capture_ai_response","operation":"MCPRequest","duration":283,"performanceLog":true}
{"timestamp":"2025-06-24 12:54:00.020","level":"warn","service":"s3-mcp-server","message":"Health Check: memory is unhealthy","component":"memory","environment":"development","version":"1.0.0","status":"unhealthy","details":{"message":"High memory usage: 95.0%","duration":0,"details":{"usedMB":19,"totalMB":20,"usagePercent":95}},"healthCheck":true}
{"timestamp":"2025-06-24 12:54:09.361","level":"http","service":"s3-mcp-server","message":"POST /tools/capture_ai_response","component":"HTTPBridge","environment":"development","version":"1.0.0","requestId":"req_1750766049360_tsuhhq","userAgent":"curl/8.7.1","ip":"127.0.0.1","contentLength":"137"}
{"contentLength":58,"conversationId":"test-456","level":"debug","message":"Intercepting AI response","service":"s3-mcp-server","timestamp":"2025-06-24 12:54:09"}
{"timestamp":"2025-06-24 12:54:09.361","level":"info","service":"s3-mcp-server","message":"Processing AI response capture via HTTP","component":"HTTPBridge","environment":"development","version":"1.0.0","contentLength":58,"conversationId":"test-456"}
{"timestamp":"2025-06-24 12:54:09.363","level":"debug","service":"s3-mcp-server","message":"Uploading response to S3","component":"S3Client","environment":"development","version":"1.0.0","key":"dev-mcp-responses/staging/2025-06-24/staging_20250624_115409_4036380f.txt","bucket":"test-s3-bucket-development","contentLength":428,"namespace":"staging","responseId":"4036380f-7538-4630-a15d-f0ad99234e0d"}
{"contentLength":58,"context":{"conversationId":"test-456","requestType":"performance_analysis"},"id":"4036380f-7538-4630-a15d-f0ad99234e0d","level":"debug","message":"Processing AI response","service":"s3-mcp-server","timestamp":"2025-06-24 12:54:09"}
{"id":"4036380f-7538-4630-a15d-f0ad99234e0d","level":"info","message":"AI response processed","namespace":"staging","namespaceConfidence":"high","namespaceSource":"command","podCount":0,"service":"s3-mcp-server","timestamp":"2025-06-24 12:54:09"}
{"timestamp":"2025-06-24 12:54:09.639","level":"error","service":"s3-mcp-server","message":"AWS S3 Error [object Object]","component":"S3Client","environment":"development","version":"1.0.0"}
{"timestamp":"2025-06-24 12:54:09.640","level":"warn","service":"s3-mcp-server","message":"MCPError: Failed to upload response to S3: The AWS Access Key Id you provided does not exist in our records.","component":"S3Client","environment":"development","version":"1.0.0","responseId":"4036380f-7538-4630-a15d-f0ad99234e0d","namespace":"staging","uploadTime":277,"error":{"name":"MCPError","message":"Failed to upload response to S3: The AWS Access Key Id you provided does not exist in our records.","code":"S3_UPLOAD_FAILED","severity":"medium","context":{"requestId":"4036380f-7538-4630-a15d-f0ad99234e0d","conversationId":"test-456","namespace":"staging","operation":"uploadResponse","timestamp":"2025-06-24T11:54:09.640Z"},"isRetryable":false,"stack":"MCPError: Failed to upload response to S3: The AWS Access Key Id you provided does not exist in our records.\n    at Function.s3Error (file:///Users/<USER>/Desktop/kubera-new/hack-day/s3-mcp2/dist/src/error-handling/error-types.js:90:16)\n    at S3Client.uploadResponse (file:///Users/<USER>/Desktop/kubera-new/hack-day/s3-mcp2/dist/src/s3-client.js:156:43)\n    at processTicksAndRejections (node:internal/process/task_queues:96:5)\n    at async HTTPBridge.handleCaptureResponse (file:///Users/<USER>/Desktop/kubera-new/hack-day/s3-mcp2/dist/src/http-bridge.js:345:34)\n    at async file:///Users/<USER>/Desktop/kubera-new/hack-day/s3-mcp2/dist/src/http-bridge.js:202:32","originalError":{"name":"InvalidAccessKeyId","message":"The AWS Access Key Id you provided does not exist in our records.","stack":"InvalidAccessKeyId: The AWS Access Key Id you provided does not exist in our records.\n    at throwDefaultError (/Users/<USER>/Desktop/kubera-new/hack-day/s3-mcp2/node_modules/@smithy/smithy-client/dist-cjs/index.js:388:20)\n    at /Users/<USER>/Desktop/kubera-new/hack-day/s3-mcp2/node_modules/@smithy/smithy-client/dist-cjs/index.js:397:5\n    at de_CommandError (/Users/<USER>/Desktop/kubera-new/hack-day/s3-mcp2/node_modules/@aws-sdk/client-s3/dist-cjs/index.js:5019:14)\n    at processTicksAndRejections (node:internal/process/task_queues:96:5)\n    at async /Users/<USER>/Desktop/kubera-new/hack-day/s3-mcp2/node_modules/@smithy/middleware-serde/dist-cjs/index.js:36:20\n    at async /Users/<USER>/Desktop/kubera-new/hack-day/s3-mcp2/node_modules/@aws-sdk/middleware-sdk-s3/dist-cjs/index.js:484:18\n    at async /Users/<USER>/Desktop/kubera-new/hack-day/s3-mcp2/node_modules/@smithy/middleware-retry/dist-cjs/index.js:320:38\n    at async /Users/<USER>/Desktop/kubera-new/hack-day/s3-mcp2/node_modules/@aws-sdk/middleware-flexible-checksums/dist-cjs/index.js:318:18\n    at async /Users/<USER>/Desktop/kubera-new/hack-day/s3-mcp2/node_modules/@aws-sdk/middleware-sdk-s3/dist-cjs/index.js:110:22\n    at async /Users/<USER>/Desktop/kubera-new/hack-day/s3-mcp2/node_modules/@aws-sdk/middleware-sdk-s3/dist-cjs/index.js:137:14"}}}
{"timestamp":"2025-06-24 12:54:09.640","level":"warn","service":"s3-mcp-server","message":"MCPError: Failed to store AI response in S3: Failed to upload response to S3: The AWS Access Key Id you provided does not exist in our records.","component":"HTTPBridge","environment":"development","version":"1.0.0","responseId":"4036380f-7538-4630-a15d-f0ad99234e0d","duration":279,"error":{"name":"MCPError","message":"Failed to store AI response in S3: Failed to upload response to S3: The AWS Access Key Id you provided does not exist in our records.","code":"S3_UPLOAD_FAILED","severity":"medium","context":{"operation":"captureAIResponse","requestId":"4036380f-7538-4630-a15d-f0ad99234e0d","namespace":"staging","timestamp":"2025-06-24T11:54:09.640Z"},"isRetryable":true,"stack":"MCPError: Failed to store AI response in S3: Failed to upload response to S3: The AWS Access Key Id you provided does not exist in our records.\n    at Function.s3Error (file:///Users/<USER>/Desktop/kubera-new/hack-day/s3-mcp2/dist/src/error-handling/error-types.js:90:16)\n    at HTTPBridge.handleCaptureResponse (file:///Users/<USER>/Desktop/kubera-new/hack-day/s3-mcp2/dist/src/http-bridge.js:383:44)\n    at processTicksAndRejections (node:internal/process/task_queues:96:5)\n    at async file:///Users/<USER>/Desktop/kubera-new/hack-day/s3-mcp2/dist/src/http-bridge.js:202:32"}}
{"timestamp":"2025-06-24 12:54:09.640","level":"warn","service":"s3-mcp-server","message":"MCPError: Failed to store AI response in S3: Failed to upload response to S3: The AWS Access Key Id you provided does not exist in our records.","component":"ErrorReporter","environment":"development","version":"1.0.0","operation":"captureAIResponse","responseId":"4036380f-7538-4630-a15d-f0ad99234e0d","reportId":"err_1750766036387_90poyp","fingerprint":"MCPError|S3_UPLOAD_FAILED|captureAIResponse|Failed_to_store_AI_response_in_S3__Failed_to_upload_response_to_S3__The_AWS_Access_Key_Id_you_provid","count":2,"error":{"name":"MCPError","message":"Failed to store AI response in S3: Failed to upload response to S3: The AWS Access Key Id you provided does not exist in our records.","code":"S3_UPLOAD_FAILED","severity":"medium","context":{"operation":"captureAIResponse","requestId":"4036380f-7538-4630-a15d-f0ad99234e0d","namespace":"staging","timestamp":"2025-06-24T11:54:09.640Z"},"isRetryable":true,"stack":"MCPError: Failed to store AI response in S3: Failed to upload response to S3: The AWS Access Key Id you provided does not exist in our records.\n    at Function.s3Error (file:///Users/<USER>/Desktop/kubera-new/hack-day/s3-mcp2/dist/src/error-handling/error-types.js:90:16)\n    at HTTPBridge.handleCaptureResponse (file:///Users/<USER>/Desktop/kubera-new/hack-day/s3-mcp2/dist/src/http-bridge.js:383:44)\n    at processTicksAndRejections (node:internal/process/task_queues:96:5)\n    at async file:///Users/<USER>/Desktop/kubera-new/hack-day/s3-mcp2/dist/src/http-bridge.js:202:32"}}
{"timestamp":"2025-06-24 12:54:09.641","level":"info","service":"s3-mcp-server","message":"Performance: DirectToolCall","component":"HTTPBridge","environment":"development","version":"1.0.0","requestId":"req_1750766049360_tsuhhq","contentLength":58,"operation":"DirectToolCall","duration":280,"performanceLog":true}
{"timestamp":"2025-06-24 12:54:30.026","level":"warn","service":"s3-mcp-server","message":"Health Check: memory is unhealthy","component":"memory","environment":"development","version":"1.0.0","status":"unhealthy","details":{"message":"High memory usage: 90.5%","duration":4,"details":{"usedMB":19,"totalMB":21,"usagePercent":90.47619047619048}},"healthCheck":true}
{"timestamp":"2025-06-24 12:54:30.316","level":"error","service":"s3-mcp-server","message":"AWS S3 Error [object Object]","component":"S3Client","environment":"development","version":"1.0.0"}
{"timestamp":"2025-06-24 12:54:30.320","level":"warn","service":"s3-mcp-server","message":"MCPError: S3 connection test failed: UnknownError","component":"S3Client","environment":"development","version":"1.0.0","bucket":"test-s3-bucket-development","region":"us-east-1","duration":292,"error":{"name":"MCPError","message":"S3 connection test failed: UnknownError","code":"S3_UPLOAD_FAILED","severity":"medium","context":{"operation":"testConnection","timestamp":"2025-06-24T11:54:30.320Z"},"isRetryable":false,"stack":"MCPError: S3 connection test failed: UnknownError\n    at Function.s3Error (file:///Users/<USER>/Desktop/kubera-new/hack-day/s3-mcp2/dist/src/error-handling/error-types.js:90:16)\n    at S3Client.testConnection (file:///Users/<USER>/Desktop/kubera-new/hack-day/s3-mcp2/dist/src/s3-client.js:328:43)\n    at processTicksAndRejections (node:internal/process/task_queues:96:5)\n    at async Object.check (file:///Users/<USER>/Desktop/kubera-new/hack-day/s3-mcp2/dist/src/error-handling/health-monitor.js:256:32)\n    at async HealthMonitor.runCheck (file:///Users/<USER>/Desktop/kubera-new/hack-day/s3-mcp2/dist/src/error-handling/health-monitor.js:52:28)","originalError":{"name":"403","message":"UnknownError","stack":"403: UnknownError\n    at throwDefaultError (/Users/<USER>/Desktop/kubera-new/hack-day/s3-mcp2/node_modules/@smithy/smithy-client/dist-cjs/index.js:388:20)\n    at /Users/<USER>/Desktop/kubera-new/hack-day/s3-mcp2/node_modules/@smithy/smithy-client/dist-cjs/index.js:397:5\n    at de_CommandError (/Users/<USER>/Desktop/kubera-new/hack-day/s3-mcp2/node_modules/@aws-sdk/client-s3/dist-cjs/index.js:5019:14)\n    at processTicksAndRejections (node:internal/process/task_queues:96:5)\n    at async /Users/<USER>/Desktop/kubera-new/hack-day/s3-mcp2/node_modules/@smithy/middleware-serde/dist-cjs/index.js:36:20\n    at async /Users/<USER>/Desktop/kubera-new/hack-day/s3-mcp2/node_modules/@aws-sdk/middleware-sdk-s3/dist-cjs/index.js:484:18\n    at async /Users/<USER>/Desktop/kubera-new/hack-day/s3-mcp2/node_modules/@smithy/middleware-retry/dist-cjs/index.js:320:38\n    at async /Users/<USER>/Desktop/kubera-new/hack-day/s3-mcp2/node_modules/@aws-sdk/middleware-sdk-s3/dist-cjs/index.js:110:22\n    at async /Users/<USER>/Desktop/kubera-new/hack-day/s3-mcp2/node_modules/@aws-sdk/middleware-sdk-s3/dist-cjs/index.js:137:14\n    at async /Users/<USER>/Desktop/kubera-new/hack-day/s3-mcp2/node_modules/@aws-sdk/middleware-logger/dist-cjs/index.js:33:22"}}}
{"timestamp":"2025-06-24 12:54:30.321","level":"warn","service":"s3-mcp-server","message":"Health Check: s3_connectivity is unhealthy","component":"s3_connectivity","environment":"development","version":"1.0.0","status":"unhealthy","details":{"message":"S3 connection test failed: UnknownError","duration":293,"details":{"bucket":"test-s3-bucket-development","region":"us-east-1"}},"healthCheck":true}
{"timestamp":"2025-06-24 12:55:00.027","level":"warn","service":"s3-mcp-server","message":"Health Check: memory is unhealthy","component":"memory","environment":"development","version":"1.0.0","status":"unhealthy","details":{"message":"High memory usage: 90.5%","duration":1,"details":{"usedMB":19,"totalMB":21,"usagePercent":90.47619047619048}},"healthCheck":true}
