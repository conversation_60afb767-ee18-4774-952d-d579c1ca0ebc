{"level":"info","message":"Added middleware to interceptor","name":"namespace-enrichment","priority":10,"service":"s3-mcp-server","timestamp":"2025-06-24 10:58:07"}
{"level":"info","message":"Added middleware to interceptor","name":"pod-enrichment","priority":20,"service":"s3-mcp-server","timestamp":"2025-06-24 10:58:07"}
{"level":"info","message":"Added middleware to interceptor","name":"content-filter","priority":30,"service":"s3-mcp-server","timestamp":"2025-06-24 10:58:07"}
{"level":"info","message":"Added middleware to interceptor","name":"kubernetes-resource-detection","priority":15,"service":"s3-mcp-server","timestamp":"2025-06-24 10:58:07"}
{"level":"info","message":"Added middleware to interceptor","name":"kubernetes-error-detection","priority":25,"service":"s3-mcp-server","timestamp":"2025-06-24 10:58:07"}
{"level":"info","message":"Added middleware to interceptor","name":"kubernetes-metrics-extraction","priority":35,"service":"s3-mcp-server","timestamp":"2025-06-24 10:58:07"}
{"contentLength":62,"conversationId":"test-conv-1","level":"debug","message":"Intercepting AI response","service":"s3-mcp-server","timestamp":"2025-06-24 10:58:07"}
{"contentLength":62,"context":{"conversationId":"test-conv-1","requestType":"pod_log_analysis"},"id":"a0b80eb9-559b-4b01-b669-c377a1708bfc","level":"debug","message":"Processing AI response","service":"s3-mcp-server","timestamp":"2025-06-24 10:58:07"}
{"id":"a0b80eb9-559b-4b01-b669-c377a1708bfc","level":"info","message":"AI response processed","namespace":"production","namespaceConfidence":"high","namespaceSource":"command","podCount":1,"service":"s3-mcp-server","timestamp":"2025-06-24 10:58:07"}
{"contentLength":83,"conversationId":"test-conv-2","level":"debug","message":"Intercepting AI response","service":"s3-mcp-server","timestamp":"2025-06-24 10:58:07"}
{"conversationId":"test-conv-2","level":"info","message":"Detected namespace for conversation","namespace":"api-service-67890","service":"s3-mcp-server","source":"content","timestamp":"2025-06-24 10:58:07"}
{"contentLength":83,"context":{"conversationId":"test-conv-2","requestType":"log_analysis"},"id":"d5aed1fd-6e58-4eda-8a66-f353c54f2b28","level":"debug","message":"Processing AI response","service":"s3-mcp-server","timestamp":"2025-06-24 10:58:07"}
{"id":"d5aed1fd-6e58-4eda-8a66-f353c54f2b28","level":"info","message":"AI response processed","namespace":"api-service-67890","namespaceConfidence":"medium","namespaceSource":"content","podCount":0,"service":"s3-mcp-server","timestamp":"2025-06-24 10:58:07"}
{"contentLength":61,"conversationId":"test-conv-3","level":"debug","message":"Intercepting AI response","service":"s3-mcp-server","timestamp":"2025-06-24 10:58:07"}
{"contentLength":61,"context":{"conversationId":"test-conv-3","requestType":"troubleshooting"},"id":"5b6e2ff7-e245-4356-8482-56af93517f25","level":"debug","message":"Processing AI response","service":"s3-mcp-server","timestamp":"2025-06-24 10:58:07"}
{"id":"5b6e2ff7-e245-4356-8482-56af93517f25","level":"info","message":"AI response processed","namespace":"team-alpha-backend","namespaceConfidence":"high","namespaceSource":"command","podCount":0,"service":"s3-mcp-server","timestamp":"2025-06-24 10:58:07"}
{"errorCount":1,"errorTypes":["CrashLoopBackOff"],"level":"info","message":"Detected Kubernetes errors in response","responseId":"5b6e2ff7-e245-4356-8482-56af93517f25","service":"s3-mcp-server","timestamp":"2025-06-24 10:58:07"}
{"contentLength":71,"conversationId":"test-conv-4","level":"debug","message":"Intercepting AI response","service":"s3-mcp-server","timestamp":"2025-06-24 10:58:07"}
{"contentLength":71,"context":{"conversationId":"test-conv-4","requestType":"performance_analysis"},"id":"b7760da1-b296-4c20-84b8-581ca0a7a976","level":"debug","message":"Processing AI response","service":"s3-mcp-server","timestamp":"2025-06-24 10:58:07"}
{"id":"b7760da1-b296-4c20-84b8-581ca0a7a976","level":"info","message":"AI response processed","namespace":"DEV_NAMESPACE","namespaceConfidence":"low","namespaceSource":"default","podCount":1,"service":"s3-mcp-server","timestamp":"2025-06-24 10:58:07"}
{"level":"debug","message":"Detected Kubernetes resources","resourceCount":1,"resources":["Pod/feature-branch-testing-env-worker-123"],"responseId":"b7760da1-b296-4c20-84b8-581ca0a7a976","service":"s3-mcp-server","timestamp":"2025-06-24 10:58:07"}
{"contentLength":80,"conversationId":"test-conv-5","level":"debug","message":"Intercepting AI response","service":"s3-mcp-server","timestamp":"2025-06-24 10:58:07"}
{"conversationId":"test-conv-5","level":"info","message":"Detected namespace for conversation","namespace":"558bd4d5db-xyz","service":"s3-mcp-server","source":"content","timestamp":"2025-06-24 10:58:07"}
{"contentLength":80,"context":{"conversationId":"test-conv-5","requestType":"system_troubleshooting"},"id":"5d03ff2c-e1e1-4cc9-b1f9-cec47c6b3fb2","level":"debug","message":"Processing AI response","service":"s3-mcp-server","timestamp":"2025-06-24 10:58:07"}
{"id":"5d03ff2c-e1e1-4cc9-b1f9-cec47c6b3fb2","level":"info","message":"AI response processed","namespace":"kube-system","namespaceConfidence":"high","namespaceSource":"command","podCount":0,"service":"s3-mcp-server","timestamp":"2025-06-24 10:58:07"}
{"contentLength":68,"conversationId":"test-conv-6","level":"debug","message":"Intercepting AI response","service":"s3-mcp-server","timestamp":"2025-06-24 10:58:07"}
{"conversationId":"test-conv-6","level":"info","message":"Detected namespace for conversation","namespace":"observability-monitoring","service":"s3-mcp-server","source":"content","timestamp":"2025-06-24 10:58:07"}
{"contentLength":68,"context":{"conversationId":"test-conv-6","requestType":"monitoring_analysis"},"id":"d2ded860-721e-4f91-a140-a2d99f485d36","level":"debug","message":"Processing AI response","service":"s3-mcp-server","timestamp":"2025-06-24 10:58:07"}
{"id":"d2ded860-721e-4f91-a140-a2d99f485d36","level":"info","message":"AI response processed","namespace":"observability-monitoring","namespaceConfidence":"medium","namespaceSource":"content","podCount":0,"service":"s3-mcp-server","timestamp":"2025-06-24 10:58:07"}
{"contentLength":66,"conversationId":"test-conv-7","level":"debug","message":"Intercepting AI response","service":"s3-mcp-server","timestamp":"2025-06-24 10:58:07"}
{"contentLength":66,"context":{"conversationId":"test-conv-7","requestType":"deployment_troubleshooting"},"id":"a8e855f5-36da-44b4-a1da-bd1e9be44464","level":"debug","message":"Processing AI response","service":"s3-mcp-server","timestamp":"2025-06-24 10:58:07"}
{"id":"a8e855f5-36da-44b4-a1da-bd1e9be44464","level":"info","message":"AI response processed","namespace":"dev-feature-xyz-123","namespaceConfidence":"high","namespaceSource":"command","podCount":1,"service":"s3-mcp-server","timestamp":"2025-06-24 10:58:07"}
{"level":"debug","message":"Detected Kubernetes resources","resourceCount":1,"resources":["pod/-n"],"responseId":"a8e855f5-36da-44b4-a1da-bd1e9be44464","service":"s3-mcp-server","timestamp":"2025-06-24 10:58:07"}
{"errorCount":1,"errorTypes":["ImagePullBackOff"],"level":"info","message":"Detected Kubernetes errors in response","responseId":"a8e855f5-36da-44b4-a1da-bd1e9be44464","service":"s3-mcp-server","timestamp":"2025-06-24 10:58:07"}
{"contentLength":69,"conversationId":"test-conv-8","level":"debug","message":"Intercepting AI response","service":"s3-mcp-server","timestamp":"2025-06-24 10:58:07"}
{"contentLength":69,"context":{"conversationId":"test-conv-8","requestType":"customer_support"},"id":"47eea111-7d44-45d7-a708-e9167413d8fe","level":"debug","message":"Processing AI response","service":"s3-mcp-server","timestamp":"2025-06-24 10:58:07"}
{"id":"47eea111-7d44-45d7-a708-e9167413d8fe","level":"info","message":"AI response processed","namespace":"DEV_NAMESPACE","namespaceConfidence":"low","namespaceSource":"default","podCount":0,"service":"s3-mcp-server","timestamp":"2025-06-24 10:58:07"}
{"contentLength":70,"conversationId":"test-conv-9","level":"debug","message":"Intercepting AI response","service":"s3-mcp-server","timestamp":"2025-06-24 10:58:07"}
{"conversationId":"test-conv-9","level":"info","message":"Detected namespace for conversation","namespace":"context","service":"s3-mcp-server","source":"content","timestamp":"2025-06-24 10:58:07"}
{"contentLength":70,"context":{"conversationId":"test-conv-9","requestType":"general_analysis"},"id":"57bd376a-cddb-4afa-8796-2f434d4a4f8e","level":"debug","message":"Processing AI response","service":"s3-mcp-server","timestamp":"2025-06-24 10:58:07"}
{"id":"57bd376a-cddb-4afa-8796-2f434d4a4f8e","level":"info","message":"AI response processed","namespace":"context","namespaceConfidence":"medium","namespaceSource":"content","podCount":0,"service":"s3-mcp-server","timestamp":"2025-06-24 10:58:07"}
