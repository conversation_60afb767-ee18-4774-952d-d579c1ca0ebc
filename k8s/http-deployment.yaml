# Kubernetes deployment for S3 MCP Server with HTTP Bridge
apiVersion: v1
kind: ConfigMap
metadata:
  name: s3-mcp-config
  namespace: s3-mcp-server
data:
  NODE_ENV: "production"
  HTTP_MODE: "true"
  HTTP_PORT: "3000"
  AWS_REGION: "us-east-1"
  LOG_LEVEL: "info"
  ENABLE_CONSOLE_LOGGING: "false"
  DEFAULT_NAMESPACE_PREFIX: "NO_NAMESPACE"
  ENABLE_NAMESPACE_FORMAT_VALIDATION: "true"
  CORS_ORIGINS: "*"
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: s3-mcp-server
  namespace: s3-mcp-server
  labels:
    app: s3-mcp-server
    mode: http
spec:
  replicas: 3
  selector:
    matchLabels:
      app: s3-mcp-server
  template:
    metadata:
      labels:
        app: s3-mcp-server
        mode: http
    spec:
      containers:
      - name: s3-mcp-server
        image: s3-mcp-server:latest
        imagePullPolicy: Always
        ports:
        - name: http
          containerPort: 3000
          protocol: TCP
        env:
        - name: AWS_ACCESS_KEY_ID
          valueFrom:
            secretKeyRef:
              name: aws-credentials
              key: access-key-id
        - name: AWS_SECRET_ACCESS_KEY
          valueFrom:
            secretKeyRef:
              name: aws-credentials
              key: secret-access-key
        - name: S3_BUCKET_NAME
          valueFrom:
            secretKeyRef:
              name: aws-credentials
              key: bucket-name
        envFrom:
        - configMapRef:
            name: s3-mcp-config
        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"
          limits:
            memory: "512Mi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /health
            port: 3000
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /health
            port: 3000
          initialDelaySeconds: 5
          periodSeconds: 5
          timeoutSeconds: 3
          failureThreshold: 2
        startupProbe:
          httpGet:
            path: /health
            port: 3000
          initialDelaySeconds: 10
          periodSeconds: 5
          timeoutSeconds: 3
          failureThreshold: 10
---
apiVersion: v1
kind: Service
metadata:
  name: s3-mcp-service
  namespace: s3-mcp-server
  labels:
    app: s3-mcp-server
spec:
  selector:
    app: s3-mcp-server
  ports:
  - name: http
    port: 80
    targetPort: 3000
    protocol: TCP
  type: ClusterIP
---
# Ingress for external access
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: s3-mcp-ingress
  namespace: s3-mcp-server
  annotations:
    kubernetes.io/ingress.class: alb
    alb.ingress.kubernetes.io/scheme: internet-facing
    alb.ingress.kubernetes.io/target-type: ip
    alb.ingress.kubernetes.io/listen-ports: '[{"HTTP": 80}, {"HTTPS": 443}]'
    alb.ingress.kubernetes.io/ssl-redirect: '443'
    alb.ingress.kubernetes.io/healthcheck-path: /health
    alb.ingress.kubernetes.io/healthcheck-interval-seconds: '30'
    alb.ingress.kubernetes.io/healthcheck-timeout-seconds: '5'
    alb.ingress.kubernetes.io/healthy-threshold-count: '2'
    alb.ingress.kubernetes.io/unhealthy-threshold-count: '3'
    alb.ingress.kubernetes.io/load-balancer-name: s3-mcp-server-alb
    alb.ingress.kubernetes.io/group.name: s3-mcp-server
spec:
  rules:
  - host: your-domain.com  # Replace with your actual domain
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: s3-mcp-service
            port:
              number: 80