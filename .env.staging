# Staging Environment Configuration
NODE_ENV=staging

# AWS Configuration (use staging credentials)
AWS_REGION=us-east-1
S3_BUCKET_NAME=staging-s3-mcp-bucket

# MCP Server Configuration
MCP_SERVER_PORT=3000
MCP_SERVER_NAME=staging-s3-mcp-server

# Kubernetes Configuration
DEFAULT_NAMESPACE_PREFIX=STAGING_NAMESPACE
ENABLE_NAMESPACE_FORMAT_VALIDATION=true

# Storage Configuration
FILE_RETENTION_DAYS=30
MAX_FILE_SIZE_MB=100
ENABLE_COMPRESSION=true
S3_BASE_PATH=staging-mcp-responses
ENABLE_DATE_FOLDERS=true
ENABLE_NAMESPACE_FOLDERS=true

# Logging Configuration
LOG_LEVEL=info
LOG_FILE_PATH=./logs/staging-mcp-server.log
ENABLE_CONSOLE_LOGGING=true

# Webhook Configuration
WEBHOOK_ENABLED=true
WEBHOOK_PORT=3001
WEBHOOK_CORS_ENABLED=true
WEBHOOK_MAX_PAYLOAD_SIZE=25mb

# Performance Configuration
MAX_CONCURRENT_UPLOADS=7
UPLOAD_TIMEOUT_MS=45000
RETRY_ATTEMPTS=3
RETRY_DELAY_MS=1000

# Security Configuration
ENABLE_REQUEST_VALIDATION=true
ENABLE_RESPONSE_SANITIZATION=true
MAX_REQUEST_SIZE_MB=75

# Monitoring Configuration
ENABLE_METRICS=true
METRICS_PORT=9090
HEALTH_CHECK_INTERVAL_MS=20000
