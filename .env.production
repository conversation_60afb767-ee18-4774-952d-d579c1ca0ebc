# Production Environment Configuration
NODE_ENV=production

# AWS Configuration (use production credentials)
AWS_REGION=us-east-1
S3_BUCKET_NAME=prod-s3-mcp-bucket

# MCP Server Configuration
MCP_SERVER_PORT=3000
MCP_SERVER_NAME=s3-mcp-server

# Kubernetes Configuration
DEFAULT_NAMESPACE_PREFIX=NO_NAMESPACE
ENABLE_NAMESPACE_FORMAT_VALIDATION=true

# Storage Configuration
FILE_RETENTION_DAYS=90
MAX_FILE_SIZE_MB=500
ENABLE_COMPRESSION=true
S3_BASE_PATH=mcp-responses
ENABLE_DATE_FOLDERS=true
ENABLE_NAMESPACE_FOLDERS=true

# Logging Configuration (minimal for performance)
LOG_LEVEL=warn
LOG_FILE_PATH=./logs/prod-mcp-server.log
ENABLE_CONSOLE_LOGGING=false

# Webhook Configuration (disabled by default in production)
WEBHOOK_ENABLED=false
WEBHOOK_PORT=3001
WEBHOOK_CORS_ENABLED=false
WEBHOOK_MAX_PAYLOAD_SIZE=10mb

# Performance Configuration (optimized for production)
MAX_CONCURRENT_UPLOADS=5
UPLOAD_TIMEOUT_MS=30000
RETRY_ATTEMPTS=3
RETRY_DELAY_MS=1000

# Security Configuration (strict in production)
ENABLE_REQUEST_VALIDATION=true
ENABLE_RESPONSE_SANITIZATION=true
MAX_REQUEST_SIZE_MB=100

# Monitoring Configuration
ENABLE_METRICS=true
METRICS_PORT=9090
HEALTH_CHECK_INTERVAL_MS=30000
