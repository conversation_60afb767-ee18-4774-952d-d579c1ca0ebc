# =============================================================================
# S3 MCP Server Configuration
# =============================================================================

# Environment Configuration
# Set to: development, staging, production, or test
NODE_ENV=development

# =============================================================================
# AWS S3 Configuration (Required)
# =============================================================================
AWS_ACCESS_KEY_ID=your_access_key_here
AWS_SECRET_ACCESS_KEY=your_secret_key_here
AWS_REGION=us-east-1
S3_BUCKET_NAME=your-s3-bucket-name

# =============================================================================
# MCP Server Configuration
# =============================================================================
MCP_SERVER_PORT=3000
MCP_SERVER_NAME=s3-mcp-server

# =============================================================================
# Kubernetes Namespace Configuration
# =============================================================================
DEFAULT_NAMESPACE_PREFIX=NO_NAMESPACE
# Enable format validation for Kubernetes namespace naming rules (optional)
ENABLE_NAMESPACE_FORMAT_VALIDATION=true

# =============================================================================
# File Storage Configuration
# =============================================================================
FILE_RETENTION_DAYS=30
MAX_FILE_SIZE_MB=10
ENABLE_COMPRESSION=false
S3_BASE_PATH=mcp-responses
ENABLE_DATE_FOLDERS=true
ENABLE_NAMESPACE_FOLDERS=true

# =============================================================================
# Logging Configuration
# =============================================================================
LOG_LEVEL=info
LOG_FILE_PATH=./logs/mcp-server.log
ENABLE_CONSOLE_LOGGING=true

# =============================================================================
# Advanced Configuration (Optional)
# =============================================================================

# Webhook Configuration (for HTTP endpoints)
WEBHOOK_ENABLED=false
WEBHOOK_PORT=3001
WEBHOOK_SECRET=your_webhook_secret_here
WEBHOOK_CORS_ENABLED=true
WEBHOOK_MAX_PAYLOAD_SIZE=10mb

# Performance Configuration
MAX_CONCURRENT_UPLOADS=5
UPLOAD_TIMEOUT_MS=30000
RETRY_ATTEMPTS=3
RETRY_DELAY_MS=1000

# Security Configuration
ENABLE_REQUEST_VALIDATION=true
ENABLE_RESPONSE_SANITIZATION=true
MAX_REQUEST_SIZE_MB=50

# Monitoring Configuration
ENABLE_METRICS=true
METRICS_PORT=9090
HEALTH_CHECK_INTERVAL_MS=30000

# Development Configuration (only used in development mode)
DEV_ENABLE_DEBUG_LOGS=true
DEV_MOCK_S3_UPLOADS=false
DEV_SIMULATE_ERRORS=false
