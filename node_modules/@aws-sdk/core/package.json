{"name": "@aws-sdk/core", "version": "3.835.0", "description": "Core functions & classes shared by multiple AWS SDK clients.", "scripts": {"build": "yarn lint && concurrently 'yarn:build:cjs' 'yarn:build:es' 'yarn:build:types'", "build:cjs": "node ../../scripts/compilation/inline core && rimraf ./dist-cjs/api-extractor-type-index.js", "build:es": "tsc -p tsconfig.es.json && rimraf ./dist-es/api-extractor-type-index.js", "build:include:deps": "lerna run --scope $npm_package_name --include-dependencies build", "build:types": "tsc -p tsconfig.types.json", "build:types:downlevel": "downlevel-dts dist-types dist-types/ts3.4", "lint": "node ../../scripts/validation/submodules-linter.js --pkg core", "clean": "rimraf ./dist-* && rimraf *.tsbuildinfo", "extract:docs": "api-extractor run --local", "test": "yarn g:vitest run", "test:integration": "yarn g:vitest run -c vitest.config.integ.ts", "test:watch": "yarn g:vitest watch", "test:integration:watch": "yarn g:vitest watch -c vitest.config.integ.ts"}, "main": "./dist-cjs/index.js", "module": "./dist-es/index.js", "types": "./dist-types/index.d.ts", "exports": {".": {"types": "./dist-types/index.d.ts", "module": "./dist-es/index.js", "node": "./dist-cjs/index.js", "import": "./dist-es/index.js", "require": "./dist-cjs/index.js"}, "./package.json": {"module": "./package.json", "node": "./package.json", "import": "./package.json", "require": "./package.json"}, "./client": {"types": "./dist-types/submodules/client/index.d.ts", "module": "./dist-es/submodules/client/index.js", "node": "./dist-cjs/submodules/client/index.js", "import": "./dist-es/submodules/client/index.js", "require": "./dist-cjs/submodules/client/index.js"}, "./httpAuthSchemes": {"types": "./dist-types/submodules/httpAuthSchemes/index.d.ts", "module": "./dist-es/submodules/httpAuthSchemes/index.js", "node": "./dist-cjs/submodules/httpAuthSchemes/index.js", "import": "./dist-es/submodules/httpAuthSchemes/index.js", "require": "./dist-cjs/submodules/httpAuthSchemes/index.js"}, "./account-id-endpoint": {"types": "./dist-types/submodules/account-id-endpoint/index.d.ts", "module": "./dist-es/submodules/account-id-endpoint/index.js", "node": "./dist-cjs/submodules/account-id-endpoint/index.js", "import": "./dist-es/submodules/account-id-endpoint/index.js", "require": "./dist-cjs/submodules/account-id-endpoint/index.js"}, "./protocols": {"types": "./dist-types/submodules/protocols/index.d.ts", "module": "./dist-es/submodules/protocols/index.js", "node": "./dist-cjs/submodules/protocols/index.js", "import": "./dist-es/submodules/protocols/index.js", "require": "./dist-cjs/submodules/protocols/index.js"}}, "files": ["./account-id-endpoint.d.ts", "./account-id-endpoint.js", "./client.d.ts", "./client.js", "./httpAuthSchemes.d.ts", "./httpAuthSchemes.js", "./protocols.d.ts", "./protocols.js", "dist-*/**"], "sideEffects": false, "author": {"name": "AWS SDK for JavaScript Team", "url": "https://aws.amazon.com/javascript/"}, "license": "Apache-2.0", "dependencies": {"@aws-sdk/types": "3.821.0", "@aws-sdk/xml-builder": "3.821.0", "@smithy/core": "^3.5.3", "@smithy/node-config-provider": "^4.1.3", "@smithy/property-provider": "^4.0.4", "@smithy/protocol-http": "^5.1.2", "@smithy/signature-v4": "^5.1.2", "@smithy/smithy-client": "^4.4.4", "@smithy/types": "^4.3.1", "@smithy/util-base64": "^4.0.0", "@smithy/util-body-length-browser": "^4.0.0", "@smithy/util-middleware": "^4.0.4", "@smithy/util-utf8": "^4.0.0", "fast-xml-parser": "4.4.1", "tslib": "^2.6.2"}, "devDependencies": {"@tsconfig/recommended": "1.0.1", "concurrently": "7.0.0", "downlevel-dts": "0.10.1", "rimraf": "3.0.2", "typescript": "~5.8.3"}, "engines": {"node": ">=18.0.0"}, "typesVersions": {"<4.0": {"dist-types/*": ["dist-types/ts3.4/*"]}}, "homepage": "https://github.com/aws/aws-sdk-js-v3/tree/main/packages/core", "repository": {"type": "git", "url": "https://github.com/aws/aws-sdk-js-v3.git", "directory": "packages/core"}}