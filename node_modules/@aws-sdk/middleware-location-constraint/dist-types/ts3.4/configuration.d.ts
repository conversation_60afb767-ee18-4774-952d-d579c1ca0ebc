import { Provider } from "@smithy/types";
export interface LocationConstraintInputConfig {}
interface PreviouslyResolved {
  region: Provider<string>;
}
export interface LocationConstraintResolvedConfig {
  region: Provider<string>;
}
export declare function resolveLocationConstraintConfig<T>(
  input: T & LocationConstraintInputConfig & PreviouslyResolved
): T & LocationConstraintResolvedConfig;
export {};
