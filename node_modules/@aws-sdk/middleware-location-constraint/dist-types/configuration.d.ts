import { Provider } from "@smithy/types";
/**
 * @public
 */
export interface LocationConstraintInputConfig {
}
interface PreviouslyResolved {
    region: Provider<string>;
}
export interface LocationConstraintResolvedConfig {
    /**
     * Resolved value for input config {@link RegionInputConfig.region}
     */
    region: Provider<string>;
}
export declare function resolveLocationConstraintConfig<T>(input: T & LocationConstraintInputConfig & PreviouslyResolved): T & LocationConstraintResolvedConfig;
export {};
