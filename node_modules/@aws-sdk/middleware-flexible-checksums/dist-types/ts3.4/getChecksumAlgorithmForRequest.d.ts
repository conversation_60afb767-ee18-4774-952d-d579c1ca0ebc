import { ChecksumAlgorithm, RequestChecksumCalculation } from "./constants";
export interface GetChecksumAlgorithmForRequestOptions {
  requestChecksumRequired: boolean;
  requestAlgorithmMember?: string;
  requestChecksumCalculation: RequestChecksumCalculation;
}
export declare const getChecksumAlgorithmForRequest: (
  input: any,
  {
    requestChecksumRequired,
    requestAlgorithmMember,
    requestChecksumCalculation,
  }: GetChecksumAlgorithmForRequestOptions
) => ChecksumAlgorithm | undefined;
