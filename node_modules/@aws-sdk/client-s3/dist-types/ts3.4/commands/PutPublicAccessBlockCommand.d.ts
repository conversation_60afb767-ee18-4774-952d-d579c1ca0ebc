import { Command as $Command } from "@smithy/smithy-client";
import { Metada<PERSON>Bearer as __MetadataBearer } from "@smithy/types";
import { PutPublicAccessBlockRequest } from "../models/models_1";
import {
  S3ClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../S3Client";
export { __MetadataBearer };
export { $Command };
export interface PutPublicAccessBlockCommandInput
  extends PutPublicAccessBlockRequest {}
export interface PutPublicAccessBlockCommandOutput extends __MetadataBearer {}
declare const PutPublicAccessBlockCommand_base: {
  new (
    input: PutPublicAccessBlockCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    PutPublicAccessBlockCommandInput,
    PutPublicAccessBlockCommandOutput,
    S3ClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: PutPublicAccessBlockCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    PutPublicAccessBlockCommandInput,
    PutPublicAccessBlockCommandOutput,
    S3ClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class PutPublicAccessBlockCommand extends PutPublicAccessBlockCommand_base {
  protected static __types: {
    api: {
      input: PutPublicAccessBlockRequest;
      output: {};
    };
    sdk: {
      input: PutPublicAccessBlockCommandInput;
      output: PutPublicAccessBlockCommandOutput;
    };
  };
}
