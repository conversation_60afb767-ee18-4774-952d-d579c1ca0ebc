import { RequestSerializer, SerdeContext, Serde<PERSON>un<PERSON>, SerializeMiddleware } from "@smithy/types";
/**
 * @internal
 * @deprecated will be replaced by schemaSerdePlugin from core/schema.
 */
export declare const serializerMiddleware: <Input extends object = any, Output extends object = any, CommandSerdeContext extends SerdeContext = any>(options: SerdeFunctions, serializer: RequestSerializer<any, CommandSerdeContext>) => SerializeMiddleware<Input, Output>;
