/**
 * Errors encountered when the client clock and server clock cannot agree on the
 * current time.
 *
 * These errors are retryable, assuming the SDK has enabled clock skew
 * correction.
 */
export declare const CLOCK_SKEW_ERROR_CODES: string[];
/**
 * Errors that indicate the SDK is being throttled.
 *
 * These errors are always retryable.
 */
export declare const THROTTLING_ERROR_CODES: string[];
/**
 * Error codes that indicate transient issues
 */
export declare const TRANSIENT_ERROR_CODES: string[];
/**
 * Error codes that indicate transient issues
 */
export declare const TRANSIENT_ERROR_STATUS_CODES: number[];
/**
 * Node.js system error codes that indicate timeout.
 */
export declare const NODEJS_TIMEOUT_ERROR_CODES: string[];
/**
 * Node.js system error codes that indicate network error.
 */
export declare const NODEJS_NETWORK_ERROR_CODES: string[];
