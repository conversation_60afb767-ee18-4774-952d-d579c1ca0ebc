/**
 * Advanced AI Agent Response Interceptor
 * Provides middleware pipeline for processing AI responses with enhanced capabilities
 */
import { EventEmitter } from 'events';
import { AIResponse } from './types.js';
export interface InterceptorContext {
    conversationId: string;
    sessionId?: string;
    userId?: string;
    timestamp: Date;
    metadata: Record<string, any>;
}
export interface ResponseChunk {
    id: string;
    content: string;
    isComplete: boolean;
    chunkIndex: number;
    totalChunks?: number;
    timestamp: Date;
}
export interface InterceptorMiddleware {
    name: string;
    priority: number;
    process(response: AIResponse, context: InterceptorContext): Promise<AIResponse>;
}
export interface ConversationState {
    id: string;
    namespace?: string;
    podNames: string[];
    requestType?: string;
    startTime: Date;
    lastActivity: Date;
    messageCount: number;
    metadata: Record<string, any>;
}
export declare class AIResponseInterceptor extends EventEmitter {
    private responseHandler;
    private _config;
    private middleware;
    private conversations;
    private activeStreams;
    constructor();
    /**
     * Add middleware to the processing pipeline
     */
    addMiddleware(middleware: InterceptorMiddleware): void;
    /**
     * Intercept a complete AI response
     */
    interceptResponse(content: string, context?: Partial<InterceptorContext>): Promise<AIResponse>;
    /**
     * Intercept streaming AI response chunks
     */
    interceptStreamChunk(chunk: string, streamId: string, isComplete?: boolean, context?: Partial<InterceptorContext>): Promise<ResponseChunk>;
    /**
     * Get conversation state
     */
    getConversationState(conversationId: string): ConversationState | undefined;
    /**
     * Update conversation state with new information
     */
    private updateConversationState;
    /**
     * Build complete context from partial context
     */
    private buildContext;
    /**
     * Extract pod names from content
     */
    private extractPodNamesFromContent;
    /**
     * Setup default middleware
     */
    private setupDefaultMiddleware;
    /**
     * Start conversation cleanup process
     */
    private startConversationCleanup;
    /**
     * Get statistics about interceptor usage
     */
    getStats(): {
        activeConversations: number;
        activeStreams: number;
        middlewareCount: number;
        totalInterceptions: number;
    };
}
//# sourceMappingURL=interceptor.d.ts.map