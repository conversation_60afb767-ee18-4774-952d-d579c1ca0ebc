/**
 * Type definitions for the S3 MCP Server
 */
export interface ServerConfig {
    aws: {
        accessKeyId: string;
        secretAccessKey: string;
        region: string;
        bucketName: string;
    };
    mcp: {
        port: number;
        serverName: string;
    };
    kubernetes: {
        defaultNamespacePrefix: string;
        validationEnabled: boolean;
        allowedNamespaces: string[];
    };
    storage: {
        retentionDays: number;
        maxFileSizeMB: number;
        enableCompression: boolean;
        basePath: string;
        enableDateFolders: boolean;
        enableNamespaceFolders: boolean;
    };
    logging: {
        level: string;
        filePath: string;
        enableConsoleLogging: boolean;
    };
}
export interface NamespaceInfo {
    namespace: string;
    confidence: 'high' | 'medium' | 'low';
    source: 'parameter' | 'pod-name' | 'command' | 'log-path' | 'context' | 'default';
    extractedFrom?: string;
}
export interface AIResponse {
    id: string;
    timestamp: Date;
    content: string;
    namespace: NamespaceInfo;
    conversationId?: string;
    requestType?: string;
    podNames?: string[];
    metadata?: Record<string, any>;
}
export interface S3UploadResult {
    success: boolean;
    key: string;
    bucket: string;
    url?: string;
    error?: string;
}
export interface NamespaceExtractor {
    extractFromRequest(request: any): NamespaceInfo | null;
    extractFromContent(content: string): NamespaceInfo | null;
    extractFromContext(context: any): NamespaceInfo | null;
}
//# sourceMappingURL=types.d.ts.map