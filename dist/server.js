"use strict";
/**
 * Main MCP Server implementation
 * This will be implemented in the next task
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const logger_js_1 = __importDefault(require("./logger.js"));
const config_js_1 = require("./config.js");
async function main() {
    const config = (0, config_js_1.getConfig)();
    logger_js_1.default.info('Starting S3 MCP Server...', {
        serverName: config.mcp.serverName,
        port: config.mcp.port,
        bucketName: config.aws.bucketName,
    });
    // TODO: Implement MCP server initialization
    logger_js_1.default.info('MCP Server implementation coming in next task...');
}
// Handle graceful shutdown
process.on('SIGINT', () => {
    logger_js_1.default.info('Received SIGINT, shutting down gracefully...');
    process.exit(0);
});
process.on('SIGTERM', () => {
    logger_js_1.default.info('Received SIGTERM, shutting down gracefully...');
    process.exit(0);
});
// Start the server
if (require.main === module) {
    main().catch((error) => {
        logger_js_1.default.error('Failed to start server:', error);
        process.exit(1);
    });
}
//# sourceMappingURL=server.js.map