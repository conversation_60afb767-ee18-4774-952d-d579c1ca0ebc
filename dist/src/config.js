/**
 * Enhanced configuration management for the S3 MCP Server
 */
import dotenv from 'dotenv';
import { configSchema } from './config/schema.js';
import { validateConfig, validateEnvironmentVariables, formatValidationErrors } from './config/validator.js';
import { getCurrentEnvironment, mergeEnvironmentConfig, validateEnvironmentRequirements, getEnvironmentBucketName } from './config/environments.js';
import { loadSecrets, applySecrets, maskSensitiveConfig, getSecretSourcesForEnvironment } from './config/secrets.js';
// Load environment variables from .env file
dotenv.config();
// Global configuration cache
let cachedConfig = null;
let configLoadTime = null;
/**
 * Enhanced configuration loading with validation and environment support
 */
async function loadConfiguration() {
    const environment = getCurrentEnvironment();
    console.log(`Loading configuration for environment: ${environment}`);
    // Validate environment variables first
    const envValidation = validateEnvironmentVariables();
    if (envValidation.warnings.length > 0) {
        console.warn('Environment validation warnings:');
        for (const warning of envValidation.warnings) {
            console.warn(`  ⚠️  ${warning.path}: ${warning.message}`);
        }
    }
    // Check environment-specific requirements
    const envRequirements = validateEnvironmentRequirements(environment);
    if (!envRequirements.isValid) {
        throw new Error(`Missing required environment variables for ${environment}: ${envRequirements.missing.join(', ')}`);
    }
    // Load secrets
    const secretSources = getSecretSourcesForEnvironment(environment);
    const secrets = await loadSecrets(secretSources);
    // Build base configuration from environment variables
    const baseConfig = {
        aws: {
            accessKeyId: process.env['AWS_ACCESS_KEY_ID'] || '',
            secretAccessKey: process.env['AWS_SECRET_ACCESS_KEY'] || '',
            region: process.env['AWS_REGION'] || 'us-east-1',
            bucketName: getEnvironmentBucketName(process.env['S3_BUCKET_NAME'] || '', environment),
        },
        mcp: {
            port: parseInt(process.env['MCP_SERVER_PORT'] || '3000', 10),
            serverName: process.env['MCP_SERVER_NAME'] || 's3-mcp-server',
        },
        kubernetes: {
            defaultNamespacePrefix: process.env['DEFAULT_NAMESPACE_PREFIX'] || 'NO_NAMESPACE',
            validationEnabled: process.env['NAMESPACE_VALIDATION_ENABLED'] === 'true',
            allowedNamespaces: parseStringArray(process.env['ALLOWED_NAMESPACES'], ['production', 'staging', 'development', 'kube-system']),
        },
        storage: {
            retentionDays: parseInt(process.env['FILE_RETENTION_DAYS'] || '30', 10),
            maxFileSizeMB: parseInt(process.env['MAX_FILE_SIZE_MB'] || '10', 10),
            enableCompression: process.env['ENABLE_COMPRESSION'] === 'true',
            basePath: process.env['S3_BASE_PATH'] || 'mcp-responses',
            enableDateFolders: process.env['ENABLE_DATE_FOLDERS'] !== 'false',
            enableNamespaceFolders: process.env['ENABLE_NAMESPACE_FOLDERS'] !== 'false',
        },
        logging: {
            level: process.env['LOG_LEVEL'] || 'info',
            filePath: process.env['LOG_FILE_PATH'] || './logs/mcp-server.log',
            enableConsoleLogging: process.env['ENABLE_CONSOLE_LOGGING'] !== 'false',
        },
    };
    // Apply secrets to configuration
    const configWithSecrets = applySecrets(baseConfig, secrets);
    // Merge with environment-specific overrides
    const finalConfig = mergeEnvironmentConfig(configWithSecrets, environment);
    // Validate the final configuration
    const validation = validateConfig(finalConfig, configSchema);
    if (!validation.isValid) {
        const errorMessage = formatValidationErrors(validation);
        throw new Error(`Configuration validation failed:\n${errorMessage}`);
    }
    // Log warnings if any
    if (validation.warnings.length > 0) {
        console.warn('Configuration warnings:');
        for (const warning of validation.warnings) {
            console.warn(`  ⚠️  ${warning.path}: ${warning.message}`);
        }
    }
    // Log environment warnings
    if (envRequirements.warnings.length > 0) {
        console.info('Environment information:');
        for (const warning of envRequirements.warnings) {
            console.info(`  ℹ️  ${warning}`);
        }
    }
    return finalConfig;
}
/**
 * Parses comma-separated string into array
 */
function parseStringArray(value, defaultValue = []) {
    if (!value)
        return defaultValue;
    return value.split(',').map(item => item.trim()).filter(item => item.length > 0);
}
/**
 * Gets the server configuration (synchronous version for backward compatibility)
 */
export function getConfig() {
    // If we have a cached config that's less than 5 minutes old, use it
    if (cachedConfig && configLoadTime && (Date.now() - configLoadTime.getTime()) < 5 * 60 * 1000) {
        return cachedConfig;
    }
    // For synchronous compatibility, load basic config without async features
    const environment = getCurrentEnvironment();
    const baseConfig = {
        aws: {
            accessKeyId: process.env['AWS_ACCESS_KEY_ID'] || '',
            secretAccessKey: process.env['AWS_SECRET_ACCESS_KEY'] || '',
            region: process.env['AWS_REGION'] || 'us-east-1',
            bucketName: getEnvironmentBucketName(process.env['S3_BUCKET_NAME'] || '', environment),
        },
        mcp: {
            port: parseInt(process.env['MCP_SERVER_PORT'] || '3000', 10),
            serverName: process.env['MCP_SERVER_NAME'] || 's3-mcp-server',
        },
        kubernetes: {
            defaultNamespacePrefix: process.env['DEFAULT_NAMESPACE_PREFIX'] || 'NO_NAMESPACE',
            validationEnabled: process.env['NAMESPACE_VALIDATION_ENABLED'] === 'true',
            allowedNamespaces: parseStringArray(process.env['ALLOWED_NAMESPACES'], ['production', 'staging', 'development', 'kube-system']),
        },
        storage: {
            retentionDays: parseInt(process.env['FILE_RETENTION_DAYS'] || '30', 10),
            maxFileSizeMB: parseInt(process.env['MAX_FILE_SIZE_MB'] || '10', 10),
            enableCompression: process.env['ENABLE_COMPRESSION'] === 'true',
            basePath: process.env['S3_BASE_PATH'] || 'mcp-responses',
            enableDateFolders: process.env['ENABLE_DATE_FOLDERS'] !== 'false',
            enableNamespaceFolders: process.env['ENABLE_NAMESPACE_FOLDERS'] !== 'false',
        },
        logging: {
            level: process.env['LOG_LEVEL'] || 'info',
            filePath: process.env['LOG_FILE_PATH'] || './logs/mcp-server.log',
            enableConsoleLogging: process.env['ENABLE_CONSOLE_LOGGING'] !== 'false',
        },
    };
    // Merge with environment-specific overrides
    const finalConfig = mergeEnvironmentConfig(baseConfig, environment);
    // Validate required fields
    const required = ['aws.accessKeyId', 'aws.secretAccessKey', 'aws.region', 'aws.bucketName'];
    for (const field of required) {
        const value = getNestedValue(finalConfig, field);
        if (!value) {
            throw new Error(`Missing required configuration: ${field}`);
        }
    }
    // Cache the config
    cachedConfig = finalConfig;
    configLoadTime = new Date();
    return finalConfig;
}
/**
 * Gets the server configuration (async version with full validation)
 */
export async function getConfigAsync() {
    // If we have a cached config that's less than 5 minutes old, use it
    if (cachedConfig && configLoadTime && (Date.now() - configLoadTime.getTime()) < 5 * 60 * 1000) {
        return cachedConfig;
    }
    const config = await loadConfiguration();
    // Cache the config
    cachedConfig = config;
    configLoadTime = new Date();
    return config;
}
/**
 * Gets a nested value from an object using dot notation
 */
function getNestedValue(obj, path) {
    return path.split('.').reduce((current, key) => current?.[key], obj);
}
/**
 * Clears the configuration cache (useful for testing)
 */
export function clearConfigCache() {
    cachedConfig = null;
    configLoadTime = null;
}
/**
 * Gets masked configuration for logging (hides sensitive values)
 */
export function getMaskedConfig() {
    const config = getConfig();
    return maskSensitiveConfig(config);
}
//# sourceMappingURL=config.js.map