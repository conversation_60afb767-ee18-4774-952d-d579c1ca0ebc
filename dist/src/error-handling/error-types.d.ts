/**
 * Custom error types for the S3 MCP Server
 */
export declare enum ErrorCode {
    CONFIG_INVALID = "CONFIG_INVALID",
    CONFIG_MISSING = "CONFIG_MISSING",
    S3_CONNECTION_FAILED = "S3_CONNECTION_FAILED",
    S3_UPLOAD_FAILED = "S3_UPLOAD_FAILED",
    S3_BUCKET_NOT_FOUND = "S3_BUCKET_NOT_FOUND",
    S3_ACCESS_DENIED = "S3_ACCESS_DENIED",
    S3_QUOTA_EXCEEDED = "S3_QUOTA_EXCEEDED",
    MCP_PROTOCOL_ERROR = "MCP_PROTOCOL_ERROR",
    MCP_TOOL_NOT_FOUND = "MCP_TOOL_NOT_FOUND",
    MCP_INVALID_REQUEST = "MCP_INVALID_REQUEST",
    RESPONSE_PROCESSING_FAILED = "RESPONSE_PROCESSING_FAILED",
    NAMESPACE_EXTRACTION_FAILED = "NAMESPACE_EXTRACTION_FAILED",
    CONTENT_VALIDATION_FAILED = "CONTENT_VALIDATION_FAILED",
    NETWORK_TIMEOUT = "NETWORK_TIMEOUT",
    NETWORK_CONNECTION_FAILED = "NETWORK_CONNECTION_FAILED",
    VALIDATION_FAILED = "VALIDATION_FAILED",
    INVALID_INPUT = "INVALID_INPUT",
    SYSTEM_ERROR = "SYSTEM_ERROR",
    RESOURCE_EXHAUSTED = "RESOURCE_EXHAUSTED",
    RATE_LIMIT_EXCEEDED = "RATE_LIMIT_EXCEEDED"
}
export declare enum ErrorSeverity {
    LOW = "low",
    MEDIUM = "medium",
    HIGH = "high",
    CRITICAL = "critical"
}
export interface ErrorContext {
    requestId?: string;
    conversationId?: string;
    namespace?: string;
    operation?: string;
    userId?: string;
    timestamp?: Date;
    metadata?: Record<string, any>;
}
export declare class MCPError extends Error {
    readonly code: ErrorCode;
    readonly severity: ErrorSeverity;
    readonly context: ErrorContext;
    readonly isRetryable: boolean;
    readonly originalError?: Error | undefined;
    constructor(code: ErrorCode, message: string, severity?: ErrorSeverity, context?: ErrorContext, isRetryable?: boolean, originalError?: Error);
    toJSON(): {
        name: string;
        message: string;
        code: ErrorCode;
        severity: ErrorSeverity;
        context: ErrorContext;
        isRetryable: boolean;
        stack: string | undefined;
        originalError: {
            name: string;
            message: string;
            stack: string | undefined;
        } | undefined;
    };
}
/**
 * Factory functions for common error types
 */
export declare class ErrorFactory {
    static configurationError(message: string, context?: ErrorContext): MCPError;
    static s3Error(message: string, context?: ErrorContext, originalError?: Error): MCPError;
    static processingError(message: string, context?: ErrorContext, originalError?: Error): MCPError;
    static networkError(message: string, context?: ErrorContext, originalError?: Error): MCPError;
    static validationError(message: string, context?: ErrorContext): MCPError;
    private static isS3ErrorRetryable;
    private static getS3ErrorSeverity;
}
/**
 * Error classification utilities
 */
export declare class ErrorClassifier {
    static isRetryable(error: Error | MCPError): boolean;
    static getSeverity(error: Error | MCPError): ErrorSeverity;
    static shouldAlert(error: Error | MCPError): boolean;
}
//# sourceMappingURL=error-types.d.ts.map