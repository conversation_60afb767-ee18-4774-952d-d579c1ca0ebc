{"version": 3, "file": "enhanced-logger.d.ts", "sourceRoot": "", "sources": ["../../../src/error-handling/enhanced-logger.ts"], "names": [], "mappings": "AAAA;;GAEG;AAEH,OAAO,OAAO,MAAM,SAAS,CAAC;AAE9B,OAAO,EAAE,QAAQ,EAAiB,MAAM,kBAAkB,CAAC;AAoJ3D,qBAAa,cAAc;IACzB,OAAO,CAAC,OAAO,CAAiB;IAChC,OAAO,CAAC,OAAO,CAAkC;IACjD,OAAO,CAAC,kBAAkB,CAAiF;gBAE/F,aAAa,EAAE,OAAO,CAAC,MAAM;IAKzC,KAAK,CAAC,OAAO,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE,GAAG,GAAG,IAAI;IAKxC,IAAI,CAAC,OAAO,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE,GAAG,GAAG,IAAI;IAKvC,IAAI,CAAC,OAAO,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE,GAAG,GAAG,IAAI;IAIvC,IAAI,CAAC,OAAO,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE,GAAG,GAAG,IAAI;IAIvC,KAAK,CAAC,OAAO,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE,GAAG,GAAG,IAAI;IAIxC,OAAO,CAAC,OAAO,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE,GAAG,GAAG,IAAI;IAI1C,GAAG,CAAC,KAAK,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE,GAAG,GAAG,IAAI;IAKrD,QAAQ,CAAC,KAAK,EAAE,KAAK,GAAG,QAAQ,EAAE,OAAO,CAAC,EAAE,GAAG,GAAG,IAAI;IAoBtD,cAAc,CAAC,SAAS,EAAE,MAAM,EAAE,QAAQ,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE,GAAG,GAAG,IAAI;IAgBrE,WAAW,CAAC,KAAK,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE,GAAG,GAAG,IAAI;IAQ5C,QAAQ,CAAC,MAAM,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE,GAAG,GAAG,IAAI;IAO1C,UAAU,CAAC,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,UAAU,EAAE,MAAM,EAAE,QAAQ,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE,GAAG,GAAG,IAAI;IAYhG,OAAO,CAAC,eAAe;IAKvB,UAAU,IAAI,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC;IAIpC,qBAAqB,IAAI,MAAM,CAAC,MAAM,EAAE;QAAE,KAAK,EAAE,MAAM,CAAC;QAAC,SAAS,EAAE,MAAM,CAAC;QAAC,OAAO,EAAE,MAAM,CAAA;KAAE,CAAC;IAI9F,YAAY,IAAI,IAAI;IAMpB,cAAc,CAAC,SAAS,EAAE,MAAM,EAAE,MAAM,EAAE,SAAS,GAAG,WAAW,GAAG,UAAU,EAAE,OAAO,CAAC,EAAE,GAAG,GAAG,IAAI;IAUpG,OAAO,CAAC,kBAAkB;IAgB1B,KAAK,CAAC,OAAO,EAAE,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,GAAG,cAAc;CAIpD;AAGD,QAAA,MAAM,cAAc,gBAAiC,CAAC;AAEtD,eAAe,cAAc,CAAC"}