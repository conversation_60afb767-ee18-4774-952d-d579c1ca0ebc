/**
 * Enhanced logging system with structured logging and monitoring
 */
import winston from 'winston';
import { MCPError } from './error-types.js';
export declare class EnhancedLogger {
    private winston;
    private metrics;
    private performanceMetrics;
    constructor(winstonLogger: winston.Logger);
    error(message: string, meta?: any): void;
    warn(message: string, meta?: any): void;
    info(message: string, meta?: any): void;
    http(message: string, meta?: any): void;
    debug(message: string, meta?: any): void;
    verbose(message: string, meta?: any): void;
    log(level: string, message: string, meta?: any): void;
    logError(error: Error | MCPError, context?: any): void;
    logPerformance(operation: string, duration: number, meta?: any): void;
    logSecurity(event: string, meta?: any): void;
    logAudit(action: string, meta?: any): void;
    logRequest(method: string, path: string, statusCode: number, duration: number, meta?: any): void;
    private incrementMetric;
    getMetrics(): Record<string, number>;
    getPerformanceMetrics(): Record<string, {
        count: number;
        totalTime: number;
        avgTime: number;
    }>;
    resetMetrics(): void;
    logHealthCheck(component: string, status: 'healthy' | 'unhealthy' | 'degraded', details?: any): void;
    private severityToLogLevel;
    child(context: Record<string, any>): EnhancedLogger;
}
declare const enhancedLogger: EnhancedLogger;
export default enhancedLogger;
//# sourceMappingURL=enhanced-logger.d.ts.map