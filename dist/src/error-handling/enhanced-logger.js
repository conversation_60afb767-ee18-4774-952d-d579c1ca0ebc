/**
 * Enhanced logging system with structured logging and monitoring
 */
import winston from 'winston';
import { getConfig } from '../config.js';
import { MCPError, ErrorSeverity } from './error-types.js';
import { dirname } from 'path';
import { mkdirSync } from 'fs';
// Get configuration
const config = getConfig();
// Ensure log directory exists
const logDir = dirname(config.logging.filePath);
try {
    mkdirSync(logDir, { recursive: true });
}
catch (error) {
    // Directory might already exist, ignore error
}
// Custom log levels with priorities
const customLevels = {
    levels: {
        error: 0,
        warn: 1,
        info: 2,
        http: 3,
        debug: 4,
        verbose: 5,
    },
    colors: {
        error: 'red',
        warn: 'yellow',
        info: 'green',
        http: 'magenta',
        debug: 'blue',
        verbose: 'cyan',
    },
};
// Add colors to winston
winston.addColors(customLevels.colors);
// Custom format for structured logging
const structuredFormat = winston.format.combine(winston.format.timestamp({
    format: 'YYYY-MM-DD HH:mm:ss.SSS'
}), winston.format.errors({ stack: true }), winston.format.printf((info) => {
    const { timestamp, level, message, service, ...meta } = info;
    // Handle MCPError objects
    if (meta['error'] instanceof MCPError) {
        meta['error'] = meta['error'].toJSON();
    }
    const logEntry = {
        timestamp,
        level,
        service,
        message,
        ...meta,
    };
    return JSON.stringify(logEntry);
}));
// Console format for development
const consoleFormat = winston.format.combine(winston.format.colorize(), winston.format.timestamp({
    format: 'HH:mm:ss'
}), winston.format.printf((info) => {
    const { timestamp, level, message, service, ...meta } = info;
    let metaStr = '';
    if (Object.keys(meta).length > 0) {
        metaStr = ` ${JSON.stringify(meta)}`;
    }
    return `${timestamp} [${service}] ${level}: ${message}${metaStr}`;
}));
// Configure winston logger
const baseLogger = winston.createLogger({
    level: config.logging.level,
    levels: customLevels.levels,
    format: structuredFormat,
    defaultMeta: {
        service: config.mcp.serverName,
        environment: process.env['NODE_ENV'] || 'development',
        version: process.env['npm_package_version'] || '1.0.0',
    },
    transports: [
        // File transport for all logs
        new winston.transports.File({
            filename: config.logging.filePath,
            maxsize: 10485760, // 10MB
            maxFiles: 10,
            tailable: true,
        }),
        // Separate file for errors
        new winston.transports.File({
            filename: config.logging.filePath.replace('.log', '-error.log'),
            level: 'error',
            maxsize: 5242880, // 5MB
            maxFiles: 5,
            tailable: true,
        }),
        // Performance logs
        new winston.transports.File({
            filename: config.logging.filePath.replace('.log', '-performance.log'),
            level: 'info',
            maxsize: 5242880, // 5MB
            maxFiles: 3,
            format: winston.format.combine(structuredFormat, winston.format((info) => {
                return info['performanceLog'] ? info : false;
            })()),
        }),
    ],
    // Handle uncaught exceptions and rejections
    exceptionHandlers: [
        new winston.transports.File({
            filename: config.logging.filePath.replace('.log', '-exceptions.log'),
            maxsize: 5242880, // 5MB
            maxFiles: 3,
        }),
    ],
    rejectionHandlers: [
        new winston.transports.File({
            filename: config.logging.filePath.replace('.log', '-rejections.log'),
            maxsize: 5242880, // 5MB
            maxFiles: 3,
        }),
    ],
});
// Add console transport if enabled
if (config.logging.enableConsoleLogging) {
    baseLogger.add(new winston.transports.Console({
        format: consoleFormat,
        level: config.logging.level,
    }));
}
// Enhanced logger with additional methods
export class EnhancedLogger {
    winston;
    metrics = new Map();
    performanceMetrics = new Map();
    constructor(winstonLogger) {
        this.winston = winstonLogger;
    }
    // Standard logging methods
    error(message, meta) {
        this.winston.error(message, meta);
        this.incrementMetric('errors');
    }
    warn(message, meta) {
        this.winston.warn(message, meta);
        this.incrementMetric('warnings');
    }
    info(message, meta) {
        this.winston.info(message, meta);
    }
    http(message, meta) {
        this.winston.http(message, meta);
    }
    debug(message, meta) {
        this.winston.debug(message, meta);
    }
    verbose(message, meta) {
        this.winston.verbose(message, meta);
    }
    log(level, message, meta) {
        this.winston.log(level, message, meta);
    }
    // Enhanced methods for specific use cases
    logError(error, context) {
        const meta = {
            ...context,
            error: error instanceof MCPError ? error.toJSON() : {
                name: error.name,
                message: error.message,
                stack: error.stack,
            },
        };
        if (error instanceof MCPError) {
            const level = this.severityToLogLevel(error.severity);
            this.winston.log(level, `MCPError: ${error.message}`, meta);
        }
        else {
            this.winston.error(error.message, meta);
        }
        this.incrementMetric('errors');
    }
    logPerformance(operation, duration, meta) {
        this.winston.info(`Performance: ${operation}`, {
            ...meta,
            operation,
            duration,
            performanceLog: true,
        });
        // Update performance metrics
        const current = this.performanceMetrics.get(operation) || { count: 0, totalTime: 0, avgTime: 0 };
        current.count++;
        current.totalTime += duration;
        current.avgTime = current.totalTime / current.count;
        this.performanceMetrics.set(operation, current);
    }
    logSecurity(event, meta) {
        this.winston.warn(`Security: ${event}`, {
            ...meta,
            securityEvent: true,
        });
        this.incrementMetric('security_events');
    }
    logAudit(action, meta) {
        this.winston.info(`Audit: ${action}`, {
            ...meta,
            auditLog: true,
        });
    }
    logRequest(method, path, statusCode, duration, meta) {
        this.winston.http(`${method} ${path} ${statusCode}`, {
            ...meta,
            method,
            path,
            statusCode,
            duration,
            requestLog: true,
        });
    }
    // Metrics and monitoring
    incrementMetric(metric) {
        const current = this.metrics.get(metric) || 0;
        this.metrics.set(metric, current + 1);
    }
    getMetrics() {
        return Object.fromEntries(this.metrics);
    }
    getPerformanceMetrics() {
        return Object.fromEntries(this.performanceMetrics);
    }
    resetMetrics() {
        this.metrics.clear();
        this.performanceMetrics.clear();
    }
    // Health check logging
    logHealthCheck(component, status, details) {
        const level = status === 'healthy' ? 'info' : 'warn';
        this.winston.log(level, `Health Check: ${component} is ${status}`, {
            component,
            status,
            details,
            healthCheck: true,
        });
    }
    severityToLogLevel(severity) {
        switch (severity) {
            case ErrorSeverity.CRITICAL:
                return 'error';
            case ErrorSeverity.HIGH:
                return 'error';
            case ErrorSeverity.MEDIUM:
                return 'warn';
            case ErrorSeverity.LOW:
                return 'info';
            default:
                return 'warn';
        }
    }
    // Create child logger with additional context
    child(context) {
        const childWinston = this.winston.child(context);
        return new EnhancedLogger(childWinston);
    }
}
// Create and export the enhanced logger instance
const enhancedLogger = new EnhancedLogger(baseLogger);
export default enhancedLogger;
//# sourceMappingURL=enhanced-logger.js.map