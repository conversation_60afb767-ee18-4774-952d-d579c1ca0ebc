/**
 * Error reporting and alerting system
 */
import enhancedLogger from './enhanced-logger.js';
import { MCPError, ErrorSeverity, ErrorClassifier } from './error-types.js';
import { getConfig } from '../config.js';
export class ErrorReporter {
    reports = new Map();
    alertRules = new Map();
    lastAlerts = new Map();
    logger = enhancedLogger.child({ component: 'ErrorReporter' });
    constructor() {
        this.setupDefaultAlertRules();
        this.logger.info('Error reporter initialized');
    }
    /**
     * Report an error
     */
    async reportError(error, context = {}) {
        const severity = ErrorClassifier.getSeverity(error);
        const fingerprint = this.generateFingerprint(error, context);
        const timestamp = new Date();
        let report = this.reports.get(fingerprint);
        if (report) {
            // Update existing report
            report.count++;
            report.lastSeen = timestamp;
            report.context = { ...report.context, ...context };
        }
        else {
            // Create new report
            report = {
                id: this.generateId(),
                timestamp,
                error,
                context,
                severity,
                fingerprint,
                count: 1,
                firstSeen: timestamp,
                lastSeen: timestamp,
            };
            this.reports.set(fingerprint, report);
        }
        // Log the error
        this.logger.logError(error, {
            ...context,
            reportId: report.id,
            fingerprint,
            count: report.count,
        });
        // Check alert rules
        await this.checkAlertRules(report);
        // Clean up old reports periodically
        if (this.reports.size % 100 === 0) {
            this.cleanupOldReports();
        }
    }
    /**
     * Register an alert rule
     */
    registerAlertRule(rule) {
        this.alertRules.set(rule.name, rule);
        this.logger.info('Alert rule registered', {
            name: rule.name,
            cooldown: rule.cooldown,
            enabled: rule.enabled,
        });
    }
    /**
     * Get error reports
     */
    getReports(limit = 100) {
        return Array.from(this.reports.values())
            .sort((a, b) => b.lastSeen.getTime() - a.lastSeen.getTime())
            .slice(0, limit);
    }
    /**
     * Get error statistics
     */
    getStatistics() {
        const reports = Array.from(this.reports.values());
        const now = Date.now();
        const oneHourAgo = now - 60 * 60 * 1000;
        return {
            totalReports: reports.reduce((sum, report) => sum + report.count, 0),
            uniqueErrors: reports.length,
            criticalErrors: reports.filter(r => r.severity === ErrorSeverity.CRITICAL).length,
            recentErrors: reports.filter(r => r.lastSeen.getTime() > oneHourAgo).length,
        };
    }
    /**
     * Generate error fingerprint for deduplication
     */
    generateFingerprint(error, context) {
        const errorType = error.constructor.name;
        const errorMessage = error.message;
        const errorCode = error instanceof MCPError ? error.code : 'UNKNOWN';
        const operation = context['operation'] || 'unknown';
        // Create a hash-like fingerprint
        const parts = [errorType, errorCode, operation, errorMessage.substring(0, 100)];
        return parts.join('|').replace(/[^a-zA-Z0-9|]/g, '_');
    }
    /**
     * Generate unique ID
     */
    generateId() {
        return `err_${Date.now()}_${Math.random().toString(36).substring(2, 8)}`;
    }
    /**
     * Check alert rules for a report
     */
    async checkAlertRules(report) {
        for (const [name, rule] of this.alertRules) {
            if (!rule.enabled)
                continue;
            // Check cooldown
            const lastAlert = this.lastAlerts.get(name);
            if (lastAlert && Date.now() - lastAlert.getTime() < rule.cooldown) {
                continue;
            }
            // Check condition
            if (rule.condition(report)) {
                try {
                    await rule.action(report);
                    this.lastAlerts.set(name, new Date());
                    this.logger.info('Alert rule triggered', {
                        ruleName: name,
                        reportId: report.id,
                        errorFingerprint: report.fingerprint,
                    });
                }
                catch (error) {
                    this.logger.error('Alert rule action failed', {
                        ruleName: name,
                        error: error instanceof Error ? error.message : String(error),
                    });
                }
            }
        }
    }
    /**
     * Setup default alert rules
     */
    setupDefaultAlertRules() {
        // Critical error alert
        this.registerAlertRule({
            name: 'critical_errors',
            condition: (report) => report.severity === ErrorSeverity.CRITICAL,
            action: async (report) => {
                this.logger.error('CRITICAL ERROR ALERT', {
                    reportId: report.id,
                    error: report.error.message,
                    count: report.count,
                    context: report.context,
                });
            },
            cooldown: 5 * 60 * 1000, // 5 minutes
            enabled: true,
        });
        // High frequency error alert
        this.registerAlertRule({
            name: 'high_frequency_errors',
            condition: (report) => report.count >= 10 && report.count % 10 === 0,
            action: async (report) => {
                this.logger.warn('HIGH FREQUENCY ERROR ALERT', {
                    reportId: report.id,
                    error: report.error.message,
                    count: report.count,
                    frequency: report.count / ((Date.now() - report.firstSeen.getTime()) / 1000 / 60), // per minute
                });
            },
            cooldown: 10 * 60 * 1000, // 10 minutes
            enabled: true,
        });
        // S3 connection failure alert
        this.registerAlertRule({
            name: 's3_connection_failures',
            condition: (report) => {
                return report.error.message.toLowerCase().includes('s3') &&
                    (report.error.message.toLowerCase().includes('connection') ||
                        report.error.message.toLowerCase().includes('network'));
            },
            action: async (report) => {
                this.logger.error('S3 CONNECTION FAILURE ALERT', {
                    reportId: report.id,
                    error: report.error.message,
                    count: report.count,
                    awsRegion: getConfig().aws.region,
                    bucketName: getConfig().aws.bucketName,
                });
            },
            cooldown: 15 * 60 * 1000, // 15 minutes
            enabled: true,
        });
    }
    /**
     * Clean up old error reports
     */
    cleanupOldReports() {
        const maxAge = 24 * 60 * 60 * 1000; // 24 hours
        const cutoff = Date.now() - maxAge;
        let cleaned = 0;
        for (const [fingerprint, report] of this.reports) {
            if (report.lastSeen.getTime() < cutoff) {
                this.reports.delete(fingerprint);
                cleaned++;
            }
        }
        if (cleaned > 0) {
            this.logger.info('Cleaned up old error reports', { cleaned });
        }
    }
    /**
     * Export error reports for analysis
     */
    exportReports() {
        return Array.from(this.reports.values()).map(report => ({
            id: report.id,
            timestamp: report.timestamp.toISOString(),
            error: {
                name: report.error.name,
                message: report.error.message,
                code: report.error instanceof MCPError ? report.error.code : undefined,
                severity: report.severity,
            },
            context: report.context,
            fingerprint: report.fingerprint,
            count: report.count,
            firstSeen: report.firstSeen.toISOString(),
            lastSeen: report.lastSeen.toISOString(),
        }));
    }
}
/**
 * Global error reporter instance
 */
export const errorReporter = new ErrorReporter();
//# sourceMappingURL=error-reporter.js.map