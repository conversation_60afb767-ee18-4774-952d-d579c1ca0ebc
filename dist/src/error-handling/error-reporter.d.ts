/**
 * Error reporting and alerting system
 */
import { MCPError, ErrorSeverity } from './error-types.js';
export interface ErrorReport {
    id: string;
    timestamp: Date;
    error: MCPError | Error;
    context: Record<string, any>;
    severity: ErrorSeverity;
    fingerprint: string;
    count: number;
    firstSeen: Date;
    lastSeen: Date;
}
export interface AlertRule {
    name: string;
    condition: (report: ErrorReport) => boolean;
    action: (report: ErrorReport) => Promise<void>;
    cooldown: number;
    enabled: boolean;
}
export declare class ErrorReporter {
    private reports;
    private alertRules;
    private lastAlerts;
    private logger;
    constructor();
    /**
     * Report an error
     */
    reportError(error: Error | MCPError, context?: Record<string, any>): Promise<void>;
    /**
     * Register an alert rule
     */
    registerAlertRule(rule: AlertRule): void;
    /**
     * Get error reports
     */
    getReports(limit?: number): ErrorReport[];
    /**
     * Get error statistics
     */
    getStatistics(): {
        totalReports: number;
        uniqueErrors: number;
        criticalErrors: number;
        recentErrors: number;
    };
    /**
     * Generate error fingerprint for deduplication
     */
    private generateFingerprint;
    /**
     * Generate unique ID
     */
    private generateId;
    /**
     * Check alert rules for a report
     */
    private checkAlertRules;
    /**
     * Setup default alert rules
     */
    private setupDefaultAlertRules;
    /**
     * Clean up old error reports
     */
    private cleanupOldReports;
    /**
     * Export error reports for analysis
     */
    exportReports(): any[];
}
/**
 * Global error reporter instance
 */
export declare const errorReporter: ErrorReporter;
//# sourceMappingURL=error-reporter.d.ts.map