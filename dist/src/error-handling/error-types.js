/**
 * Custom error types for the S3 MCP Server
 */
export var ErrorCode;
(function (ErrorCode) {
    // Configuration errors
    ErrorCode["CONFIG_INVALID"] = "CONFIG_INVALID";
    ErrorCode["CONFIG_MISSING"] = "CONFIG_MISSING";
    // AWS/S3 errors
    ErrorCode["S3_CONNECTION_FAILED"] = "S3_CONNECTION_FAILED";
    ErrorCode["S3_UPLOAD_FAILED"] = "S3_UPLOAD_FAILED";
    ErrorCode["S3_BUCKET_NOT_FOUND"] = "S3_BUCKET_NOT_FOUND";
    ErrorCode["S3_ACCESS_DENIED"] = "S3_ACCESS_DENIED";
    ErrorCode["S3_QUOTA_EXCEEDED"] = "S3_QUOTA_EXCEEDED";
    // MCP protocol errors
    ErrorCode["MCP_PROTOCOL_ERROR"] = "MCP_PROTOCOL_ERROR";
    ErrorCode["MCP_TOOL_NOT_FOUND"] = "MCP_TOOL_NOT_FOUND";
    ErrorCode["MCP_INVALID_REQUEST"] = "MCP_INVALID_REQUEST";
    // Processing errors
    ErrorCode["RESPONSE_PROCESSING_FAILED"] = "RESPONSE_PROCESSING_FAILED";
    ErrorCode["NAMESPACE_EXTRACTION_FAILED"] = "NAMESPACE_EXTRACTION_FAILED";
    ErrorCode["CONTENT_VALIDATION_FAILED"] = "CONTENT_VALIDATION_FAILED";
    // Network errors
    ErrorCode["NETWORK_TIMEOUT"] = "NETWORK_TIMEOUT";
    ErrorCode["NETWORK_CONNECTION_FAILED"] = "NETWORK_CONNECTION_FAILED";
    // Validation errors
    ErrorCode["VALIDATION_FAILED"] = "VALIDATION_FAILED";
    ErrorCode["INVALID_INPUT"] = "INVALID_INPUT";
    // System errors
    ErrorCode["SYSTEM_ERROR"] = "SYSTEM_ERROR";
    ErrorCode["RESOURCE_EXHAUSTED"] = "RESOURCE_EXHAUSTED";
    ErrorCode["RATE_LIMIT_EXCEEDED"] = "RATE_LIMIT_EXCEEDED";
})(ErrorCode || (ErrorCode = {}));
export var ErrorSeverity;
(function (ErrorSeverity) {
    ErrorSeverity["LOW"] = "low";
    ErrorSeverity["MEDIUM"] = "medium";
    ErrorSeverity["HIGH"] = "high";
    ErrorSeverity["CRITICAL"] = "critical";
})(ErrorSeverity || (ErrorSeverity = {}));
export class MCPError extends Error {
    code;
    severity;
    context;
    isRetryable;
    originalError;
    constructor(code, message, severity = ErrorSeverity.MEDIUM, context = {}, isRetryable = false, originalError) {
        super(message);
        this.name = 'MCPError';
        this.code = code;
        this.severity = severity;
        this.context = {
            ...context,
            timestamp: context.timestamp || new Date(),
        };
        this.isRetryable = isRetryable;
        this.originalError = originalError;
        // Maintain proper stack trace
        if (Error.captureStackTrace) {
            Error.captureStackTrace(this, MCPError);
        }
    }
    toJSON() {
        return {
            name: this.name,
            message: this.message,
            code: this.code,
            severity: this.severity,
            context: this.context,
            isRetryable: this.isRetryable,
            stack: this.stack,
            originalError: this.originalError ? {
                name: this.originalError.name,
                message: this.originalError.message,
                stack: this.originalError.stack,
            } : undefined,
        };
    }
}
/**
 * Factory functions for common error types
 */
export class ErrorFactory {
    static configurationError(message, context) {
        return new MCPError(ErrorCode.CONFIG_INVALID, message, ErrorSeverity.HIGH, context, false);
    }
    static s3Error(message, context, originalError) {
        const isRetryable = this.isS3ErrorRetryable(originalError);
        const severity = this.getS3ErrorSeverity(originalError);
        return new MCPError(ErrorCode.S3_UPLOAD_FAILED, message, severity, context, isRetryable, originalError);
    }
    static processingError(message, context, originalError) {
        return new MCPError(ErrorCode.RESPONSE_PROCESSING_FAILED, message, ErrorSeverity.MEDIUM, context, true, originalError);
    }
    static networkError(message, context, originalError) {
        return new MCPError(ErrorCode.NETWORK_CONNECTION_FAILED, message, ErrorSeverity.HIGH, context, true, originalError);
    }
    static validationError(message, context) {
        return new MCPError(ErrorCode.VALIDATION_FAILED, message, ErrorSeverity.LOW, context, false);
    }
    static isS3ErrorRetryable(error) {
        if (!error)
            return true;
        const retryableErrors = [
            'RequestTimeout',
            'ServiceUnavailable',
            'Throttling',
            'InternalError',
            'SlowDown',
        ];
        return retryableErrors.some(retryableError => error.message.includes(retryableError) || error.name.includes(retryableError));
    }
    static getS3ErrorSeverity(error) {
        if (!error)
            return ErrorSeverity.MEDIUM;
        if (error.message.includes('AccessDenied') || error.message.includes('Forbidden')) {
            return ErrorSeverity.HIGH;
        }
        if (error.message.includes('NoSuchBucket') || error.message.includes('NotFound')) {
            return ErrorSeverity.CRITICAL;
        }
        return ErrorSeverity.MEDIUM;
    }
}
/**
 * Error classification utilities
 */
export class ErrorClassifier {
    static isRetryable(error) {
        if (error instanceof MCPError) {
            return error.isRetryable;
        }
        // Check for common retryable error patterns
        const retryablePatterns = [
            /timeout/i,
            /connection/i,
            /network/i,
            /temporary/i,
            /throttl/i,
            /rate limit/i,
            /service unavailable/i,
        ];
        return retryablePatterns.some(pattern => pattern.test(error.message) || pattern.test(error.name));
    }
    static getSeverity(error) {
        if (error instanceof MCPError) {
            return error.severity;
        }
        // Classify based on error type and message
        if (error.name === 'ValidationError' || error.message.includes('validation')) {
            return ErrorSeverity.LOW;
        }
        if (error.name === 'TypeError' || error.name === 'ReferenceError') {
            return ErrorSeverity.HIGH;
        }
        if (error.message.includes('ENOTFOUND') || error.message.includes('ECONNREFUSED')) {
            return ErrorSeverity.HIGH;
        }
        return ErrorSeverity.MEDIUM;
    }
    static shouldAlert(error) {
        const severity = this.getSeverity(error);
        return severity === ErrorSeverity.HIGH || severity === ErrorSeverity.CRITICAL;
    }
}
//# sourceMappingURL=error-types.js.map