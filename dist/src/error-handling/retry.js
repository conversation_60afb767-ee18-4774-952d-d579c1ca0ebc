/**
 * Retry mechanism with exponential backoff and jitter
 */
import logger from '../logger.js';
import { MCPError, ErrorClassifier } from './error-types.js';
export class RetryManager {
    static defaultOptions = {
        maxAttempts: 3,
        baseDelay: 1000,
        maxDelay: 30000,
        backoffMultiplier: 2,
        jitter: true,
        retryCondition: (error) => ErrorClassifier.isRetryable(error),
    };
    /**
     * Execute a function with retry logic
     */
    static async execute(operation, options = {}, context) {
        const config = { ...this.defaultOptions, ...options };
        const startTime = Date.now();
        let lastError;
        logger.debug('Starting retry operation', {
            operationName: context?.operationName,
            requestId: context?.requestId,
            maxAttempts: config.maxAttempts,
            baseDelay: config.baseDelay,
        });
        for (let attempt = 1; attempt <= config.maxAttempts; attempt++) {
            try {
                const result = await operation();
                const totalTime = Date.now() - startTime;
                logger.debug('Retry operation succeeded', {
                    operationName: context?.operationName,
                    requestId: context?.requestId,
                    attempt,
                    totalTime,
                });
                return {
                    success: true,
                    result,
                    attempts: attempt,
                    totalTime,
                };
            }
            catch (error) {
                lastError = error instanceof Error ? error : new Error(String(error));
                logger.warn('Retry operation failed', {
                    operationName: context?.operationName,
                    requestId: context?.requestId,
                    attempt,
                    error: lastError.message,
                    isRetryable: config.retryCondition(lastError),
                });
                // Check if we should retry
                if (attempt === config.maxAttempts || !config.retryCondition(lastError)) {
                    break;
                }
                // Calculate delay with exponential backoff and jitter
                const delay = this.calculateDelay(attempt, config);
                logger.debug('Retrying operation after delay', {
                    operationName: context?.operationName,
                    requestId: context?.requestId,
                    attempt,
                    delay,
                    nextAttempt: attempt + 1,
                });
                // Call retry callback if provided
                if (config.onRetry) {
                    config.onRetry(lastError, attempt);
                }
                // Wait before retrying
                await this.sleep(delay);
            }
        }
        const totalTime = Date.now() - startTime;
        logger.error('Retry operation failed after all attempts', {
            operationName: context?.operationName,
            requestId: context?.requestId,
            maxAttempts: config.maxAttempts,
            totalTime,
            finalError: lastError?.message,
        });
        return {
            success: false,
            error: lastError,
            attempts: config.maxAttempts,
            totalTime,
        };
    }
    /**
     * Calculate delay with exponential backoff and optional jitter
     */
    static calculateDelay(attempt, options) {
        let delay = options.baseDelay * Math.pow(options.backoffMultiplier, attempt - 1);
        // Apply maximum delay limit
        delay = Math.min(delay, options.maxDelay);
        // Add jitter to prevent thundering herd
        if (options.jitter) {
            delay = delay * (0.5 + Math.random() * 0.5);
        }
        return Math.floor(delay);
    }
    /**
     * Sleep for specified milliseconds
     */
    static sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
    /**
     * Create a retry wrapper for a function
     */
    static wrap(fn, options = {}, operationName) {
        return async (...args) => {
            const result = await this.execute(() => fn(...args), options, operationName ? { operationName } : {});
            if (result.success) {
                return result.result;
            }
            else {
                throw result.error || new Error('Operation failed after retries');
            }
        };
    }
}
/**
 * Decorator for adding retry logic to methods
 */
export function retry(options = {}) {
    return function (target, propertyKey, descriptor) {
        const originalMethod = descriptor.value;
        const operationName = `${target.constructor.name}.${propertyKey}`;
        descriptor.value = async function (...args) {
            const result = await RetryManager.execute(() => originalMethod.apply(this, args), options, { operationName });
            if (result.success) {
                return result.result;
            }
            else {
                throw result.error || new Error(`${operationName} failed after retries`);
            }
        };
        return descriptor;
    };
}
/**
 * Circuit breaker pattern for preventing cascading failures
 */
export class CircuitBreaker {
    failureThreshold;
    recoveryTimeout;
    name;
    failures = 0;
    lastFailureTime = 0;
    state = 'CLOSED';
    constructor(failureThreshold = 5, recoveryTimeout = 60000, name = 'CircuitBreaker') {
        this.failureThreshold = failureThreshold;
        this.recoveryTimeout = recoveryTimeout;
        this.name = name;
    }
    async execute(operation) {
        if (this.state === 'OPEN') {
            if (Date.now() - this.lastFailureTime > this.recoveryTimeout) {
                this.state = 'HALF_OPEN';
                logger.info('Circuit breaker transitioning to HALF_OPEN', { name: this.name });
            }
            else {
                throw new MCPError('CIRCUIT_BREAKER_OPEN', `Circuit breaker ${this.name} is OPEN`, 'HIGH', { metadata: { circuitBreakerName: this.name } });
            }
        }
        try {
            const result = await operation();
            if (this.state === 'HALF_OPEN') {
                this.reset();
            }
            return result;
        }
        catch (error) {
            this.recordFailure();
            throw error;
        }
    }
    recordFailure() {
        this.failures++;
        this.lastFailureTime = Date.now();
        if (this.failures >= this.failureThreshold) {
            this.state = 'OPEN';
            logger.warn('Circuit breaker opened due to failures', {
                name: this.name,
                failures: this.failures,
                threshold: this.failureThreshold,
            });
        }
    }
    reset() {
        this.failures = 0;
        this.state = 'CLOSED';
        logger.info('Circuit breaker reset to CLOSED', { name: this.name });
    }
    getState() {
        return {
            state: this.state,
            failures: this.failures,
            lastFailureTime: this.lastFailureTime,
        };
    }
}
//# sourceMappingURL=retry.js.map