{"version": 3, "file": "enhanced-logger.js", "sourceRoot": "", "sources": ["../../../src/error-handling/enhanced-logger.ts"], "names": [], "mappings": "AAAA;;GAEG;AAEH,OAAO,OAAO,MAAM,SAAS,CAAC;AAC9B,OAAO,EAAE,SAAS,EAAE,MAAM,cAAc,CAAC;AACzC,OAAO,EAAE,QAAQ,EAAE,aAAa,EAAE,MAAM,kBAAkB,CAAC;AAC3D,OAAO,EAAE,OAAO,EAAE,MAAM,MAAM,CAAC;AAC/B,OAAO,EAAE,SAAS,EAAE,MAAM,IAAI,CAAC;AAE/B,oBAAoB;AACpB,MAAM,MAAM,GAAG,SAAS,EAAE,CAAC;AAE3B,8BAA8B;AAC9B,MAAM,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;AAChD,IAAI,CAAC;IACH,SAAS,CAAC,MAAM,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC;AACzC,CAAC;AAAC,OAAO,KAAK,EAAE,CAAC;IACf,8CAA8C;AAChD,CAAC;AAED,oCAAoC;AACpC,MAAM,YAAY,GAAG;IACnB,MAAM,EAAE;QACN,KAAK,EAAE,CAAC;QACR,IAAI,EAAE,CAAC;QACP,IAAI,EAAE,CAAC;QACP,IAAI,EAAE,CAAC;QACP,KAAK,EAAE,CAAC;QACR,OAAO,EAAE,CAAC;KACX;IACD,MAAM,EAAE;QACN,KAAK,EAAE,KAAK;QACZ,IAAI,EAAE,QAAQ;QACd,IAAI,EAAE,OAAO;QACb,IAAI,EAAE,SAAS;QACf,KAAK,EAAE,MAAM;QACb,OAAO,EAAE,MAAM;KAChB;CACF,CAAC;AAEF,wBAAwB;AACxB,OAAO,CAAC,SAAS,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC;AAEvC,uCAAuC;AACvC,MAAM,gBAAgB,GAAG,OAAO,CAAC,MAAM,CAAC,OAAO,CAC7C,OAAO,CAAC,MAAM,CAAC,SAAS,CAAC;IACvB,MAAM,EAAE,yBAAyB;CAClC,CAAC,EACF,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC,EACtC,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,EAAE,EAAE;IAC7B,MAAM,EAAE,SAAS,EAAE,KAAK,EAAE,OAAO,EAAE,OAAO,EAAE,GAAG,IAAI,EAAE,GAAG,IAAI,CAAC;IAE7D,0BAA0B;IAC1B,IAAI,IAAI,CAAC,OAAO,CAAC,YAAY,QAAQ,EAAE,CAAC;QACtC,IAAI,CAAC,OAAO,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE,CAAC;IACzC,CAAC;IAED,MAAM,QAAQ,GAAG;QACf,SAAS;QACT,KAAK;QACL,OAAO;QACP,OAAO;QACP,GAAG,IAAI;KACR,CAAC;IAEF,OAAO,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;AAClC,CAAC,CAAC,CACH,CAAC;AAEF,iCAAiC;AACjC,MAAM,aAAa,GAAG,OAAO,CAAC,MAAM,CAAC,OAAO,CAC1C,OAAO,CAAC,MAAM,CAAC,QAAQ,EAAE,EACzB,OAAO,CAAC,MAAM,CAAC,SAAS,CAAC;IACvB,MAAM,EAAE,UAAU;CACnB,CAAC,EACF,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,EAAE,EAAE;IAC7B,MAAM,EAAE,SAAS,EAAE,KAAK,EAAE,OAAO,EAAE,OAAO,EAAE,GAAG,IAAI,EAAE,GAAG,IAAI,CAAC;IAE7D,IAAI,OAAO,GAAG,EAAE,CAAC;IACjB,IAAI,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;QACjC,OAAO,GAAG,IAAI,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE,CAAC;IACvC,CAAC;IAED,OAAO,GAAG,SAAS,KAAK,OAAO,KAAK,KAAK,KAAK,OAAO,GAAG,OAAO,EAAE,CAAC;AACpE,CAAC,CAAC,CACH,CAAC;AAEF,2BAA2B;AAC3B,MAAM,UAAU,GAAG,OAAO,CAAC,YAAY,CAAC;IACtC,KAAK,EAAE,MAAM,CAAC,OAAO,CAAC,KAAK;IAC3B,MAAM,EAAE,YAAY,CAAC,MAAM;IAC3B,MAAM,EAAE,gBAAgB;IACxB,WAAW,EAAE;QACX,OAAO,EAAE,MAAM,CAAC,GAAG,CAAC,UAAU;QAC9B,WAAW,EAAE,OAAO,CAAC,GAAG,CAAC,UAAU,CAAC,IAAI,aAAa;QACrD,OAAO,EAAE,OAAO,CAAC,GAAG,CAAC,qBAAqB,CAAC,IAAI,OAAO;KACvD;IACD,UAAU,EAAE;QACV,8BAA8B;QAC9B,IAAI,OAAO,CAAC,UAAU,CAAC,IAAI,CAAC;YAC1B,QAAQ,EAAE,MAAM,CAAC,OAAO,CAAC,QAAQ;YACjC,OAAO,EAAE,QAAQ,EAAE,OAAO;YAC1B,QAAQ,EAAE,EAAE;YACZ,QAAQ,EAAE,IAAI;SACf,CAAC;QACF,2BAA2B;QAC3B,IAAI,OAAO,CAAC,UAAU,CAAC,IAAI,CAAC;YAC1B,QAAQ,EAAE,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,MAAM,EAAE,YAAY,CAAC;YAC/D,KAAK,EAAE,OAAO;YACd,OAAO,EAAE,OAAO,EAAE,MAAM;YACxB,QAAQ,EAAE,CAAC;YACX,QAAQ,EAAE,IAAI;SACf,CAAC;QACF,mBAAmB;QACnB,IAAI,OAAO,CAAC,UAAU,CAAC,IAAI,CAAC;YAC1B,QAAQ,EAAE,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,MAAM,EAAE,kBAAkB,CAAC;YACrE,KAAK,EAAE,MAAM;YACb,OAAO,EAAE,OAAO,EAAE,MAAM;YACxB,QAAQ,EAAE,CAAC;YACX,MAAM,EAAE,OAAO,CAAC,MAAM,CAAC,OAAO,CAC5B,gBAAgB,EAChB,OAAO,CAAC,MAAM,CAAC,CAAC,IAAI,EAAE,EAAE;gBACtB,OAAO,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC;YAC/C,CAAC,CAAC,EAAE,CACL;SACF,CAAC;KACH;IACD,4CAA4C;IAC5C,iBAAiB,EAAE;QACjB,IAAI,OAAO,CAAC,UAAU,CAAC,IAAI,CAAC;YAC1B,QAAQ,EAAE,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,MAAM,EAAE,iBAAiB,CAAC;YACpE,OAAO,EAAE,OAAO,EAAE,MAAM;YACxB,QAAQ,EAAE,CAAC;SACZ,CAAC;KACH;IACD,iBAAiB,EAAE;QACjB,IAAI,OAAO,CAAC,UAAU,CAAC,IAAI,CAAC;YAC1B,QAAQ,EAAE,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,MAAM,EAAE,iBAAiB,CAAC;YACpE,OAAO,EAAE,OAAO,EAAE,MAAM;YACxB,QAAQ,EAAE,CAAC;SACZ,CAAC;KACH;CACF,CAAC,CAAC;AAEH,mCAAmC;AACnC,IAAI,MAAM,CAAC,OAAO,CAAC,oBAAoB,EAAE,CAAC;IACxC,UAAU,CAAC,GAAG,CAAC,IAAI,OAAO,CAAC,UAAU,CAAC,OAAO,CAAC;QAC5C,MAAM,EAAE,aAAa;QACrB,KAAK,EAAE,MAAM,CAAC,OAAO,CAAC,KAAK;KAC5B,CAAC,CAAC,CAAC;AACN,CAAC;AAED,0CAA0C;AAC1C,MAAM,OAAO,cAAc;IACjB,OAAO,CAAiB;IACxB,OAAO,GAAwB,IAAI,GAAG,EAAE,CAAC;IACzC,kBAAkB,GAAuE,IAAI,GAAG,EAAE,CAAC;IAE3G,YAAY,aAA6B;QACvC,IAAI,CAAC,OAAO,GAAG,aAAa,CAAC;IAC/B,CAAC;IAED,2BAA2B;IAC3B,KAAK,CAAC,OAAe,EAAE,IAAU;QAC/B,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;QAClC,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,CAAC;IACjC,CAAC;IAED,IAAI,CAAC,OAAe,EAAE,IAAU;QAC9B,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;QACjC,IAAI,CAAC,eAAe,CAAC,UAAU,CAAC,CAAC;IACnC,CAAC;IAED,IAAI,CAAC,OAAe,EAAE,IAAU;QAC9B,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;IACnC,CAAC;IAED,IAAI,CAAC,OAAe,EAAE,IAAU;QAC9B,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;IACnC,CAAC;IAED,KAAK,CAAC,OAAe,EAAE,IAAU;QAC/B,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;IACpC,CAAC;IAED,OAAO,CAAC,OAAe,EAAE,IAAU;QACjC,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;IACtC,CAAC;IAED,GAAG,CAAC,KAAa,EAAE,OAAe,EAAE,IAAU;QAC5C,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,EAAE,OAAO,EAAE,IAAI,CAAC,CAAC;IACzC,CAAC;IAED,0CAA0C;IAC1C,QAAQ,CAAC,KAAuB,EAAE,OAAa;QAC7C,MAAM,IAAI,GAAG;YACX,GAAG,OAAO;YACV,KAAK,EAAE,KAAK,YAAY,QAAQ,CAAC,CAAC,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;gBAClD,IAAI,EAAE,KAAK,CAAC,IAAI;gBAChB,OAAO,EAAE,KAAK,CAAC,OAAO;gBACtB,KAAK,EAAE,KAAK,CAAC,KAAK;aACnB;SACF,CAAC;QAEF,IAAI,KAAK,YAAY,QAAQ,EAAE,CAAC;YAC9B,MAAM,KAAK,GAAG,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;YACtD,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,EAAE,aAAa,KAAK,CAAC,OAAO,EAAE,EAAE,IAAI,CAAC,CAAC;QAC9D,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;QAC1C,CAAC;QAED,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,CAAC;IACjC,CAAC;IAED,cAAc,CAAC,SAAiB,EAAE,QAAgB,EAAE,IAAU;QAC5D,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,gBAAgB,SAAS,EAAE,EAAE;YAC7C,GAAG,IAAI;YACP,SAAS;YACT,QAAQ;YACR,cAAc,EAAE,IAAI;SACrB,CAAC,CAAC;QAEH,6BAA6B;QAC7B,MAAM,OAAO,GAAG,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,SAAS,CAAC,IAAI,EAAE,KAAK,EAAE,CAAC,EAAE,SAAS,EAAE,CAAC,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC;QACjG,OAAO,CAAC,KAAK,EAAE,CAAC;QAChB,OAAO,CAAC,SAAS,IAAI,QAAQ,CAAC;QAC9B,OAAO,CAAC,OAAO,GAAG,OAAO,CAAC,SAAS,GAAG,OAAO,CAAC,KAAK,CAAC;QACpD,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;IAClD,CAAC;IAED,WAAW,CAAC,KAAa,EAAE,IAAU;QACnC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,aAAa,KAAK,EAAE,EAAE;YACtC,GAAG,IAAI;YACP,aAAa,EAAE,IAAI;SACpB,CAAC,CAAC;QACH,IAAI,CAAC,eAAe,CAAC,iBAAiB,CAAC,CAAC;IAC1C,CAAC;IAED,QAAQ,CAAC,MAAc,EAAE,IAAU;QACjC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,UAAU,MAAM,EAAE,EAAE;YACpC,GAAG,IAAI;YACP,QAAQ,EAAE,IAAI;SACf,CAAC,CAAC;IACL,CAAC;IAED,UAAU,CAAC,MAAc,EAAE,IAAY,EAAE,UAAkB,EAAE,QAAgB,EAAE,IAAU;QACvF,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,MAAM,IAAI,IAAI,IAAI,UAAU,EAAE,EAAE;YACnD,GAAG,IAAI;YACP,MAAM;YACN,IAAI;YACJ,UAAU;YACV,QAAQ;YACR,UAAU,EAAE,IAAI;SACjB,CAAC,CAAC;IACL,CAAC;IAED,yBAAyB;IACjB,eAAe,CAAC,MAAc;QACpC,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;QAC9C,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,MAAM,EAAE,OAAO,GAAG,CAAC,CAAC,CAAC;IACxC,CAAC;IAED,UAAU;QACR,OAAO,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;IAC1C,CAAC;IAED,qBAAqB;QACnB,OAAO,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;IACrD,CAAC;IAED,YAAY;QACV,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC;QACrB,IAAI,CAAC,kBAAkB,CAAC,KAAK,EAAE,CAAC;IAClC,CAAC;IAED,uBAAuB;IACvB,cAAc,CAAC,SAAiB,EAAE,MAA4C,EAAE,OAAa;QAC3F,MAAM,KAAK,GAAG,MAAM,KAAK,SAAS,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC;QACrD,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,EAAE,iBAAiB,SAAS,OAAO,MAAM,EAAE,EAAE;YACjE,SAAS;YACT,MAAM;YACN,OAAO;YACP,WAAW,EAAE,IAAI;SAClB,CAAC,CAAC;IACL,CAAC;IAEO,kBAAkB,CAAC,QAAuB;QAChD,QAAQ,QAAQ,EAAE,CAAC;YACjB,KAAK,aAAa,CAAC,QAAQ;gBACzB,OAAO,OAAO,CAAC;YACjB,KAAK,aAAa,CAAC,IAAI;gBACrB,OAAO,OAAO,CAAC;YACjB,KAAK,aAAa,CAAC,MAAM;gBACvB,OAAO,MAAM,CAAC;YAChB,KAAK,aAAa,CAAC,GAAG;gBACpB,OAAO,MAAM,CAAC;YAChB;gBACE,OAAO,MAAM,CAAC;QAClB,CAAC;IACH,CAAC;IAED,8CAA8C;IAC9C,KAAK,CAAC,OAA4B;QAChC,MAAM,YAAY,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;QACjD,OAAO,IAAI,cAAc,CAAC,YAAY,CAAC,CAAC;IAC1C,CAAC;CACF;AAED,iDAAiD;AACjD,MAAM,cAAc,GAAG,IAAI,cAAc,CAAC,UAAU,CAAC,CAAC;AAEtD,eAAe,cAAc,CAAC"}