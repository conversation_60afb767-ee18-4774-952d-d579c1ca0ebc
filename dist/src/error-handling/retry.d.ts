/**
 * Retry mechanism with exponential backoff and jitter
 */
export interface RetryOptions {
    maxAttempts: number;
    baseDelay: number;
    maxDelay: number;
    backoffMultiplier: number;
    jitter: boolean;
    retryCondition?: (error: Error) => boolean;
    onRetry?: (error: Error, attempt: number) => void;
}
export interface RetryResult<T> {
    success: boolean;
    result?: T;
    error?: Error | undefined;
    attempts: number;
    totalTime: number;
}
export declare class RetryManager {
    private static defaultOptions;
    /**
     * Execute a function with retry logic
     */
    static execute<T>(operation: () => Promise<T>, options?: Partial<RetryOptions>, context?: {
        operationName?: string;
        requestId?: string;
    }): Promise<RetryResult<T>>;
    /**
     * Calculate delay with exponential backoff and optional jitter
     */
    private static calculateDelay;
    /**
     * Sleep for specified milliseconds
     */
    private static sleep;
    /**
     * Create a retry wrapper for a function
     */
    static wrap<T extends any[], R>(fn: (...args: T) => Promise<R>, options?: Partial<RetryOptions>, operationName?: string): (...args: T) => Promise<R>;
}
/**
 * Decorator for adding retry logic to methods
 */
export declare function retry(options?: Partial<RetryOptions>): (target: any, propertyKey: string, descriptor: PropertyDescriptor) => PropertyDescriptor;
/**
 * Circuit breaker pattern for preventing cascading failures
 */
export declare class CircuitBreaker {
    private readonly failureThreshold;
    private readonly recoveryTimeout;
    private readonly name;
    private failures;
    private lastFailureTime;
    private state;
    constructor(failureThreshold?: number, recoveryTimeout?: number, name?: string);
    execute<T>(operation: () => Promise<T>): Promise<T>;
    private recordFailure;
    private reset;
    getState(): {
        state: string;
        failures: number;
        lastFailureTime: number;
    };
}
//# sourceMappingURL=retry.d.ts.map