{"version": 3, "file": "health-monitor.js", "sourceRoot": "", "sources": ["../../../src/error-handling/health-monitor.ts"], "names": [], "mappings": "AAAA;;GAEG;AAEH,OAAO,cAAc,MAAM,sBAAsB,CAAC;AAClD,OAAO,EAAE,SAAS,EAAE,MAAM,cAAc,CAAC;AAyBzC,MAAM,OAAO,aAAa;IAChB,MAAM,GAA6B,IAAI,GAAG,EAAE,CAAC;IAC7C,OAAO,GAAmC,IAAI,GAAG,EAAE,CAAC;IACpD,SAAS,GAAgC,IAAI,GAAG,EAAE,CAAC;IACnD,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;IACvB,MAAM,GAAG,cAAc,CAAC,KAAK,CAAC,EAAE,SAAS,EAAE,eAAe,EAAE,CAAC,CAAC;IAEtE;QACE,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,4BAA4B,CAAC,CAAC;IACjD,CAAC;IAED;;OAEG;IACH,aAAa,CAAC,KAAkB;QAC9B,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;QACnC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,yBAAyB,EAAE;YAC1C,IAAI,EAAE,KAAK,CAAC,IAAI;YAChB,QAAQ,EAAE,KAAK,CAAC,QAAQ;YACxB,QAAQ,EAAE,KAAK,CAAC,QAAQ;SACzB,CAAC,CAAC;QAEH,0BAA0B;QAC1B,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC,CAAC;IACjC,CAAC;IAED;;OAEG;IACH,eAAe,CAAC,IAAY;QAC1B,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;QACzB,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;QAE1B,MAAM,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;QAC1C,IAAI,QAAQ,EAAE,CAAC;YACb,aAAa,CAAC,QAAQ,CAAC,CAAC;YACxB,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;QAC9B,CAAC;QAED,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,2BAA2B,EAAE,EAAE,IAAI,EAAE,CAAC,CAAC;IAC1D,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,QAAQ,CAAC,IAAY;QACzB,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;QACpC,IAAI,CAAC,KAAK,EAAE,CAAC;YACX,MAAM,IAAI,KAAK,CAAC,iBAAiB,IAAI,aAAa,CAAC,CAAC;QACtD,CAAC;QAED,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAE7B,IAAI,CAAC;YACH,yBAAyB;YACzB,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,IAAI,CAAC;gBAChC,KAAK,CAAC,KAAK,EAAE;gBACb,IAAI,CAAC,oBAAoB,CAAC,KAAK,CAAC,OAAO,CAAC;aACzC,CAAC,CAAC;YAEH,MAAM,QAAQ,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;YACxC,MAAM,WAAW,GAAG;gBAClB,GAAG,MAAM;gBACT,SAAS,EAAE,IAAI,IAAI,EAAE;gBACrB,QAAQ;aACT,CAAC;YAEF,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,EAAE,WAAW,CAAC,CAAC;YAEpC,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,IAAI,EAAE,WAAW,CAAC,MAAM,EAAE;gBACnD,OAAO,EAAE,WAAW,CAAC,OAAO;gBAC5B,QAAQ;gBACR,OAAO,EAAE,WAAW,CAAC,OAAO;aAC7B,CAAC,CAAC;YAEH,OAAO,WAAW,CAAC;QACrB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,QAAQ,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;YACxC,MAAM,MAAM,GAAsB;gBAChC,MAAM,EAAE,WAAW;gBACnB,OAAO,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC;gBAC/D,SAAS,EAAE,IAAI,IAAI,EAAE;gBACrB,QAAQ;aACT,CAAC;YAEF,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;YAE/B,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,IAAI,EAAE,WAAW,EAAE;gBAC5C,KAAK,EAAE,MAAM,CAAC,OAAO;gBACrB,QAAQ;aACT,CAAC,CAAC;YAEH,OAAO,MAAM,CAAC;QAChB,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,YAAY;QAChB,MAAM,OAAO,GAAsC,EAAE,CAAC;QAEtD,MAAM,QAAQ,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC,CAAC,GAAG,CAAC,KAAK,EAAE,IAAI,EAAE,EAAE;YACjE,IAAI,CAAC;gBACH,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;gBACzC,OAAO,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC;YACzB,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,OAAO,CAAC,IAAI,CAAC,GAAG;oBACd,MAAM,EAAE,WAAW;oBACnB,OAAO,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC;oBAC/D,SAAS,EAAE,IAAI,IAAI,EAAE;oBACrB,QAAQ,EAAE,CAAC;iBACZ,CAAC;YACJ,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,MAAM,OAAO,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC;QACnC,OAAO,OAAO,CAAC;IACjB,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,eAAe;QACnB,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,YAAY,EAAE,CAAC;QACzC,MAAM,OAAO,GAAG,IAAI,CAAC,sBAAsB,CAAC,MAAM,CAAC,CAAC;QAEpD,OAAO;YACL,OAAO;YACP,MAAM;YACN,SAAS,EAAE,IAAI,IAAI,EAAE;YACrB,MAAM,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,SAAS;SACpC,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,gBAAgB;QACd,MAAM,MAAM,GAAG,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAChD,MAAM,OAAO,GAAG,IAAI,CAAC,sBAAsB,CAAC,MAAM,CAAC,CAAC;QAEpD,OAAO;YACL,OAAO;YACP,MAAM;YACN,SAAS,EAAE,IAAI,IAAI,EAAE;YACrB,MAAM,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,SAAS;SACpC,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,kBAAkB,CAAC,KAAkB;QAC3C,oBAAoB;QACpB,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,KAAK,CAAC,CAAC,KAAK,EAAE,EAAE;YACxC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,6BAA6B,EAAE;gBAC/C,IAAI,EAAE,KAAK,CAAC,IAAI;gBAChB,KAAK,EAAE,KAAK,CAAC,OAAO;aACrB,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,2BAA2B;QAC3B,MAAM,QAAQ,GAAG,WAAW,CAAC,GAAG,EAAE;YAChC,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,KAAK,CAAC,CAAC,KAAK,EAAE,EAAE;gBACxC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,8BAA8B,EAAE;oBAChD,IAAI,EAAE,KAAK,CAAC,IAAI;oBAChB,KAAK,EAAE,KAAK,CAAC,OAAO;iBACrB,CAAC,CAAC;YACL,CAAC,CAAC,CAAC;QACL,CAAC,EAAE,KAAK,CAAC,QAAQ,CAAC,CAAC;QAEnB,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;IAC3C,CAAC;IAED;;OAEG;IACK,oBAAoB,CAAC,OAAe;QAC1C,OAAO,IAAI,OAAO,CAAC,CAAC,CAAC,EAAE,MAAM,EAAE,EAAE;YAC/B,UAAU,CAAC,GAAG,EAAE;gBACd,MAAM,CAAC,IAAI,KAAK,CAAC,gCAAgC,OAAO,IAAI,CAAC,CAAC,CAAC;YACjE,CAAC,EAAE,OAAO,CAAC,CAAC;QACd,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACK,sBAAsB,CAAC,MAAyC;QACtE,MAAM,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;QAEtC,IAAI,OAAO,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACzB,OAAO,SAAS,CAAC;QACnB,CAAC;QAED,MAAM,eAAe,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,KAAK,EAAE,EAAE;YAClD,MAAM,SAAS,GAAG,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,KAAK,CAAC,CAAC;YAC7C,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,SAAU,CAAC,CAAC;YAC1C,OAAO,KAAK,EAAE,QAAQ,CAAC;QACzB,CAAC,CAAC,CAAC;QAEH,0DAA0D;QAC1D,IAAI,eAAe,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,MAAM,CAAC,MAAM,KAAK,WAAW,CAAC,EAAE,CAAC;YAClE,OAAO,WAAW,CAAC;QACrB,CAAC;QAED,4DAA4D;QAC5D,IAAI,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,MAAM,CAAC,MAAM,KAAK,WAAW,IAAI,MAAM,CAAC,MAAM,KAAK,UAAU,CAAC,EAAE,CAAC;YAC1F,OAAO,UAAU,CAAC;QACpB,CAAC;QAED,OAAO,SAAS,CAAC;IACnB,CAAC;IAED;;OAEG;IACH,IAAI;QACF,KAAK,MAAM,QAAQ,IAAI,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,EAAE,CAAC;YAC/C,aAAa,CAAC,QAAQ,CAAC,CAAC;QAC1B,CAAC;QACD,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,CAAC;QACvB,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,wBAAwB,CAAC,CAAC;IAC7C,CAAC;CACF;AAED;;GAEG;AACH,MAAM,UAAU,yBAAyB,CAAC,QAAc;IACtD,MAAM,MAAM,GAAG,SAAS,EAAE,CAAC;IAE3B,MAAM,MAAM,GAAkB;QAC5B,qBAAqB;QACrB;YACE,IAAI,EAAE,QAAQ;YACd,KAAK,EAAE,KAAK,IAAI,EAAE;gBAChB,MAAM,KAAK,GAAG,OAAO,CAAC,WAAW,EAAE,CAAC;gBACpC,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,QAAQ,GAAG,IAAI,GAAG,IAAI,CAAC,CAAC;gBACxD,MAAM,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,SAAS,GAAG,IAAI,GAAG,IAAI,CAAC,CAAC;gBAC1D,MAAM,YAAY,GAAG,CAAC,MAAM,GAAG,OAAO,CAAC,GAAG,GAAG,CAAC;gBAE9C,IAAI,YAAY,GAAG,EAAE,EAAE,CAAC;oBACtB,OAAO;wBACL,MAAM,EAAE,WAAW;wBACnB,OAAO,EAAE,sBAAsB,YAAY,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG;wBACzD,OAAO,EAAE,EAAE,MAAM,EAAE,OAAO,EAAE,YAAY,EAAE;wBAC1C,SAAS,EAAE,IAAI,IAAI,EAAE;wBACrB,QAAQ,EAAE,CAAC;qBACZ,CAAC;gBACJ,CAAC;qBAAM,IAAI,YAAY,GAAG,EAAE,EAAE,CAAC;oBAC7B,OAAO;wBACL,MAAM,EAAE,UAAU;wBAClB,OAAO,EAAE,0BAA0B,YAAY,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG;wBAC7D,OAAO,EAAE,EAAE,MAAM,EAAE,OAAO,EAAE,YAAY,EAAE;wBAC1C,SAAS,EAAE,IAAI,IAAI,EAAE;wBACrB,QAAQ,EAAE,CAAC;qBACZ,CAAC;gBACJ,CAAC;gBAED,OAAO;oBACL,MAAM,EAAE,SAAS;oBACjB,OAAO,EAAE,iBAAiB,YAAY,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG;oBACpD,OAAO,EAAE,EAAE,MAAM,EAAE,OAAO,EAAE,YAAY,EAAE;oBAC1C,SAAS,EAAE,IAAI,IAAI,EAAE;oBACrB,QAAQ,EAAE,CAAC;iBACZ,CAAC;YACJ,CAAC;YACD,QAAQ,EAAE,KAAK,EAAE,aAAa;YAC9B,OAAO,EAAE,IAAI;YACb,QAAQ,EAAE,IAAI;SACf;QAED,wBAAwB;QACxB;YACE,IAAI,EAAE,iBAAiB;YACvB,KAAK,EAAE,KAAK,IAAI,EAAE;gBAChB,IAAI,CAAC,QAAQ,EAAE,CAAC;oBACd,OAAO;wBACL,MAAM,EAAE,UAAU;wBAClB,OAAO,EAAE,yBAAyB;wBAClC,SAAS,EAAE,IAAI,IAAI,EAAE;wBACrB,QAAQ,EAAE,CAAC;qBACZ,CAAC;gBACJ,CAAC;gBAED,MAAM,MAAM,GAAG,MAAM,QAAQ,CAAC,cAAc,EAAE,CAAC;gBAE/C,OAAO;oBACL,MAAM,EAAE,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,WAAW;oBAChD,OAAO,EAAE,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,0BAA0B,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK;oBACnE,OAAO,EAAE,EAAE,MAAM,EAAE,MAAM,CAAC,GAAG,CAAC,UAAU,EAAE,MAAM,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,EAAE;oBACrE,SAAS,EAAE,IAAI,IAAI,EAAE;oBACrB,QAAQ,EAAE,CAAC;iBACZ,CAAC;YACJ,CAAC;YACD,QAAQ,EAAE,KAAK,EAAE,WAAW;YAC5B,OAAO,EAAE,KAAK;YACd,QAAQ,EAAE,IAAI;SACf;KACF,CAAC;IAEF,OAAO,MAAM,CAAC;AAChB,CAAC;AAED;;GAEG;AACH,MAAM,CAAC,MAAM,aAAa,GAAG,IAAI,aAAa,EAAE,CAAC"}