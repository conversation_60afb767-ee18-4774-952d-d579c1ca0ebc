{"version": 3, "file": "retry.d.ts", "sourceRoot": "", "sources": ["../../../src/error-handling/retry.ts"], "names": [], "mappings": "AAAA;;GAEG;AAKH,MAAM,WAAW,YAAY;IAC3B,WAAW,EAAE,MAAM,CAAC;IACpB,SAAS,EAAE,MAAM,CAAC;IAClB,QAAQ,EAAE,MAAM,CAAC;IACjB,iBAAiB,EAAE,MAAM,CAAC;IAC1B,MAAM,EAAE,OAAO,CAAC;IAChB,cAAc,CAAC,EAAE,CAAC,KAAK,EAAE,KAAK,KAAK,OAAO,CAAC;IAC3C,OAAO,CAAC,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,OAAO,EAAE,MAAM,KAAK,IAAI,CAAC;CACnD;AAED,MAAM,WAAW,WAAW,CAAC,CAAC;IAC5B,OAAO,EAAE,OAAO,CAAC;IACjB,MAAM,CAAC,EAAE,CAAC,CAAC;IACX,KAAK,CAAC,EAAE,KAAK,GAAG,SAAS,CAAC;IAC1B,QAAQ,EAAE,MAAM,CAAC;IACjB,SAAS,EAAE,MAAM,CAAC;CACnB;AAED,qBAAa,YAAY;IACvB,OAAO,CAAC,MAAM,CAAC,cAAc,CAO3B;IAEF;;OAEG;WACU,OAAO,CAAC,CAAC,EACpB,SAAS,EAAE,MAAM,OAAO,CAAC,CAAC,CAAC,EAC3B,OAAO,GAAE,OAAO,CAAC,YAAY,CAAM,EACnC,OAAO,CAAC,EAAE;QAAE,aAAa,CAAC,EAAE,MAAM,CAAC;QAAC,SAAS,CAAC,EAAE,MAAM,CAAA;KAAE,GACvD,OAAO,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;IAqF1B;;OAEG;IACH,OAAO,CAAC,MAAM,CAAC,cAAc;IAc7B;;OAEG;IACH,OAAO,CAAC,MAAM,CAAC,KAAK;IAIpB;;OAEG;IACH,MAAM,CAAC,IAAI,CAAC,CAAC,SAAS,GAAG,EAAE,EAAE,CAAC,EAC5B,EAAE,EAAE,CAAC,GAAG,IAAI,EAAE,CAAC,KAAK,OAAO,CAAC,CAAC,CAAC,EAC9B,OAAO,GAAE,OAAO,CAAC,YAAY,CAAM,EACnC,aAAa,CAAC,EAAE,MAAM,GACrB,CAAC,GAAG,IAAI,EAAE,CAAC,KAAK,OAAO,CAAC,CAAC,CAAC;CAe9B;AAED;;GAEG;AACH,wBAAgB,KAAK,CAAC,OAAO,GAAE,OAAO,CAAC,YAAY,CAAM,IACtC,QAAQ,GAAG,EAAE,aAAa,MAAM,EAAE,YAAY,kBAAkB,wBAoBlF;AAED;;GAEG;AACH,qBAAa,cAAc;IAMvB,OAAO,CAAC,QAAQ,CAAC,gBAAgB;IACjC,OAAO,CAAC,QAAQ,CAAC,eAAe;IAChC,OAAO,CAAC,QAAQ,CAAC,IAAI;IAPvB,OAAO,CAAC,QAAQ,CAAK;IACrB,OAAO,CAAC,eAAe,CAAK;IAC5B,OAAO,CAAC,KAAK,CAA6C;gBAGvC,gBAAgB,GAAE,MAAU,EAC5B,eAAe,GAAE,MAAc,EAC/B,IAAI,GAAE,MAAyB;IAG5C,OAAO,CAAC,CAAC,EAAE,SAAS,EAAE,MAAM,OAAO,CAAC,CAAC,CAAC,GAAG,OAAO,CAAC,CAAC,CAAC;IA6BzD,OAAO,CAAC,aAAa;IAcrB,OAAO,CAAC,KAAK;IAMb,QAAQ,IAAI;QAAE,KAAK,EAAE,MAAM,CAAC;QAAC,QAAQ,EAAE,MAAM,CAAC;QAAC,eAAe,EAAE,MAAM,CAAA;KAAE;CAOzE"}