{"version": 3, "file": "error-types.js", "sourceRoot": "", "sources": ["../../../src/error-handling/error-types.ts"], "names": [], "mappings": "AAAA;;GAEG;AAEH,MAAM,CAAN,IAAY,SAkCX;AAlCD,WAAY,SAAS;IACnB,uBAAuB;IACvB,8CAAiC,CAAA;IACjC,8CAAiC,CAAA;IAEjC,gBAAgB;IAChB,0DAA6C,CAAA;IAC7C,kDAAqC,CAAA;IACrC,wDAA2C,CAAA;IAC3C,kDAAqC,CAAA;IACrC,oDAAuC,CAAA;IAEvC,sBAAsB;IACtB,sDAAyC,CAAA;IACzC,sDAAyC,CAAA;IACzC,wDAA2C,CAAA;IAE3C,oBAAoB;IACpB,sEAAyD,CAAA;IACzD,wEAA2D,CAAA;IAC3D,oEAAuD,CAAA;IAEvD,iBAAiB;IACjB,gDAAmC,CAAA;IACnC,oEAAuD,CAAA;IAEvD,oBAAoB;IACpB,oDAAuC,CAAA;IACvC,4CAA+B,CAAA;IAE/B,gBAAgB;IAChB,0CAA6B,CAAA;IAC7B,sDAAyC,CAAA;IACzC,wDAA2C,CAAA;AAC7C,CAAC,EAlCW,SAAS,KAAT,SAAS,QAkCpB;AAED,MAAM,CAAN,IAAY,aAKX;AALD,WAAY,aAAa;IACvB,4BAAW,CAAA;IACX,kCAAiB,CAAA;IACjB,8BAAa,CAAA;IACb,sCAAqB,CAAA;AACvB,CAAC,EALW,aAAa,KAAb,aAAa,QAKxB;AAYD,MAAM,OAAO,QAAS,SAAQ,KAAK;IACjB,IAAI,CAAY;IAChB,QAAQ,CAAgB;IACxB,OAAO,CAAe;IACtB,WAAW,CAAU;IACrB,aAAa,CAAqB;IAElD,YACE,IAAe,EACf,OAAe,EACf,WAA0B,aAAa,CAAC,MAAM,EAC9C,UAAwB,EAAE,EAC1B,cAAuB,KAAK,EAC5B,aAAqB;QAErB,KAAK,CAAC,OAAO,CAAC,CAAC;QACf,IAAI,CAAC,IAAI,GAAG,UAAU,CAAC;QACvB,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;QACjB,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;QACzB,IAAI,CAAC,OAAO,GAAG;YACb,GAAG,OAAO;YACV,SAAS,EAAE,OAAO,CAAC,SAAS,IAAI,IAAI,IAAI,EAAE;SAC3C,CAAC;QACF,IAAI,CAAC,WAAW,GAAG,WAAW,CAAC;QAC/B,IAAI,CAAC,aAAa,GAAG,aAAa,CAAC;QAEnC,8BAA8B;QAC9B,IAAI,KAAK,CAAC,iBAAiB,EAAE,CAAC;YAC5B,KAAK,CAAC,iBAAiB,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;QAC1C,CAAC;IACH,CAAC;IAED,MAAM;QACJ,OAAO;YACL,IAAI,EAAE,IAAI,CAAC,IAAI;YACf,OAAO,EAAE,IAAI,CAAC,OAAO;YACrB,IAAI,EAAE,IAAI,CAAC,IAAI;YACf,QAAQ,EAAE,IAAI,CAAC,QAAQ;YACvB,OAAO,EAAE,IAAI,CAAC,OAAO;YACrB,WAAW,EAAE,IAAI,CAAC,WAAW;YAC7B,KAAK,EAAE,IAAI,CAAC,KAAK;YACjB,aAAa,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC;gBAClC,IAAI,EAAE,IAAI,CAAC,aAAa,CAAC,IAAI;gBAC7B,OAAO,EAAE,IAAI,CAAC,aAAa,CAAC,OAAO;gBACnC,KAAK,EAAE,IAAI,CAAC,aAAa,CAAC,KAAK;aAChC,CAAC,CAAC,CAAC,SAAS;SACd,CAAC;IACJ,CAAC;CACF;AAED;;GAEG;AACH,MAAM,OAAO,YAAY;IACvB,MAAM,CAAC,kBAAkB,CAAC,OAAe,EAAE,OAAsB;QAC/D,OAAO,IAAI,QAAQ,CACjB,SAAS,CAAC,cAAc,EACxB,OAAO,EACP,aAAa,CAAC,IAAI,EAClB,OAAO,EACP,KAAK,CACN,CAAC;IACJ,CAAC;IAED,MAAM,CAAC,OAAO,CAAC,OAAe,EAAE,OAAsB,EAAE,aAAqB;QAC3E,MAAM,WAAW,GAAG,IAAI,CAAC,kBAAkB,CAAC,aAAa,CAAC,CAAC;QAC3D,MAAM,QAAQ,GAAG,IAAI,CAAC,kBAAkB,CAAC,aAAa,CAAC,CAAC;QAExD,OAAO,IAAI,QAAQ,CACjB,SAAS,CAAC,gBAAgB,EAC1B,OAAO,EACP,QAAQ,EACR,OAAO,EACP,WAAW,EACX,aAAa,CACd,CAAC;IACJ,CAAC;IAED,MAAM,CAAC,eAAe,CAAC,OAAe,EAAE,OAAsB,EAAE,aAAqB;QACnF,OAAO,IAAI,QAAQ,CACjB,SAAS,CAAC,0BAA0B,EACpC,OAAO,EACP,aAAa,CAAC,MAAM,EACpB,OAAO,EACP,IAAI,EACJ,aAAa,CACd,CAAC;IACJ,CAAC;IAED,MAAM,CAAC,YAAY,CAAC,OAAe,EAAE,OAAsB,EAAE,aAAqB;QAChF,OAAO,IAAI,QAAQ,CACjB,SAAS,CAAC,yBAAyB,EACnC,OAAO,EACP,aAAa,CAAC,IAAI,EAClB,OAAO,EACP,IAAI,EACJ,aAAa,CACd,CAAC;IACJ,CAAC;IAED,MAAM,CAAC,eAAe,CAAC,OAAe,EAAE,OAAsB;QAC5D,OAAO,IAAI,QAAQ,CACjB,SAAS,CAAC,iBAAiB,EAC3B,OAAO,EACP,aAAa,CAAC,GAAG,EACjB,OAAO,EACP,KAAK,CACN,CAAC;IACJ,CAAC;IAEO,MAAM,CAAC,kBAAkB,CAAC,KAAa;QAC7C,IAAI,CAAC,KAAK;YAAE,OAAO,IAAI,CAAC;QAExB,MAAM,eAAe,GAAG;YACtB,gBAAgB;YAChB,oBAAoB;YACpB,YAAY;YACZ,eAAe;YACf,UAAU;SACX,CAAC;QAEF,OAAO,eAAe,CAAC,IAAI,CAAC,cAAc,CAAC,EAAE,CAC3C,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,cAAc,CAAC,IAAI,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,cAAc,CAAC,CAC9E,CAAC;IACJ,CAAC;IAEO,MAAM,CAAC,kBAAkB,CAAC,KAAa;QAC7C,IAAI,CAAC,KAAK;YAAE,OAAO,aAAa,CAAC,MAAM,CAAC;QAExC,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,cAAc,CAAC,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,WAAW,CAAC,EAAE,CAAC;YAClF,OAAO,aAAa,CAAC,IAAI,CAAC;QAC5B,CAAC;QAED,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,cAAc,CAAC,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,UAAU,CAAC,EAAE,CAAC;YACjF,OAAO,aAAa,CAAC,QAAQ,CAAC;QAChC,CAAC;QAED,OAAO,aAAa,CAAC,MAAM,CAAC;IAC9B,CAAC;CACF;AAED;;GAEG;AACH,MAAM,OAAO,eAAe;IAC1B,MAAM,CAAC,WAAW,CAAC,KAAuB;QACxC,IAAI,KAAK,YAAY,QAAQ,EAAE,CAAC;YAC9B,OAAO,KAAK,CAAC,WAAW,CAAC;QAC3B,CAAC;QAED,4CAA4C;QAC5C,MAAM,iBAAiB,GAAG;YACxB,UAAU;YACV,aAAa;YACb,UAAU;YACV,YAAY;YACZ,UAAU;YACV,aAAa;YACb,sBAAsB;SACvB,CAAC;QAEF,OAAO,iBAAiB,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CACtC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CACxD,CAAC;IACJ,CAAC;IAED,MAAM,CAAC,WAAW,CAAC,KAAuB;QACxC,IAAI,KAAK,YAAY,QAAQ,EAAE,CAAC;YAC9B,OAAO,KAAK,CAAC,QAAQ,CAAC;QACxB,CAAC;QAED,2CAA2C;QAC3C,IAAI,KAAK,CAAC,IAAI,KAAK,iBAAiB,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,YAAY,CAAC,EAAE,CAAC;YAC7E,OAAO,aAAa,CAAC,GAAG,CAAC;QAC3B,CAAC;QAED,IAAI,KAAK,CAAC,IAAI,KAAK,WAAW,IAAI,KAAK,CAAC,IAAI,KAAK,gBAAgB,EAAE,CAAC;YAClE,OAAO,aAAa,CAAC,IAAI,CAAC;QAC5B,CAAC;QAED,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,WAAW,CAAC,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,cAAc,CAAC,EAAE,CAAC;YAClF,OAAO,aAAa,CAAC,IAAI,CAAC;QAC5B,CAAC;QAED,OAAO,aAAa,CAAC,MAAM,CAAC;IAC9B,CAAC;IAED,MAAM,CAAC,WAAW,CAAC,KAAuB;QACxC,MAAM,QAAQ,GAAG,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;QACzC,OAAO,QAAQ,KAAK,aAAa,CAAC,IAAI,IAAI,QAAQ,KAAK,aAAa,CAAC,QAAQ,CAAC;IAChF,CAAC;CACF"}