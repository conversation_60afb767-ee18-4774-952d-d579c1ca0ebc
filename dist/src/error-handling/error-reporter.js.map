{"version": 3, "file": "error-reporter.js", "sourceRoot": "", "sources": ["../../../src/error-handling/error-reporter.ts"], "names": [], "mappings": "AAAA;;GAEG;AAEH,OAAO,cAAc,MAAM,sBAAsB,CAAC;AAClD,OAAO,EAAE,QAAQ,EAAE,aAAa,EAAE,eAAe,EAAE,MAAM,kBAAkB,CAAC;AAC5E,OAAO,EAAE,SAAS,EAAE,MAAM,cAAc,CAAC;AAsBzC,MAAM,OAAO,aAAa;IAChB,OAAO,GAA6B,IAAI,GAAG,EAAE,CAAC;IAC9C,UAAU,GAA2B,IAAI,GAAG,EAAE,CAAC;IAC/C,UAAU,GAAsB,IAAI,GAAG,EAAE,CAAC;IAC1C,MAAM,GAAG,cAAc,CAAC,KAAK,CAAC,EAAE,SAAS,EAAE,eAAe,EAAE,CAAC,CAAC;IAEtE;QACE,IAAI,CAAC,sBAAsB,EAAE,CAAC;QAC9B,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,4BAA4B,CAAC,CAAC;IACjD,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,WAAW,CAAC,KAAuB,EAAE,UAA+B,EAAE;QAC1E,MAAM,QAAQ,GAAG,eAAe,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;QACpD,MAAM,WAAW,GAAG,IAAI,CAAC,mBAAmB,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;QAC7D,MAAM,SAAS,GAAG,IAAI,IAAI,EAAE,CAAC;QAE7B,IAAI,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;QAE3C,IAAI,MAAM,EAAE,CAAC;YACX,yBAAyB;YACzB,MAAM,CAAC,KAAK,EAAE,CAAC;YACf,MAAM,CAAC,QAAQ,GAAG,SAAS,CAAC;YAC5B,MAAM,CAAC,OAAO,GAAG,EAAE,GAAG,MAAM,CAAC,OAAO,EAAE,GAAG,OAAO,EAAE,CAAC;QACrD,CAAC;aAAM,CAAC;YACN,oBAAoB;YACpB,MAAM,GAAG;gBACP,EAAE,EAAE,IAAI,CAAC,UAAU,EAAE;gBACrB,SAAS;gBACT,KAAK;gBACL,OAAO;gBACP,QAAQ;gBACR,WAAW;gBACX,KAAK,EAAE,CAAC;gBACR,SAAS,EAAE,SAAS;gBACpB,QAAQ,EAAE,SAAS;aACpB,CAAC;YACF,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,WAAW,EAAE,MAAM,CAAC,CAAC;QACxC,CAAC;QAED,gBAAgB;QAChB,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,KAAK,EAAE;YAC1B,GAAG,OAAO;YACV,QAAQ,EAAE,MAAM,CAAC,EAAE;YACnB,WAAW;YACX,KAAK,EAAE,MAAM,CAAC,KAAK;SACpB,CAAC,CAAC;QAEH,oBAAoB;QACpB,MAAM,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC;QAEnC,oCAAoC;QACpC,IAAI,IAAI,CAAC,OAAO,CAAC,IAAI,GAAG,GAAG,KAAK,CAAC,EAAE,CAAC;YAClC,IAAI,CAAC,iBAAiB,EAAE,CAAC;QAC3B,CAAC;IACH,CAAC;IAED;;OAEG;IACH,iBAAiB,CAAC,IAAe;QAC/B,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;QACrC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,uBAAuB,EAAE;YACxC,IAAI,EAAE,IAAI,CAAC,IAAI;YACf,QAAQ,EAAE,IAAI,CAAC,QAAQ;YACvB,OAAO,EAAE,IAAI,CAAC,OAAO;SACtB,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,UAAU,CAAC,KAAK,GAAG,GAAG;QACpB,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC;aACrC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,QAAQ,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC,QAAQ,CAAC,OAAO,EAAE,CAAC;aAC3D,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;IACrB,CAAC;IAED;;OAEG;IACH,aAAa;QAMX,MAAM,OAAO,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC,CAAC;QAClD,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QACvB,MAAM,UAAU,GAAG,GAAG,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;QAExC,OAAO;YACL,YAAY,EAAE,OAAO,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,MAAM,EAAE,EAAE,CAAC,GAAG,GAAG,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC;YACpE,YAAY,EAAE,OAAO,CAAC,MAAM;YAC5B,cAAc,EAAE,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,QAAQ,KAAK,aAAa,CAAC,QAAQ,CAAC,CAAC,MAAM;YACjF,YAAY,EAAE,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,QAAQ,CAAC,OAAO,EAAE,GAAG,UAAU,CAAC,CAAC,MAAM;SAC5E,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,mBAAmB,CAAC,KAAuB,EAAE,OAA4B;QAC/E,MAAM,SAAS,GAAG,KAAK,CAAC,WAAW,CAAC,IAAI,CAAC;QACzC,MAAM,YAAY,GAAG,KAAK,CAAC,OAAO,CAAC;QACnC,MAAM,SAAS,GAAG,KAAK,YAAY,QAAQ,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,SAAS,CAAC;QACrE,MAAM,SAAS,GAAG,OAAO,CAAC,WAAW,CAAC,IAAI,SAAS,CAAC;QAEpD,iCAAiC;QACjC,MAAM,KAAK,GAAG,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,YAAY,CAAC,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;QAChF,OAAO,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,gBAAgB,EAAE,GAAG,CAAC,CAAC;IACxD,CAAC;IAED;;OAEG;IACK,UAAU;QAChB,OAAO,OAAO,IAAI,CAAC,GAAG,EAAE,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC;IAC3E,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,eAAe,CAAC,MAAmB;QAC/C,KAAK,MAAM,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC;YAC3C,IAAI,CAAC,IAAI,CAAC,OAAO;gBAAE,SAAS;YAE5B,iBAAiB;YACjB,MAAM,SAAS,GAAG,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;YAC5C,IAAI,SAAS,IAAI,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC,OAAO,EAAE,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;gBAClE,SAAS;YACX,CAAC;YAED,kBAAkB;YAClB,IAAI,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,EAAE,CAAC;gBAC3B,IAAI,CAAC;oBACH,MAAM,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;oBAC1B,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,EAAE,IAAI,IAAI,EAAE,CAAC,CAAC;oBAEtC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,sBAAsB,EAAE;wBACvC,QAAQ,EAAE,IAAI;wBACd,QAAQ,EAAE,MAAM,CAAC,EAAE;wBACnB,gBAAgB,EAAE,MAAM,CAAC,WAAW;qBACrC,CAAC,CAAC;gBACL,CAAC;gBAAC,OAAO,KAAK,EAAE,CAAC;oBACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,0BAA0B,EAAE;wBAC5C,QAAQ,EAAE,IAAI;wBACd,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC;qBAC9D,CAAC,CAAC;gBACL,CAAC;YACH,CAAC;QACH,CAAC;IACH,CAAC;IAED;;OAEG;IACK,sBAAsB;QAC5B,uBAAuB;QACvB,IAAI,CAAC,iBAAiB,CAAC;YACrB,IAAI,EAAE,iBAAiB;YACvB,SAAS,EAAE,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,QAAQ,KAAK,aAAa,CAAC,QAAQ;YACjE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,EAAE;gBACvB,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,sBAAsB,EAAE;oBACxC,QAAQ,EAAE,MAAM,CAAC,EAAE;oBACnB,KAAK,EAAE,MAAM,CAAC,KAAK,CAAC,OAAO;oBAC3B,KAAK,EAAE,MAAM,CAAC,KAAK;oBACnB,OAAO,EAAE,MAAM,CAAC,OAAO;iBACxB,CAAC,CAAC;YACL,CAAC;YACD,QAAQ,EAAE,CAAC,GAAG,EAAE,GAAG,IAAI,EAAE,YAAY;YACrC,OAAO,EAAE,IAAI;SACd,CAAC,CAAC;QAEH,6BAA6B;QAC7B,IAAI,CAAC,iBAAiB,CAAC;YACrB,IAAI,EAAE,uBAAuB;YAC7B,SAAS,EAAE,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,KAAK,IAAI,EAAE,IAAI,MAAM,CAAC,KAAK,GAAG,EAAE,KAAK,CAAC;YACpE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,EAAE;gBACvB,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,4BAA4B,EAAE;oBAC7C,QAAQ,EAAE,MAAM,CAAC,EAAE;oBACnB,KAAK,EAAE,MAAM,CAAC,KAAK,CAAC,OAAO;oBAC3B,KAAK,EAAE,MAAM,CAAC,KAAK;oBACnB,SAAS,EAAE,MAAM,CAAC,KAAK,GAAG,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,MAAM,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC,GAAG,IAAI,GAAG,EAAE,CAAC,EAAE,aAAa;iBACjG,CAAC,CAAC;YACL,CAAC;YACD,QAAQ,EAAE,EAAE,GAAG,EAAE,GAAG,IAAI,EAAE,aAAa;YACvC,OAAO,EAAE,IAAI;SACd,CAAC,CAAC;QAEH,8BAA8B;QAC9B,IAAI,CAAC,iBAAiB,CAAC;YACrB,IAAI,EAAE,wBAAwB;YAC9B,SAAS,EAAE,CAAC,MAAM,EAAE,EAAE;gBACpB,OAAO,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,IAAI,CAAC;oBACjD,CAAC,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,YAAY,CAAC;wBACzD,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC,CAAC;YAClE,CAAC;YACD,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,EAAE;gBACvB,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,6BAA6B,EAAE;oBAC/C,QAAQ,EAAE,MAAM,CAAC,EAAE;oBACnB,KAAK,EAAE,MAAM,CAAC,KAAK,CAAC,OAAO;oBAC3B,KAAK,EAAE,MAAM,CAAC,KAAK;oBACnB,SAAS,EAAE,SAAS,EAAE,CAAC,GAAG,CAAC,MAAM;oBACjC,UAAU,EAAE,SAAS,EAAE,CAAC,GAAG,CAAC,UAAU;iBACvC,CAAC,CAAC;YACL,CAAC;YACD,QAAQ,EAAE,EAAE,GAAG,EAAE,GAAG,IAAI,EAAE,aAAa;YACvC,OAAO,EAAE,IAAI;SACd,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACK,iBAAiB;QACvB,MAAM,MAAM,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC,WAAW;QAC/C,MAAM,MAAM,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,MAAM,CAAC;QAEnC,IAAI,OAAO,GAAG,CAAC,CAAC;QAChB,KAAK,MAAM,CAAC,WAAW,EAAE,MAAM,CAAC,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;YACjD,IAAI,MAAM,CAAC,QAAQ,CAAC,OAAO,EAAE,GAAG,MAAM,EAAE,CAAC;gBACvC,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC;gBACjC,OAAO,EAAE,CAAC;YACZ,CAAC;QACH,CAAC;QAED,IAAI,OAAO,GAAG,CAAC,EAAE,CAAC;YAChB,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,8BAA8B,EAAE,EAAE,OAAO,EAAE,CAAC,CAAC;QAChE,CAAC;IACH,CAAC;IAED;;OAEG;IACH,aAAa;QACX,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;YACtD,EAAE,EAAE,MAAM,CAAC,EAAE;YACb,SAAS,EAAE,MAAM,CAAC,SAAS,CAAC,WAAW,EAAE;YACzC,KAAK,EAAE;gBACL,IAAI,EAAE,MAAM,CAAC,KAAK,CAAC,IAAI;gBACvB,OAAO,EAAE,MAAM,CAAC,KAAK,CAAC,OAAO;gBAC7B,IAAI,EAAE,MAAM,CAAC,KAAK,YAAY,QAAQ,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,SAAS;gBACtE,QAAQ,EAAE,MAAM,CAAC,QAAQ;aAC1B;YACD,OAAO,EAAE,MAAM,CAAC,OAAO;YACvB,WAAW,EAAE,MAAM,CAAC,WAAW;YAC/B,KAAK,EAAE,MAAM,CAAC,KAAK;YACnB,SAAS,EAAE,MAAM,CAAC,SAAS,CAAC,WAAW,EAAE;YACzC,QAAQ,EAAE,MAAM,CAAC,QAAQ,CAAC,WAAW,EAAE;SACxC,CAAC,CAAC,CAAC;IACN,CAAC;CACF;AAED;;GAEG;AACH,MAAM,CAAC,MAAM,aAAa,GAAG,IAAI,aAAa,EAAE,CAAC"}