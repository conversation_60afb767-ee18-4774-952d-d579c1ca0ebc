/**
 * Health monitoring and alerting system
 */
export interface HealthCheck {
    name: string;
    check: () => Promise<HealthCheckResult>;
    interval: number;
    timeout: number;
    critical: boolean;
}
export interface HealthCheckResult {
    status: 'healthy' | 'unhealthy' | 'degraded';
    message?: string;
    details?: Record<string, any>;
    timestamp: Date;
    duration: number;
}
export interface SystemHealth {
    overall: 'healthy' | 'unhealthy' | 'degraded';
    checks: Record<string, HealthCheckResult>;
    timestamp: Date;
    uptime: number;
}
export declare class HealthMonitor {
    private checks;
    private results;
    private intervals;
    private startTime;
    private logger;
    constructor();
    /**
     * Register a health check
     */
    registerCheck(check: HealthCheck): void;
    /**
     * Unregister a health check
     */
    unregisterCheck(name: string): void;
    /**
     * Run a specific health check
     */
    runCheck(name: string): Promise<HealthCheckResult>;
    /**
     * Run all health checks
     */
    runAllChecks(): Promise<Record<string, HealthCheckResult>>;
    /**
     * Get current system health
     */
    getSystemHealth(): Promise<SystemHealth>;
    /**
     * Get health status without running checks
     */
    getCurrentHealth(): SystemHealth;
    /**
     * Start periodic checking for a health check
     */
    private startPeriodicCheck;
    /**
     * Create a timeout promise
     */
    private createTimeoutPromise;
    /**
     * Calculate overall system health
     */
    private calculateOverallHealth;
    /**
     * Stop all health checks
     */
    stop(): void;
}
/**
 * Default health checks for the MCP server
 */
export declare function createDefaultHealthChecks(s3Client?: any): HealthCheck[];
/**
 * Global health monitor instance
 */
export declare const healthMonitor: HealthMonitor;
//# sourceMappingURL=health-monitor.d.ts.map