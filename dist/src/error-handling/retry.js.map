{"version": 3, "file": "retry.js", "sourceRoot": "", "sources": ["../../../src/error-handling/retry.ts"], "names": [], "mappings": "AAAA;;GAEG;AAEH,OAAO,MAAM,MAAM,cAAc,CAAC;AAClC,OAAO,EAAE,QAAQ,EAAE,eAAe,EAAE,MAAM,kBAAkB,CAAC;AAoB7D,MAAM,OAAO,YAAY;IACf,MAAM,CAAC,cAAc,GAAiB;QAC5C,WAAW,EAAE,CAAC;QACd,SAAS,EAAE,IAAI;QACf,QAAQ,EAAE,KAAK;QACf,iBAAiB,EAAE,CAAC;QACpB,MAAM,EAAE,IAAI;QACZ,cAAc,EAAE,CAAC,KAAY,EAAE,EAAE,CAAC,eAAe,CAAC,WAAW,CAAC,KAAK,CAAC;KACrE,CAAC;IAEF;;OAEG;IACH,MAAM,CAAC,KAAK,CAAC,OAAO,CAClB,SAA2B,EAC3B,UAAiC,EAAE,EACnC,OAAwD;QAExD,MAAM,MAAM,GAAG,EAAE,GAAG,IAAI,CAAC,cAAc,EAAE,GAAG,OAAO,EAAE,CAAC;QACtD,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAC7B,IAAI,SAA4B,CAAC;QAEjC,MAAM,CAAC,KAAK,CAAC,0BAA0B,EAAE;YACvC,aAAa,EAAE,OAAO,EAAE,aAAa;YACrC,SAAS,EAAE,OAAO,EAAE,SAAS;YAC7B,WAAW,EAAE,MAAM,CAAC,WAAW;YAC/B,SAAS,EAAE,MAAM,CAAC,SAAS;SAC5B,CAAC,CAAC;QAEH,KAAK,IAAI,OAAO,GAAG,CAAC,EAAE,OAAO,IAAI,MAAM,CAAC,WAAW,EAAE,OAAO,EAAE,EAAE,CAAC;YAC/D,IAAI,CAAC;gBACH,MAAM,MAAM,GAAG,MAAM,SAAS,EAAE,CAAC;gBACjC,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;gBAEzC,MAAM,CAAC,KAAK,CAAC,2BAA2B,EAAE;oBACxC,aAAa,EAAE,OAAO,EAAE,aAAa;oBACrC,SAAS,EAAE,OAAO,EAAE,SAAS;oBAC7B,OAAO;oBACP,SAAS;iBACV,CAAC,CAAC;gBAEH,OAAO;oBACL,OAAO,EAAE,IAAI;oBACb,MAAM;oBACN,QAAQ,EAAE,OAAO;oBACjB,SAAS;iBACV,CAAC;YACJ,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,SAAS,GAAG,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC;gBAEtE,MAAM,CAAC,IAAI,CAAC,wBAAwB,EAAE;oBACpC,aAAa,EAAE,OAAO,EAAE,aAAa;oBACrC,SAAS,EAAE,OAAO,EAAE,SAAS;oBAC7B,OAAO;oBACP,KAAK,EAAE,SAAS,CAAC,OAAO;oBACxB,WAAW,EAAE,MAAM,CAAC,cAAe,CAAC,SAAS,CAAC;iBAC/C,CAAC,CAAC;gBAEH,2BAA2B;gBAC3B,IAAI,OAAO,KAAK,MAAM,CAAC,WAAW,IAAI,CAAC,MAAM,CAAC,cAAe,CAAC,SAAS,CAAC,EAAE,CAAC;oBACzE,MAAM;gBACR,CAAC;gBAED,sDAAsD;gBACtD,MAAM,KAAK,GAAG,IAAI,CAAC,cAAc,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;gBAEnD,MAAM,CAAC,KAAK,CAAC,gCAAgC,EAAE;oBAC7C,aAAa,EAAE,OAAO,EAAE,aAAa;oBACrC,SAAS,EAAE,OAAO,EAAE,SAAS;oBAC7B,OAAO;oBACP,KAAK;oBACL,WAAW,EAAE,OAAO,GAAG,CAAC;iBACzB,CAAC,CAAC;gBAEH,kCAAkC;gBAClC,IAAI,MAAM,CAAC,OAAO,EAAE,CAAC;oBACnB,MAAM,CAAC,OAAO,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;gBACrC,CAAC;gBAED,uBAAuB;gBACvB,MAAM,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;YAC1B,CAAC;QACH,CAAC;QAED,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;QAEzC,MAAM,CAAC,KAAK,CAAC,2CAA2C,EAAE;YACxD,aAAa,EAAE,OAAO,EAAE,aAAa;YACrC,SAAS,EAAE,OAAO,EAAE,SAAS;YAC7B,WAAW,EAAE,MAAM,CAAC,WAAW;YAC/B,SAAS;YACT,UAAU,EAAE,SAAS,EAAE,OAAO;SAC/B,CAAC,CAAC;QAEH,OAAO;YACL,OAAO,EAAE,KAAK;YACd,KAAK,EAAE,SAAS;YAChB,QAAQ,EAAE,MAAM,CAAC,WAAW;YAC5B,SAAS;SACV,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,cAAc,CAAC,OAAe,EAAE,OAAqB;QAClE,IAAI,KAAK,GAAG,OAAO,CAAC,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,iBAAiB,EAAE,OAAO,GAAG,CAAC,CAAC,CAAC;QAEjF,4BAA4B;QAC5B,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,OAAO,CAAC,QAAQ,CAAC,CAAC;QAE1C,wCAAwC;QACxC,IAAI,OAAO,CAAC,MAAM,EAAE,CAAC;YACnB,KAAK,GAAG,KAAK,GAAG,CAAC,GAAG,GAAG,IAAI,CAAC,MAAM,EAAE,GAAG,GAAG,CAAC,CAAC;QAC9C,CAAC;QAED,OAAO,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,KAAK,CAAC,EAAU;QAC7B,OAAO,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC,CAAC;IACzD,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,IAAI,CACT,EAA8B,EAC9B,UAAiC,EAAE,EACnC,aAAsB;QAEtB,OAAO,KAAK,EAAE,GAAG,IAAO,EAAc,EAAE;YACtC,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,OAAO,CAC/B,GAAG,EAAE,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,EACjB,OAAO,EACP,aAAa,CAAC,CAAC,CAAC,EAAE,aAAa,EAAE,CAAC,CAAC,CAAC,EAAE,CACvC,CAAC;YAEF,IAAI,MAAM,CAAC,OAAO,EAAE,CAAC;gBACnB,OAAO,MAAM,CAAC,MAAO,CAAC;YACxB,CAAC;iBAAM,CAAC;gBACN,MAAM,MAAM,CAAC,KAAK,IAAI,IAAI,KAAK,CAAC,gCAAgC,CAAC,CAAC;YACpE,CAAC;QACH,CAAC,CAAC;IACJ,CAAC;;AAGH;;GAEG;AACH,MAAM,UAAU,KAAK,CAAC,UAAiC,EAAE;IACvD,OAAO,UAAU,MAAW,EAAE,WAAmB,EAAE,UAA8B;QAC/E,MAAM,cAAc,GAAG,UAAU,CAAC,KAAK,CAAC;QACxC,MAAM,aAAa,GAAG,GAAG,MAAM,CAAC,WAAW,CAAC,IAAI,IAAI,WAAW,EAAE,CAAC;QAElE,UAAU,CAAC,KAAK,GAAG,KAAK,WAAW,GAAG,IAAW;YAC/C,MAAM,MAAM,GAAG,MAAM,YAAY,CAAC,OAAO,CACvC,GAAG,EAAE,CAAC,cAAc,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI,CAAC,EACtC,OAAO,EACP,EAAE,aAAa,EAAE,CAClB,CAAC;YAEF,IAAI,MAAM,CAAC,OAAO,EAAE,CAAC;gBACnB,OAAO,MAAM,CAAC,MAAM,CAAC;YACvB,CAAC;iBAAM,CAAC;gBACN,MAAM,MAAM,CAAC,KAAK,IAAI,IAAI,KAAK,CAAC,GAAG,aAAa,uBAAuB,CAAC,CAAC;YAC3E,CAAC;QACH,CAAC,CAAC;QAEF,OAAO,UAAU,CAAC;IACpB,CAAC,CAAC;AACJ,CAAC;AAED;;GAEG;AACH,MAAM,OAAO,cAAc;IAMN;IACA;IACA;IAPX,QAAQ,GAAG,CAAC,CAAC;IACb,eAAe,GAAG,CAAC,CAAC;IACpB,KAAK,GAAoC,QAAQ,CAAC;IAE1D,YACmB,mBAA2B,CAAC,EAC5B,kBAA0B,KAAK,EAC/B,OAAe,gBAAgB;QAF/B,qBAAgB,GAAhB,gBAAgB,CAAY;QAC5B,oBAAe,GAAf,eAAe,CAAgB;QAC/B,SAAI,GAAJ,IAAI,CAA2B;IAC/C,CAAC;IAEJ,KAAK,CAAC,OAAO,CAAI,SAA2B;QAC1C,IAAI,IAAI,CAAC,KAAK,KAAK,MAAM,EAAE,CAAC;YAC1B,IAAI,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,eAAe,EAAE,CAAC;gBAC7D,IAAI,CAAC,KAAK,GAAG,WAAW,CAAC;gBACzB,MAAM,CAAC,IAAI,CAAC,4CAA4C,EAAE,EAAE,IAAI,EAAE,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC;YACjF,CAAC;iBAAM,CAAC;gBACN,MAAM,IAAI,QAAQ,CAChB,sBAA6B,EAC7B,mBAAmB,IAAI,CAAC,IAAI,UAAU,EACtC,MAAa,EACb,EAAE,QAAQ,EAAE,EAAE,kBAAkB,EAAE,IAAI,CAAC,IAAI,EAAE,EAAE,CAChD,CAAC;YACJ,CAAC;QACH,CAAC;QAED,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,MAAM,SAAS,EAAE,CAAC;YAEjC,IAAI,IAAI,CAAC,KAAK,KAAK,WAAW,EAAE,CAAC;gBAC/B,IAAI,CAAC,KAAK,EAAE,CAAC;YACf,CAAC;YAED,OAAO,MAAM,CAAC;QAChB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,aAAa,EAAE,CAAC;YACrB,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAEO,aAAa;QACnB,IAAI,CAAC,QAAQ,EAAE,CAAC;QAChB,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAElC,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,gBAAgB,EAAE,CAAC;YAC3C,IAAI,CAAC,KAAK,GAAG,MAAM,CAAC;YACpB,MAAM,CAAC,IAAI,CAAC,wCAAwC,EAAE;gBACpD,IAAI,EAAE,IAAI,CAAC,IAAI;gBACf,QAAQ,EAAE,IAAI,CAAC,QAAQ;gBACvB,SAAS,EAAE,IAAI,CAAC,gBAAgB;aACjC,CAAC,CAAC;QACL,CAAC;IACH,CAAC;IAEO,KAAK;QACX,IAAI,CAAC,QAAQ,GAAG,CAAC,CAAC;QAClB,IAAI,CAAC,KAAK,GAAG,QAAQ,CAAC;QACtB,MAAM,CAAC,IAAI,CAAC,iCAAiC,EAAE,EAAE,IAAI,EAAE,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC;IACtE,CAAC;IAED,QAAQ;QACN,OAAO;YACL,KAAK,EAAE,IAAI,CAAC,KAAK;YACjB,QAAQ,EAAE,IAAI,CAAC,QAAQ;YACvB,eAAe,EAAE,IAAI,CAAC,eAAe;SACtC,CAAC;IACJ,CAAC;CACF"}