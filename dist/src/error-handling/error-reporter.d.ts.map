{"version": 3, "file": "error-reporter.d.ts", "sourceRoot": "", "sources": ["../../../src/error-handling/error-reporter.ts"], "names": [], "mappings": "AAAA;;GAEG;AAGH,OAAO,EAAE,QAAQ,EAAE,aAAa,EAAmB,MAAM,kBAAkB,CAAC;AAG5E,MAAM,WAAW,WAAW;IAC1B,EAAE,EAAE,MAAM,CAAC;IACX,SAAS,EAAE,IAAI,CAAC;IAChB,KAAK,EAAE,QAAQ,GAAG,KAAK,CAAC;IACxB,OAAO,EAAE,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;IAC7B,QAAQ,EAAE,aAAa,CAAC;IACxB,WAAW,EAAE,MAAM,CAAC;IACpB,KAAK,EAAE,MAAM,CAAC;IACd,SAAS,EAAE,IAAI,CAAC;IAChB,QAAQ,EAAE,IAAI,CAAC;CAChB;AAED,MAAM,WAAW,SAAS;IACxB,IAAI,EAAE,MAAM,CAAC;IACb,SAAS,EAAE,CAAC,MAAM,EAAE,WAAW,KAAK,OAAO,CAAC;IAC5C,MAAM,EAAE,CAAC,MAAM,EAAE,WAAW,KAAK,OAAO,CAAC,IAAI,CAAC,CAAC;IAC/C,QAAQ,EAAE,MAAM,CAAC;IACjB,OAAO,EAAE,OAAO,CAAC;CAClB;AAED,qBAAa,aAAa;IACxB,OAAO,CAAC,OAAO,CAAuC;IACtD,OAAO,CAAC,UAAU,CAAqC;IACvD,OAAO,CAAC,UAAU,CAAgC;IAClD,OAAO,CAAC,MAAM,CAAwD;;IAOtE;;OAEG;IACG,WAAW,CAAC,KAAK,EAAE,KAAK,GAAG,QAAQ,EAAE,OAAO,GAAE,MAAM,CAAC,MAAM,EAAE,GAAG,CAAM,GAAG,OAAO,CAAC,IAAI,CAAC;IA6C5F;;OAEG;IACH,iBAAiB,CAAC,IAAI,EAAE,SAAS,GAAG,IAAI;IASxC;;OAEG;IACH,UAAU,CAAC,KAAK,SAAM,GAAG,WAAW,EAAE;IAMtC;;OAEG;IACH,aAAa,IAAI;QACf,YAAY,EAAE,MAAM,CAAC;QACrB,YAAY,EAAE,MAAM,CAAC;QACrB,cAAc,EAAE,MAAM,CAAC;QACvB,YAAY,EAAE,MAAM,CAAC;KACtB;IAaD;;OAEG;IACH,OAAO,CAAC,mBAAmB;IAW3B;;OAEG;IACH,OAAO,CAAC,UAAU;IAIlB;;OAEG;YACW,eAAe;IA+B7B;;OAEG;IACH,OAAO,CAAC,sBAAsB;IAuD9B;;OAEG;IACH,OAAO,CAAC,iBAAiB;IAiBzB;;OAEG;IACH,aAAa,IAAI,GAAG,EAAE;CAiBvB;AAED;;GAEG;AACH,eAAO,MAAM,aAAa,eAAsB,CAAC"}