/**
 * Environment-specific configuration management
 */
/**
 * Environment-specific configuration overrides
 */
export const environmentConfigs = {
    development: {
        name: 'development',
        overrides: {
            logging: {
                level: 'debug',
                filePath: './logs/dev-mcp-server.log',
                enableConsoleLogging: true,
            },
            storage: {
                retentionDays: 7,
                maxFileSizeMB: 50,
                enableCompression: false,
                basePath: 'dev-mcp-responses',
                enableDateFolders: true,
                enableNamespaceFolders: true,
            },
            kubernetes: {
                defaultNamespacePrefix: 'DEV_NAMESPACE',
                enableFormatValidation: true,
            },
        },
        requiredVariables: [
            'AWS_ACCESS_KEY_ID',
            'AWS_SECRET_ACCESS_KEY',
            'AWS_REGION',
            'S3_BUCKET_NAME',
        ],
        warnings: [
            'Development mode: Enhanced logging and debugging enabled',
            'Development mode: Reduced file retention period',
            'Development mode: Accepts any valid Kubernetes namespace',
        ],
    },
    staging: {
        name: 'staging',
        overrides: {
            logging: {
                level: 'info',
                filePath: './logs/staging-mcp-server.log',
                enableConsoleLogging: true,
            },
            storage: {
                retentionDays: 30,
                maxFileSizeMB: 100,
                enableCompression: true,
                basePath: 'staging-mcp-responses',
                enableDateFolders: true,
                enableNamespaceFolders: true,
            },
            kubernetes: {
                defaultNamespacePrefix: 'STAGING_NAMESPACE',
                enableFormatValidation: true,
            },
        },
        requiredVariables: [
            'AWS_ACCESS_KEY_ID',
            'AWS_SECRET_ACCESS_KEY',
            'AWS_REGION',
            'S3_BUCKET_NAME',
        ],
        warnings: [
            'Staging mode: Format validation enabled for namespace checking',
            'Staging mode: Compression enabled for storage efficiency',
            'Staging mode: Accepts any valid Kubernetes namespace',
        ],
    },
    production: {
        name: 'production',
        overrides: {
            logging: {
                level: 'warn',
                filePath: './logs/prod-mcp-server.log',
                enableConsoleLogging: false,
            },
            storage: {
                retentionDays: 90,
                maxFileSizeMB: 500,
                enableCompression: true,
                basePath: 'mcp-responses',
                enableDateFolders: true,
                enableNamespaceFolders: true,
            },
            kubernetes: {
                defaultNamespacePrefix: 'NO_NAMESPACE',
                enableFormatValidation: true,
            },
        },
        requiredVariables: [
            'AWS_ACCESS_KEY_ID',
            'AWS_SECRET_ACCESS_KEY',
            'AWS_REGION',
            'S3_BUCKET_NAME',
            'MCP_SERVER_NAME',
        ],
        warnings: [
            'Production mode: Console logging disabled for performance',
            'Production mode: Format validation enabled for namespace checking',
            'Production mode: Extended file retention period',
            'Production mode: Accepts any valid Kubernetes namespace',
        ],
    },
    test: {
        name: 'test',
        overrides: {
            logging: {
                level: 'error',
                filePath: './logs/test-mcp-server.log',
                enableConsoleLogging: false,
            },
            storage: {
                retentionDays: 1,
                maxFileSizeMB: 10,
                enableCompression: false,
                basePath: 'test-mcp-responses',
                enableDateFolders: false,
                enableNamespaceFolders: false,
            },
            kubernetes: {
                defaultNamespacePrefix: 'TEST_NAMESPACE',
                enableFormatValidation: false,
            },
        },
        requiredVariables: [],
        warnings: [
            'Test mode: Minimal logging for test performance',
            'Test mode: Short retention period for cleanup',
            'Test mode: Simplified folder structure',
        ],
    },
};
/**
 * Gets the current environment from NODE_ENV or defaults to development
 */
export function getCurrentEnvironment() {
    const env = process.env['NODE_ENV']?.toLowerCase();
    if (env && env in environmentConfigs) {
        return env;
    }
    return 'development';
}
/**
 * Gets environment-specific configuration
 */
export function getEnvironmentConfig(env) {
    const environment = env || getCurrentEnvironment();
    return environmentConfigs[environment];
}
/**
 * Merges base configuration with environment-specific overrides
 */
export function mergeEnvironmentConfig(baseConfig, env) {
    const envConfig = getEnvironmentConfig(env);
    return deepMerge(baseConfig, envConfig.overrides);
}
/**
 * Deep merge utility for configuration objects
 */
function deepMerge(target, source) {
    const result = { ...target };
    for (const key in source) {
        if (source[key] && typeof source[key] === 'object' && !Array.isArray(source[key])) {
            result[key] = deepMerge(target[key] || {}, source[key]);
        }
        else {
            result[key] = source[key];
        }
    }
    return result;
}
/**
 * Validates environment-specific requirements
 */
export function validateEnvironmentRequirements(env) {
    const envConfig = getEnvironmentConfig(env);
    const missing = [];
    for (const variable of envConfig.requiredVariables) {
        if (!process.env[variable]) {
            missing.push(variable);
        }
    }
    return {
        isValid: missing.length === 0,
        missing,
        warnings: envConfig.warnings || [],
    };
}
/**
 * Gets environment-specific S3 bucket naming
 */
export function getEnvironmentBucketName(baseBucketName, env) {
    const environment = env || getCurrentEnvironment();
    if (environment === 'production') {
        return baseBucketName;
    }
    return `${baseBucketName}-${environment}`;
}
/**
 * Gets environment-specific logging configuration
 */
export function getEnvironmentLoggingConfig(env) {
    const envConfig = getEnvironmentConfig(env);
    return envConfig.overrides.logging || {};
}
/**
 * Checks if current environment is production
 */
export function isProduction() {
    return getCurrentEnvironment() === 'production';
}
/**
 * Checks if current environment is development
 */
export function isDevelopment() {
    return getCurrentEnvironment() === 'development';
}
/**
 * Checks if current environment is test
 */
export function isTest() {
    return getCurrentEnvironment() === 'test';
}
//# sourceMappingURL=environments.js.map