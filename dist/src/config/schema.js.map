{"version": 3, "file": "schema.js", "sourceRoot": "", "sources": ["../../../src/config/schema.ts"], "names": [], "mappings": "AAAA;;GAEG;AAgBH;;GAEG;AACH,MAAM,CAAC,MAAM,YAAY,GAAiB;IACxC,GAAG,EAAE;QACH,WAAW,EAAE;YACX,QAAQ,EAAE,IAAI;YACd,IAAI,EAAE,QAAQ;YACd,GAAG,EAAE,EAAE;YACP,GAAG,EAAE,GAAG;YACR,OAAO,EAAE,aAAa;SACvB;QACD,eAAe,EAAE;YACf,QAAQ,EAAE,IAAI;YACd,IAAI,EAAE,QAAQ;YACd,GAAG,EAAE,EAAE;YACP,GAAG,EAAE,GAAG;SACT;QACD,MAAM,EAAE;YACN,QAAQ,EAAE,IAAI;YACd,IAAI,EAAE,QAAQ;YACd,OAAO,EAAE,eAAe;YACxB,IAAI,EAAE;gBACJ,WAAW,EAAE,WAAW,EAAE,WAAW,EAAE,WAAW;gBAClD,WAAW,EAAE,WAAW,EAAE,WAAW,EAAE,cAAc;gBACrD,gBAAgB,EAAE,gBAAgB,EAAE,gBAAgB;gBACpD,gBAAgB,EAAE,YAAY,EAAE,cAAc;gBAC9C,WAAW,EAAE,YAAY,EAAE,YAAY;aACxC;SACF;QACD,UAAU,EAAE;YACV,QAAQ,EAAE,IAAI;YACd,IAAI,EAAE,QAAQ;YACd,GAAG,EAAE,CAAC;YACN,GAAG,EAAE,EAAE;YACP,OAAO,EAAE,iBAAiB;YAC1B,SAAS,EAAE,CAAC,KAAa,EAAE,EAAE;gBAC3B,yBAAyB;gBACzB,IAAI,KAAK,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,KAAK,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC;oBACjD,OAAO,8CAA8C,CAAC;gBACxD,CAAC;gBACD,IAAI,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC;oBACzB,OAAO,gDAAgD,CAAC;gBAC1D,CAAC;gBACD,IAAI,sBAAsB,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC;oBACvC,OAAO,kDAAkD,CAAC;gBAC5D,CAAC;gBACD,OAAO,IAAI,CAAC;YACd,CAAC;SACF;KACF;IACD,GAAG,EAAE;QACH,IAAI,EAAE;YACJ,QAAQ,EAAE,KAAK;YACf,IAAI,EAAE,QAAQ;YACd,GAAG,EAAE,IAAI;YACT,GAAG,EAAE,KAAK;SACX;QACD,UAAU,EAAE;YACV,QAAQ,EAAE,KAAK;YACf,IAAI,EAAE,QAAQ;YACd,GAAG,EAAE,CAAC;YACN,GAAG,EAAE,GAAG;YACR,OAAO,EAAE,mBAAmB;SAC7B;KACF;IACD,UAAU,EAAE;QACV,sBAAsB,EAAE;YACtB,QAAQ,EAAE,KAAK;YACf,IAAI,EAAE,QAAQ;YACd,GAAG,EAAE,CAAC;YACN,GAAG,EAAE,EAAE;YACP,OAAO,EAAE,mBAAmB;SAC7B;QACD,sBAAsB,EAAE;YACtB,QAAQ,EAAE,KAAK;YACf,IAAI,EAAE,SAAS;SAChB;KACF;IACD,OAAO,EAAE;QACP,aAAa,EAAE;YACb,QAAQ,EAAE,KAAK;YACf,IAAI,EAAE,QAAQ;YACd,GAAG,EAAE,CAAC;YACN,GAAG,EAAE,IAAI,EAAE,eAAe;SAC3B;QACD,aAAa,EAAE;YACb,QAAQ,EAAE,KAAK;YACf,IAAI,EAAE,QAAQ;YACd,GAAG,EAAE,CAAC;YACN,GAAG,EAAE,IAAI,EAAE,UAAU;SACtB;QACD,iBAAiB,EAAE;YACjB,QAAQ,EAAE,KAAK;YACf,IAAI,EAAE,SAAS;SAChB;QACD,QAAQ,EAAE;YACR,QAAQ,EAAE,KAAK;YACf,IAAI,EAAE,QAAQ;YACd,GAAG,EAAE,CAAC;YACN,GAAG,EAAE,IAAI;YACT,OAAO,EAAE,qBAAqB;SAC/B;QACD,iBAAiB,EAAE;YACjB,QAAQ,EAAE,KAAK;YACf,IAAI,EAAE,SAAS;SAChB;QACD,sBAAsB,EAAE;YACtB,QAAQ,EAAE,KAAK;YACf,IAAI,EAAE,SAAS;SAChB;KACF;IACD,OAAO,EAAE;QACP,KAAK,EAAE;YACL,QAAQ,EAAE,KAAK;YACf,IAAI,EAAE,QAAQ;YACd,IAAI,EAAE,CAAC,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,SAAS,CAAC;SACpD;QACD,QAAQ,EAAE;YACR,QAAQ,EAAE,KAAK;YACf,IAAI,EAAE,QAAQ;YACd,GAAG,EAAE,CAAC;YACN,GAAG,EAAE,IAAI;SACV;QACD,oBAAoB,EAAE;YACpB,QAAQ,EAAE,KAAK;YACf,IAAI,EAAE,SAAS;SAChB;KACF;CACF,CAAC;AAsBF,MAAM,CAAC,MAAM,yBAAyB,GAA8B;IAClE,QAAQ,EAAE;QACR,OAAO,EAAE;YACP,qCAAqC;YACrC,iDAAiD;YACjD,4CAA4C;SAC7C;QACD,QAAQ,EAAE;YACR,oCAAoC;YACpC,gDAAgD;YAChD,yBAAyB;SAC1B;QACD,OAAO,EAAE;YACP,+CAA+C;YAC/C,yBAAyB;YACzB,gCAAgC;YAChC,kCAAkC;SACnC;QACD,QAAQ,EAAE;YACR,8CAA8C;YAC9C,uBAAuB;SACxB;KACF;IACD,UAAU,EAAE;QACV,SAAS,EAAE,GAAG;QACd,OAAO,EAAE,EAAE;QACX,UAAU,EAAE,EAAE;QACd,UAAU,EAAE,EAAE;QACd,OAAO,EAAE,EAAE;QACX,OAAO,EAAE,EAAE;QACX,OAAO,EAAE,CAAC;KACX;IACD,oBAAoB,EAAE;QACpB,IAAI,EAAE,EAAE;QACR,MAAM,EAAE,EAAE;QACV,GAAG,EAAE,EAAE;KACR;CACF,CAAC"}