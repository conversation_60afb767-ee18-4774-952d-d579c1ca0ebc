/**
 * Environment-specific configuration management
 */
import { ServerConfig } from '../types.js';
export type Environment = 'development' | 'staging' | 'production' | 'test';
export interface EnvironmentConfig {
    name: Environment;
    overrides: Partial<ServerConfig>;
    requiredVariables: string[];
    warnings?: string[];
}
/**
 * Environment-specific configuration overrides
 */
export declare const environmentConfigs: Record<Environment, EnvironmentConfig>;
/**
 * Gets the current environment from NODE_ENV or defaults to development
 */
export declare function getCurrentEnvironment(): Environment;
/**
 * Gets environment-specific configuration
 */
export declare function getEnvironmentConfig(env?: Environment): EnvironmentConfig;
/**
 * Merges base configuration with environment-specific overrides
 */
export declare function mergeEnvironmentConfig(baseConfig: ServerConfig, env?: Environment): ServerConfig;
/**
 * Validates environment-specific requirements
 */
export declare function validateEnvironmentRequirements(env?: Environment): {
    isValid: boolean;
    missing: string[];
    warnings: string[];
};
/**
 * Gets environment-specific S3 bucket naming
 */
export declare function getEnvironmentBucketName(baseBucketName: string, env?: Environment): string;
/**
 * Gets environment-specific logging configuration
 */
export declare function getEnvironmentLoggingConfig(env?: Environment): {};
/**
 * Checks if current environment is production
 */
export declare function isProduction(): boolean;
/**
 * Checks if current environment is development
 */
export declare function isDevelopment(): boolean;
/**
 * Checks if current environment is test
 */
export declare function isTest(): boolean;
//# sourceMappingURL=environments.d.ts.map