/**
 * Secrets management and secure configuration handling
 */
export interface SecretSource {
    type: 'env' | 'file' | 'aws-secrets-manager' | 'kubernetes-secret';
    path?: string;
    key?: string;
    region?: string;
}
export interface SecretConfig {
    [key: string]: SecretSource;
}
/**
 * Default secret sources configuration
 */
export declare const defaultSecretSources: SecretConfig;
/**
 * Retrieves a secret value from the specified source
 */
export declare function getSecret(source: SecretSource): Promise<string | undefined>;
/**
 * Loads all secrets based on configuration
 */
export declare function loadSecrets(secretConfig?: SecretConfig): Promise<Record<string, string>>;
/**
 * Sets configuration values from loaded secrets
 */
export declare function applySecrets(config: any, secrets: Record<string, string>): any;
/**
 * Masks sensitive values in configuration for logging
 */
export declare function maskSensitiveConfig(config: any): any;
/**
 * Validates that all required secrets are available
 */
export declare function validateSecrets(secrets: Record<string, string>, required: string[]): {
    isValid: boolean;
    missing: string[];
};
/**
 * Gets secret source configuration for different environments
 */
export declare function getSecretSourcesForEnvironment(environment: string): SecretConfig;
//# sourceMappingURL=secrets.d.ts.map