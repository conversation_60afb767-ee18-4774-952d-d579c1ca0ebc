/**
 * Configuration schema and validation for the S3 MCP Server
 */
/**
 * Configuration schema definition
 */
export const configSchema = {
    aws: {
        accessKeyId: {
            required: true,
            type: 'string',
            min: 16,
            max: 128,
            pattern: /^[A-Z0-9]+$/,
        },
        secretAccessKey: {
            required: true,
            type: 'string',
            min: 40,
            max: 128,
        },
        region: {
            required: true,
            type: 'string',
            pattern: /^[a-z0-9\-]+$/,
            enum: [
                'us-east-1', 'us-east-2', 'us-west-1', 'us-west-2',
                'eu-west-1', 'eu-west-2', 'eu-west-3', 'eu-central-1',
                'ap-southeast-1', 'ap-southeast-2', 'ap-northeast-1',
                'ap-northeast-2', 'ap-south-1', 'ca-central-1',
                'sa-east-1', 'af-south-1', 'me-south-1'
            ],
        },
        bucketName: {
            required: true,
            type: 'string',
            min: 3,
            max: 63,
            pattern: /^[a-z0-9\-\.]+$/,
            validator: (value) => {
                // S3 bucket naming rules
                if (value.startsWith('-') || value.endsWith('-')) {
                    return 'Bucket name cannot start or end with hyphens';
                }
                if (value.includes('..')) {
                    return 'Bucket name cannot contain consecutive periods';
                }
                if (/^\d+\.\d+\.\d+\.\d+$/.test(value)) {
                    return 'Bucket name cannot be formatted as an IP address';
                }
                return true;
            },
        },
    },
    mcp: {
        port: {
            required: false,
            type: 'number',
            min: 1024,
            max: 65535,
        },
        serverName: {
            required: false,
            type: 'string',
            min: 1,
            max: 100,
            pattern: /^[a-zA-Z0-9\-_]+$/,
        },
    },
    kubernetes: {
        defaultNamespacePrefix: {
            required: false,
            type: 'string',
            min: 1,
            max: 63,
            pattern: /^[a-zA-Z0-9\-_]+$/,
        },
        validationEnabled: {
            required: false,
            type: 'boolean',
        },
        allowedNamespaces: {
            required: false,
            type: 'array',
            validator: (value) => {
                if (!Array.isArray(value))
                    return 'Must be an array';
                for (const ns of value) {
                    if (typeof ns !== 'string')
                        return 'All namespaces must be strings';
                    if (!/^[a-z0-9\-]+$/.test(ns)) {
                        return `Invalid namespace format: ${ns}`;
                    }
                    if (ns.length > 63) {
                        return `Namespace too long: ${ns}`;
                    }
                }
                return true;
            },
        },
    },
    storage: {
        retentionDays: {
            required: false,
            type: 'number',
            min: 1,
            max: 3650, // 10 years max
        },
        maxFileSizeMB: {
            required: false,
            type: 'number',
            min: 1,
            max: 5120, // 5GB max
        },
        enableCompression: {
            required: false,
            type: 'boolean',
        },
        basePath: {
            required: false,
            type: 'string',
            min: 1,
            max: 1024,
            pattern: /^[a-zA-Z0-9\-_\/]+$/,
        },
        enableDateFolders: {
            required: false,
            type: 'boolean',
        },
        enableNamespaceFolders: {
            required: false,
            type: 'boolean',
        },
    },
    logging: {
        level: {
            required: false,
            type: 'string',
            enum: ['error', 'warn', 'info', 'debug', 'verbose'],
        },
        filePath: {
            required: false,
            type: 'string',
            min: 1,
            max: 1024,
        },
        enableConsoleLogging: {
            required: false,
            type: 'boolean',
        },
    },
};
export const namespaceExtractionConfig = {
    patterns: {
        kubectl: [
            /kubectl\s+[^-]*-n\s+([a-z0-9\-]+)/gi,
            /kubectl\s+[^-]*--namespace[=\s]+([a-z0-9\-]+)/gi,
            /kubectl\s+[^-]*--namespace=([a-z0-9\-]+)/gi,
        ],
        logPaths: [
            /\/var\/log\/pods\/([a-z0-9\-]+)_/gi,
            /\/var\/log\/containers\/[^_]+_([a-z0-9\-]+)_/gi,
            /logs\/([a-z0-9\-]+)\//gi,
        ],
        content: [
            /(?:namespace[\/\-_]|ns[\/\-_])([a-z0-9\-]+)/gi,
            /([a-z0-9\-]+):[\w\-]+/gi,
            /namespace[:\s]+([a-z0-9\-]+)/gi,
            /in\s+namespace\s+([a-z0-9\-]+)/gi,
        ],
        podNames: [
            /^([a-z0-9\-]+)-[a-z0-9]{5,10}-[a-z0-9]{5}$/gi,
            /^([a-z0-9\-]+)-\d+$/gi,
        ],
    },
    priorities: {
        parameter: 100,
        kubectl: 90,
        'log-path': 80,
        'pod-name': 70,
        content: 60,
        context: 50,
        default: 0,
    },
    confidenceThresholds: {
        high: 80,
        medium: 50,
        low: 20,
    },
};
//# sourceMappingURL=schema.js.map