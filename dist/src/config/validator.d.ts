/**
 * Configuration validation utilities
 */
import { ConfigSchema } from './schema.js';
export interface ValidationError {
    path: string;
    message: string;
    value?: any;
}
export interface ValidationResult {
    isValid: boolean;
    errors: ValidationError[];
    warnings: ValidationError[];
}
/**
 * Validates a configuration object against a schema
 */
export declare function validateConfig(config: any, schema: ConfigSchema, path?: string): ValidationResult;
/**
 * Formats validation errors for display
 */
export declare function formatValidationErrors(result: ValidationResult): string;
/**
 * Validates environment variables before parsing
 */
export declare function validateEnvironmentVariables(): ValidationResult;
//# sourceMappingURL=validator.d.ts.map