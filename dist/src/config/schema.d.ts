/**
 * Configuration schema and validation for the S3 MCP Server
 */
export interface ConfigValidationRule {
    required?: boolean;
    type?: 'string' | 'number' | 'boolean' | 'array';
    min?: number;
    max?: number;
    pattern?: RegExp;
    enum?: string[];
    validator?: (value: any) => boolean | string;
}
export interface ConfigSchema {
    [key: string]: ConfigValidationRule | ConfigSchema;
}
/**
 * Configuration schema definition
 */
export declare const configSchema: ConfigSchema;
/**
 * Kubernetes namespace extraction patterns configuration
 */
export interface NamespaceExtractionConfig {
    patterns: {
        kubectl: RegExp[];
        logPaths: RegExp[];
        content: RegExp[];
        podNames: RegExp[];
    };
    priorities: {
        [source: string]: number;
    };
    confidenceThresholds: {
        high: number;
        medium: number;
        low: number;
    };
}
export declare const namespaceExtractionConfig: NamespaceExtractionConfig;
//# sourceMappingURL=schema.d.ts.map