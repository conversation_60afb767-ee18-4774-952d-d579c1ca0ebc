/**
 * Enhanced configuration management for the S3 MCP Server
 */
import { ServerConfig } from './types.js';
/**
 * Gets the server configuration (synchronous version for backward compatibility)
 */
export declare function getConfig(): ServerConfig;
/**
 * Gets the server configuration (async version with full validation)
 */
export declare function getConfigAsync(): Promise<ServerConfig>;
/**
 * Clears the configuration cache (useful for testing)
 */
export declare function clearConfigCache(): void;
/**
 * Gets masked configuration for logging (hides sensitive values)
 */
export declare function getMaskedConfig(): any;
//# sourceMappingURL=config.d.ts.map