/**
 * S3 Integration Module
 * Handles uploading AI responses to S3 with Kubernetes namespace-based organization
 */
import { S3Client as AWSS3Client, PutObjectCommand, HeadBucketCommand } from '@aws-sdk/client-s3';
import { getConfig } from './config.js';
import logger from './logger.js';
export class S3Client {
    client;
    config;
    stats;
    uploadTimes = [];
    constructor() {
        this.config = getConfig();
        this.stats = {
            totalUploads: 0,
            successfulUploads: 0,
            failedUploads: 0,
            totalBytesUploaded: 0,
            averageUploadTime: 0,
        };
        // Initialize AWS S3 Client
        this.client = new AWSS3Client({
            region: this.config.aws.region,
            credentials: {
                accessKeyId: this.config.aws.accessKeyId,
                secretAccessKey: this.config.aws.secretAccessKey,
            },
            maxAttempts: 3,
            retryMode: 'adaptive',
            logger: {
                debug: (msg) => logger.debug('AWS S3 Debug', { message: msg }),
                info: (msg) => logger.info('AWS S3 Info', { message: msg }),
                warn: (msg) => logger.warn('AWS S3 Warning', { message: msg }),
                error: (msg) => logger.error('AWS S3 Error', { message: msg }),
            },
        });
        logger.info('S3 Client initialized', {
            region: this.config.aws.region,
            bucket: this.config.aws.bucketName,
        });
    }
    /**
     * Upload an AI response to S3 with namespace-based organization
     */
    async uploadResponse(response, options = {}) {
        const startTime = Date.now();
        this.stats.totalUploads++;
        try {
            // Generate S3 key with namespace-based organization
            const key = this.generateS3Key(response);
            // Prepare file content with metadata
            const fileContent = this.formatResponseContent(response);
            const contentBuffer = Buffer.from(fileContent, 'utf-8');
            // Prepare S3 upload parameters
            const uploadParams = {
                Bucket: this.config.aws.bucketName,
                Key: key,
                Body: contentBuffer,
                ContentType: options.contentType || 'text/plain; charset=utf-8',
                ContentLength: contentBuffer.length,
                Metadata: {
                    ...this.generateMetadata(response),
                    ...options.metadata,
                },
                StorageClass: options.storageClass || 'STANDARD',
                ServerSideEncryption: options.serverSideEncryption || 'AES256',
                ...(options.kmsKeyId && { SSEKMSKeyId: options.kmsKeyId }),
                ...(options.tags && { Tagging: this.formatTags(options.tags) }),
            };
            logger.debug('Uploading response to S3', {
                key,
                bucket: this.config.aws.bucketName,
                contentLength: contentBuffer.length,
                namespace: response.namespace.namespace,
                responseId: response.id,
            });
            // Upload to S3
            const command = new PutObjectCommand(uploadParams);
            const result = await this.client.send(command);
            // Update statistics
            const uploadTime = Date.now() - startTime;
            this.updateStats(true, contentBuffer.length, uploadTime);
            logger.info('Successfully uploaded response to S3', {
                key,
                bucket: this.config.aws.bucketName,
                etag: result.ETag,
                uploadTime,
                responseId: response.id,
            });
            return {
                success: true,
                key,
                bucket: this.config.aws.bucketName,
                url: this.generateS3Url(key),
                etag: result.ETag || undefined,
                uploadTime,
                size: contentBuffer.length,
            };
        }
        catch (error) {
            const uploadTime = Date.now() - startTime;
            this.updateStats(false, 0, uploadTime, error);
            const errorMessage = error instanceof Error ? error.message : String(error);
            logger.error('Failed to upload response to S3', {
                error: errorMessage,
                responseId: response.id,
                namespace: response.namespace.namespace,
                uploadTime,
            });
            return {
                success: false,
                key: '',
                bucket: this.config.aws.bucketName,
                error: errorMessage,
                uploadTime,
            };
        }
    }
    /**
     * Generate S3 key with namespace-based folder organization
     */
    generateS3Key(response) {
        const namespace = response.namespace.namespace;
        const timestamp = response.timestamp;
        const dateStr = timestamp.toISOString().split('T')[0] || '1970-01-01'; // YYYY-MM-DD
        const timeStr = timestamp.toISOString().split('T')[1]?.split('.')[0]?.replace(/:/g, '') || '000000'; // HHMMSS
        const uuid = response.id.split('-')[0] || 'unknown'; // First part of UUID for brevity
        // Build the key components
        const basePath = this.config.storage.basePath;
        const namespaceFolder = this.config.storage.enableNamespaceFolders ? namespace : '';
        const dateFolder = this.config.storage.enableDateFolders ? dateStr : '';
        // Filename format: namespace_YYYYMMDD_HHMMSS_uuid.txt
        const filename = `${namespace}_${dateStr.replace(/-/g, '')}_${timeStr}_${uuid}.txt`;
        // Construct the full key
        const keyParts = [basePath, namespaceFolder, dateFolder, filename].filter(part => part && part.length > 0);
        return keyParts.join('/');
    }
    /**
     * Format the AI response content for storage
     */
    formatResponseContent(response) {
        const lines = [
            '=== AI Response Metadata ===',
            `Response ID: ${response.id}`,
            `Timestamp: ${response.timestamp.toISOString()}`,
            `Namespace: ${response.namespace.namespace}`,
            `Namespace Source: ${response.namespace.source}`,
            `Namespace Confidence: ${response.namespace.confidence}`,
            ...(response.namespace.extractedFrom ? [`Extracted From: ${response.namespace.extractedFrom}`] : []),
            ...(response.conversationId ? [`Conversation ID: ${response.conversationId}`] : []),
            ...(response.sessionId ? [`Session ID: ${response.sessionId}`] : []),
            ...(response.userId ? [`User ID: ${response.userId}`] : []),
            ...(response.requestType ? [`Request Type: ${response.requestType}`] : []),
            ...(response.podNames?.length ? [`Pod Names: ${response.podNames.join(', ')}`] : []),
            '',
            '=== Kubernetes Context ===',
            ...(response.metadata?.['kubernetesResources'] ? [
                `Resources: ${response.metadata['kubernetesResources'].map((r) => `${r.kind}/${r.name}`).join(', ')}`
            ] : []),
            ...(response.metadata?.['kubernetesErrors'] ? [
                `Errors Detected: ${response.metadata['kubernetesErrors'].length}`,
                ...response.metadata['kubernetesErrors'].map((e) => `  - ${e.type}: ${e.message.substring(0, 100)}...`)
            ] : []),
            ...(response.metadata?.['kubernetesMetrics'] ? [
                'Metrics:',
                ...(response.metadata['kubernetesMetrics'].cpu ? [`  CPU: ${JSON.stringify(response.metadata['kubernetesMetrics'].cpu)}`] : []),
                ...(response.metadata['kubernetesMetrics'].memory ? [`  Memory: ${JSON.stringify(response.metadata['kubernetesMetrics'].memory)}`] : []),
                ...(response.metadata['kubernetesMetrics'].percentages ? [`  Percentages: ${response.metadata['kubernetesMetrics'].percentages.join(', ')}%`] : [])
            ] : []),
            '',
            '=== AI Response Content ===',
            response.content,
            '',
            '=== End of Response ===',
        ];
        return lines.join('\n');
    }
    /**
     * Generate metadata for S3 object
     */
    generateMetadata(response) {
        const metadata = {
            'response-id': response.id,
            'namespace': response.namespace.namespace,
            'namespace-source': response.namespace.source,
            'namespace-confidence': response.namespace.confidence,
            'timestamp': response.timestamp.toISOString(),
            'content-length': response.content.length.toString(),
        };
        // Add optional metadata
        if (response.conversationId) {
            metadata['conversation-id'] = response.conversationId;
        }
        if (response.sessionId) {
            metadata['session-id'] = response.sessionId;
        }
        if (response.userId) {
            metadata['user-id'] = response.userId;
        }
        if (response.requestType) {
            metadata['request-type'] = response.requestType;
        }
        if (response.podNames?.length) {
            metadata['pod-names'] = response.podNames.join(',');
        }
        if (response.metadata?.['hasErrors']) {
            metadata['has-errors'] = 'true';
        }
        return metadata;
    }
    /**
     * Format tags for S3 object tagging
     */
    formatTags(tags) {
        return Object.entries(tags)
            .map(([key, value]) => `${encodeURIComponent(key)}=${encodeURIComponent(value)}`)
            .join('&');
    }
    /**
     * Generate S3 URL for the uploaded object
     */
    generateS3Url(key) {
        return `https://${this.config.aws.bucketName}.s3.${this.config.aws.region}.amazonaws.com/${key}`;
    }
    /**
     * Update upload statistics
     */
    updateStats(success, bytes, uploadTime, error) {
        if (success) {
            this.stats.successfulUploads++;
            this.stats.totalBytesUploaded += bytes;
        }
        else {
            this.stats.failedUploads++;
            this.stats.lastError = error instanceof Error ? error.message : String(error);
        }
        this.stats.lastUploadTime = new Date();
        // Track upload times for average calculation
        this.uploadTimes.push(uploadTime);
        if (this.uploadTimes.length > 100) {
            this.uploadTimes.shift(); // Keep only last 100 upload times
        }
        // Calculate average upload time
        this.stats.averageUploadTime = this.uploadTimes.reduce((sum, time) => sum + time, 0) / this.uploadTimes.length;
    }
    /**
     * Test S3 connection and bucket access
     */
    async testConnection() {
        try {
            const command = new HeadBucketCommand({ Bucket: this.config.aws.bucketName });
            await this.client.send(command);
            logger.info('S3 connection test successful', {
                bucket: this.config.aws.bucketName,
                region: this.config.aws.region,
            });
            return { success: true };
        }
        catch (error) {
            const errorMessage = error instanceof Error ? error.message : String(error);
            logger.error('S3 connection test failed', {
                error: errorMessage,
                bucket: this.config.aws.bucketName,
                region: this.config.aws.region,
            });
            return { success: false, error: errorMessage };
        }
    }
    /**
     * Get upload statistics
     */
    getStats() {
        return { ...this.stats };
    }
    /**
     * Reset statistics
     */
    resetStats() {
        this.stats = {
            totalUploads: 0,
            successfulUploads: 0,
            failedUploads: 0,
            totalBytesUploaded: 0,
            averageUploadTime: 0,
        };
        this.uploadTimes = [];
    }
    /**
     * Batch upload multiple responses
     */
    async uploadBatch(responses, options = {}) {
        logger.info('Starting batch upload', { count: responses.length });
        const results = await Promise.allSettled(responses.map(response => this.uploadResponse(response, options)));
        const uploadResults = results.map((result, index) => {
            if (result.status === 'fulfilled') {
                return result.value;
            }
            else {
                logger.error('Batch upload item failed', {
                    index,
                    error: result.reason,
                    responseId: responses[index]?.id,
                });
                return {
                    success: false,
                    key: '',
                    bucket: this.config.aws.bucketName,
                    error: result.reason instanceof Error ? result.reason.message : String(result.reason),
                };
            }
        });
        const successful = uploadResults.filter(r => r.success).length;
        logger.info('Batch upload completed', {
            total: responses.length,
            successful,
            failed: responses.length - successful,
        });
        return uploadResults;
    }
    /**
     * Clean up old uploads (if lifecycle policies are not configured)
     */
    async cleanupOldUploads(olderThanDays = 30) {
        // This would require listing objects and deleting old ones
        // For now, we'll just log that this feature could be implemented
        logger.info('Cleanup old uploads requested', { olderThanDays });
        // TODO: Implement actual cleanup logic using ListObjectsV2 and DeleteObjects
        // This would require additional permissions and careful implementation
        return { deleted: 0, errors: ['Cleanup not yet implemented - use S3 lifecycle policies instead'] };
    }
}
//# sourceMappingURL=s3-client.js.map