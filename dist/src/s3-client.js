/**
 * S3 Integration Module
 * Handles uploading AI responses to S3 with Kubernetes namespace-based organization
 */
import { S3Client as AWSS3Client, PutObjectCommand, HeadBucketCommand } from '@aws-sdk/client-s3';
import { getConfig } from './config.js';
import logger from './logger.js';
import enhancedLogger from './error-handling/enhanced-logger.js';
import { RetryManager, CircuitBreaker } from './error-handling/retry.js';
import { ErrorFactory } from './error-handling/error-types.js';
export class S3Client {
    client;
    config;
    stats;
    uploadTimes = [];
    circuitBreaker;
    logger = enhancedLogger.child({ component: 'S3Client' });
    constructor() {
        this.config = getConfig();
        this.stats = {
            totalUploads: 0,
            successfulUploads: 0,
            failedUploads: 0,
            totalBytesUploaded: 0,
            averageUploadTime: 0,
        };
        // Initialize circuit breaker for S3 operations
        this.circuitBreaker = new CircuitBreaker(5, 60000, 'S3Client');
        // Initialize AWS S3 Client
        this.client = new AWSS3Client({
            region: this.config.aws.region,
            credentials: {
                accessKeyId: this.config.aws.accessKeyId,
                secretAccessKey: this.config.aws.secretAccessKey,
            },
            maxAttempts: 1, // We'll handle retries ourselves
            retryMode: 'standard',
            logger: {
                debug: (msg) => this.logger.debug('AWS S3 Debug', { message: msg }),
                info: (msg) => this.logger.info('AWS S3 Info', { message: msg }),
                warn: (msg) => this.logger.warn('AWS S3 Warning', { message: msg }),
                error: (msg) => this.logger.error('AWS S3 Error', { message: msg }),
            },
        });
        this.logger.info('S3 Client initialized', {
            region: this.config.aws.region,
            bucket: this.config.aws.bucketName,
            circuitBreakerEnabled: true,
        });
    }
    /**
     * Upload an AI response to S3 with namespace-based organization
     */
    async uploadResponse(response, options = {}) {
        const startTime = Date.now();
        this.stats.totalUploads++;
        const context = {
            requestId: response.id,
            ...(response.conversationId && { conversationId: response.conversationId }),
            namespace: response.namespace.namespace,
            operation: 'uploadResponse',
        };
        try {
            // Use circuit breaker and retry logic
            const result = await this.circuitBreaker.execute(async () => {
                return await RetryManager.execute(async () => {
                    // Generate S3 key with namespace-based organization
                    const key = this.generateS3Key(response);
                    // Prepare file content with metadata
                    const fileContent = this.formatResponseContent(response);
                    const contentBuffer = Buffer.from(fileContent, 'utf-8');
                    // Prepare S3 upload parameters
                    const uploadParams = {
                        Bucket: this.config.aws.bucketName,
                        Key: key,
                        Body: contentBuffer,
                        ContentType: options.contentType || 'text/plain; charset=utf-8',
                        ContentLength: contentBuffer.length,
                        Metadata: {
                            ...this.generateMetadata(response),
                            ...options.metadata,
                        },
                        StorageClass: options.storageClass || 'STANDARD',
                        ServerSideEncryption: options.serverSideEncryption || 'AES256',
                        ...(options.kmsKeyId && { SSEKMSKeyId: options.kmsKeyId }),
                        ...(options.tags && { Tagging: this.formatTags(options.tags) }),
                    };
                    this.logger.debug('Uploading response to S3', {
                        key,
                        bucket: this.config.aws.bucketName,
                        contentLength: contentBuffer.length,
                        namespace: response.namespace.namespace,
                        responseId: response.id,
                    });
                    // Upload to S3
                    const command = new PutObjectCommand(uploadParams);
                    const s3Result = await this.client.send(command);
                    return {
                        key,
                        contentBuffer,
                        s3Result,
                    };
                }, {
                    maxAttempts: 3,
                    baseDelay: 1000,
                    maxDelay: 10000,
                    backoffMultiplier: 2,
                    jitter: true,
                    onRetry: (error, attempt) => {
                        this.logger.warn('Retrying S3 upload', {
                            attempt,
                            error: error.message,
                            responseId: response.id,
                        });
                    },
                }, {
                    operationName: 'S3Upload',
                    requestId: response.id,
                });
            });
            if (!result.success) {
                throw result.error || new Error('S3 upload failed');
            }
            const { key, contentBuffer, s3Result } = result.result;
            const uploadTime = Date.now() - startTime;
            // Update statistics
            this.updateStats(true, contentBuffer.length, uploadTime);
            // Log performance
            this.logger.logPerformance('S3Upload', uploadTime, {
                key,
                bucket: this.config.aws.bucketName,
                size: contentBuffer.length,
                responseId: response.id,
            });
            this.logger.info('Successfully uploaded response to S3', {
                key,
                bucket: this.config.aws.bucketName,
                etag: s3Result.ETag,
                uploadTime,
                responseId: response.id,
            });
            return {
                success: true,
                key,
                bucket: this.config.aws.bucketName,
                url: this.generateS3Url(key),
                etag: s3Result.ETag || undefined,
                uploadTime,
                size: contentBuffer.length,
            };
        }
        catch (error) {
            const uploadTime = Date.now() - startTime;
            this.updateStats(false, 0, uploadTime, error);
            // Create structured error
            const mcpError = ErrorFactory.s3Error(`Failed to upload response to S3: ${error instanceof Error ? error.message : String(error)}`, context, error instanceof Error ? error : undefined);
            this.logger.logError(mcpError, {
                responseId: response.id,
                namespace: response.namespace.namespace,
                uploadTime,
            });
            return {
                success: false,
                key: '',
                bucket: this.config.aws.bucketName,
                error: mcpError.message,
                uploadTime,
            };
        }
    }
    /**
     * Generate S3 key with namespace-based folder organization
     */
    generateS3Key(response) {
        const namespace = response.namespace.namespace;
        const timestamp = response.timestamp;
        const dateStr = timestamp.toISOString().split('T')[0] || '1970-01-01'; // YYYY-MM-DD
        const timeStr = timestamp.toISOString().split('T')[1]?.split('.')[0]?.replace(/:/g, '') || '000000'; // HHMMSS
        const uuid = response.id.split('-')[0] || 'unknown'; // First part of UUID for brevity
        // Build the key components
        const basePath = this.config.storage.basePath;
        const namespaceFolder = this.config.storage.enableNamespaceFolders ? namespace : '';
        const dateFolder = this.config.storage.enableDateFolders ? dateStr : '';
        // Filename format: namespace_YYYYMMDD_HHMMSS_uuid.txt
        const filename = `${namespace}_${dateStr.replace(/-/g, '')}_${timeStr}_${uuid}.txt`;
        // Construct the full key
        const keyParts = [basePath, namespaceFolder, dateFolder, filename].filter(part => part && part.length > 0);
        return keyParts.join('/');
    }
    /**
     * Format the AI response content for storage
     */
    formatResponseContent(response) {
        const lines = [
            '=== AI Response Metadata ===',
            `Response ID: ${response.id}`,
            `Timestamp: ${response.timestamp.toISOString()}`,
            `Namespace: ${response.namespace.namespace}`,
            `Namespace Source: ${response.namespace.source}`,
            `Namespace Confidence: ${response.namespace.confidence}`,
            ...(response.namespace.extractedFrom ? [`Extracted From: ${response.namespace.extractedFrom}`] : []),
            ...(response.conversationId ? [`Conversation ID: ${response.conversationId}`] : []),
            ...(response.sessionId ? [`Session ID: ${response.sessionId}`] : []),
            ...(response.userId ? [`User ID: ${response.userId}`] : []),
            ...(response.requestType ? [`Request Type: ${response.requestType}`] : []),
            ...(response.podNames?.length ? [`Pod Names: ${response.podNames.join(', ')}`] : []),
            '',
            '=== Kubernetes Context ===',
            ...(response.metadata?.['kubernetesResources'] ? [
                `Resources: ${response.metadata['kubernetesResources'].map((r) => `${r.kind}/${r.name}`).join(', ')}`
            ] : []),
            ...(response.metadata?.['kubernetesErrors'] ? [
                `Errors Detected: ${response.metadata['kubernetesErrors'].length}`,
                ...response.metadata['kubernetesErrors'].map((e) => `  - ${e.type}: ${e.message.substring(0, 100)}...`)
            ] : []),
            ...(response.metadata?.['kubernetesMetrics'] ? [
                'Metrics:',
                ...(response.metadata['kubernetesMetrics'].cpu ? [`  CPU: ${JSON.stringify(response.metadata['kubernetesMetrics'].cpu)}`] : []),
                ...(response.metadata['kubernetesMetrics'].memory ? [`  Memory: ${JSON.stringify(response.metadata['kubernetesMetrics'].memory)}`] : []),
                ...(response.metadata['kubernetesMetrics'].percentages ? [`  Percentages: ${response.metadata['kubernetesMetrics'].percentages.join(', ')}%`] : [])
            ] : []),
            '',
            '=== AI Response Content ===',
            response.content,
            '',
            '=== End of Response ===',
        ];
        return lines.join('\n');
    }
    /**
     * Generate metadata for S3 object
     */
    generateMetadata(response) {
        const metadata = {
            'response-id': response.id,
            'namespace': response.namespace.namespace,
            'namespace-source': response.namespace.source,
            'namespace-confidence': response.namespace.confidence,
            'timestamp': response.timestamp.toISOString(),
            'content-length': response.content.length.toString(),
        };
        // Add optional metadata
        if (response.conversationId) {
            metadata['conversation-id'] = response.conversationId;
        }
        if (response.sessionId) {
            metadata['session-id'] = response.sessionId;
        }
        if (response.userId) {
            metadata['user-id'] = response.userId;
        }
        if (response.requestType) {
            metadata['request-type'] = response.requestType;
        }
        if (response.podNames?.length) {
            metadata['pod-names'] = response.podNames.join(',');
        }
        if (response.metadata?.['hasErrors']) {
            metadata['has-errors'] = 'true';
        }
        return metadata;
    }
    /**
     * Format tags for S3 object tagging
     */
    formatTags(tags) {
        return Object.entries(tags)
            .map(([key, value]) => `${encodeURIComponent(key)}=${encodeURIComponent(value)}`)
            .join('&');
    }
    /**
     * Generate S3 URL for the uploaded object
     */
    generateS3Url(key) {
        return `https://${this.config.aws.bucketName}.s3.${this.config.aws.region}.amazonaws.com/${key}`;
    }
    /**
     * Update upload statistics
     */
    updateStats(success, bytes, uploadTime, error) {
        if (success) {
            this.stats.successfulUploads++;
            this.stats.totalBytesUploaded += bytes;
        }
        else {
            this.stats.failedUploads++;
            this.stats.lastError = error instanceof Error ? error.message : String(error);
        }
        this.stats.lastUploadTime = new Date();
        // Track upload times for average calculation
        this.uploadTimes.push(uploadTime);
        if (this.uploadTimes.length > 100) {
            this.uploadTimes.shift(); // Keep only last 100 upload times
        }
        // Calculate average upload time
        this.stats.averageUploadTime = this.uploadTimes.reduce((sum, time) => sum + time, 0) / this.uploadTimes.length;
    }
    /**
     * Test S3 connection and bucket access
     */
    async testConnection() {
        const startTime = Date.now();
        try {
            const result = await RetryManager.execute(async () => {
                const command = new HeadBucketCommand({ Bucket: this.config.aws.bucketName });
                return await this.client.send(command);
            }, {
                maxAttempts: 2,
                baseDelay: 500,
                maxDelay: 2000,
            }, {
                operationName: 'S3ConnectionTest',
            });
            if (!result.success) {
                throw result.error || new Error('Connection test failed');
            }
            const duration = Date.now() - startTime;
            this.logger.logPerformance('S3ConnectionTest', duration);
            this.logger.info('S3 connection test successful', {
                bucket: this.config.aws.bucketName,
                region: this.config.aws.region,
                duration,
            });
            return { success: true };
        }
        catch (error) {
            const duration = Date.now() - startTime;
            const mcpError = ErrorFactory.s3Error(`S3 connection test failed: ${error instanceof Error ? error.message : String(error)}`, {
                operation: 'testConnection',
            }, error instanceof Error ? error : undefined);
            this.logger.logError(mcpError, {
                bucket: this.config.aws.bucketName,
                region: this.config.aws.region,
                duration,
            });
            return { success: false, error: mcpError.message };
        }
    }
    /**
     * Get upload statistics
     */
    getStats() {
        return { ...this.stats };
    }
    /**
     * Reset statistics
     */
    resetStats() {
        this.stats = {
            totalUploads: 0,
            successfulUploads: 0,
            failedUploads: 0,
            totalBytesUploaded: 0,
            averageUploadTime: 0,
        };
        this.uploadTimes = [];
    }
    /**
     * Batch upload multiple responses
     */
    async uploadBatch(responses, options = {}) {
        logger.info('Starting batch upload', { count: responses.length });
        const results = await Promise.allSettled(responses.map(response => this.uploadResponse(response, options)));
        const uploadResults = results.map((result, index) => {
            if (result.status === 'fulfilled') {
                return result.value;
            }
            else {
                logger.error('Batch upload item failed', {
                    index,
                    error: result.reason,
                    responseId: responses[index]?.id,
                });
                return {
                    success: false,
                    key: '',
                    bucket: this.config.aws.bucketName,
                    error: result.reason instanceof Error ? result.reason.message : String(result.reason),
                };
            }
        });
        const successful = uploadResults.filter(r => r.success).length;
        logger.info('Batch upload completed', {
            total: responses.length,
            successful,
            failed: responses.length - successful,
        });
        return uploadResults;
    }
    /**
     * Clean up old uploads (if lifecycle policies are not configured)
     */
    async cleanupOldUploads(olderThanDays = 30) {
        // This would require listing objects and deleting old ones
        // For now, we'll just log that this feature could be implemented
        logger.info('Cleanup old uploads requested', { olderThanDays });
        // TODO: Implement actual cleanup logic using ListObjectsV2 and DeleteObjects
        // This would require additional permissions and careful implementation
        return { deleted: 0, errors: ['Cleanup not yet implemented - use S3 lifecycle policies instead'] };
    }
}
//# sourceMappingURL=s3-client.js.map