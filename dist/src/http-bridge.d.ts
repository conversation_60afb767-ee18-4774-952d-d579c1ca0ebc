/**
 * HTTP Bridge for MCP Server
 * Provides HTTP/REST endpoints that proxy to the MCP server
 */
export declare class HTTPBridge {
    private app;
    private s3Client;
    private interceptor;
    private config;
    private logger;
    constructor();
    private setupMiddleware;
    private setupKubernetesMiddleware;
    private setupMCPServer;
    private setupRoutes;
    private setupHealthMonitoring;
    private handleCaptureResponse;
    start(port?: number): void;
    stop(): void;
}
declare global {
    namespace Express {
        interface Request {
            requestId?: string;
        }
    }
}
//# sourceMappingURL=http-bridge.d.ts.map