/**
 * Webhook-based AI Response Interceptor
 * Provides HTTP endpoints for real-time AI response interception
 */
import { AIResponseInterceptor } from './interceptor.js';
export interface WebhookConfig {
    port: number;
    path: string;
    secret?: string | undefined;
    enableCors: boolean;
    maxPayloadSize: string;
}
export interface WebhookPayload {
    content: string;
    conversationId?: string;
    sessionId?: string;
    userId?: string;
    namespace?: string;
    podNames?: string[];
    requestType?: string;
    metadata?: Record<string, any>;
    isStreaming?: boolean;
    streamId?: string;
    isComplete?: boolean;
}
export declare class WebhookInterceptor {
    private app;
    private server;
    private interceptor;
    private webhookConfig;
    constructor(webhookConfig?: Partial<WebhookConfig>);
    /**
     * Start the webhook server
     */
    start(): Promise<void>;
    /**
     * Stop the webhook server
     */
    stop(): Promise<void>;
    /**
     * Get the interceptor instance for adding custom middleware
     */
    getInterceptor(): AIResponseInterceptor;
    /**
     * Setup Express middleware
     */
    private setupMiddleware;
    /**
     * Setup Express routes
     */
    private setupRoutes;
    /**
     * Handle single webhook request
     */
    private handleWebhookRequest;
    /**
     * Handle batch webhook request
     */
    private handleBatchWebhookRequest;
    /**
     * Setup error handling
     */
    private setupErrorHandling;
}
//# sourceMappingURL=webhook-interceptor.d.ts.map