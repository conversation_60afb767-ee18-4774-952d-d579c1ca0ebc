{"version": 3, "file": "s3-client.d.ts", "sourceRoot": "", "sources": ["../../src/s3-client.ts"], "names": [], "mappings": "AAAA;;;GAGG;AAGH,OAAO,EAAE,cAAc,EAAE,UAAU,EAAgB,MAAM,YAAY,CAAC;AAOtE,MAAM,WAAW,eAAe;IAC9B,WAAW,CAAC,EAAE,MAAM,CAAC;IACrB,QAAQ,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;IAClC,IAAI,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;IAC9B,YAAY,CAAC,EAAE,UAAU,GAAG,oBAAoB,GAAG,aAAa,GAAG,YAAY,GAAG,qBAAqB,GAAG,SAAS,GAAG,cAAc,CAAC;IACrI,oBAAoB,CAAC,EAAE,QAAQ,GAAG,SAAS,CAAC;IAC5C,QAAQ,CAAC,EAAE,MAAM,CAAC;CACnB;AAED,MAAM,WAAW,aAAa;IAC5B,YAAY,EAAE,MAAM,CAAC;IACrB,iBAAiB,EAAE,MAAM,CAAC;IAC1B,aAAa,EAAE,MAAM,CAAC;IACtB,kBAAkB,EAAE,MAAM,CAAC;IAC3B,iBAAiB,EAAE,MAAM,CAAC;IAC1B,cAAc,CAAC,EAAE,IAAI,CAAC;IACtB,SAAS,CAAC,EAAE,MAAM,CAAC;CACpB;AAED,qBAAa,QAAQ;IACnB,OAAO,CAAC,MAAM,CAAc;IAC5B,OAAO,CAAC,MAAM,CAAe;IAC7B,OAAO,CAAC,KAAK,CAAgB;IAC7B,OAAO,CAAC,WAAW,CAAgB;IACnC,OAAO,CAAC,cAAc,CAAiB;IACvC,OAAO,CAAC,MAAM,CAAmD;;IAuCjE;;OAEG;IACG,cAAc,CAAC,QAAQ,EAAE,UAAU,EAAE,OAAO,GAAE,eAAoB,GAAG,OAAO,CAAC,cAAc,CAAC;IA8IlG;;OAEG;IACH,OAAO,CAAC,aAAa;IAoBrB;;OAEG;IACH,OAAO,CAAC,qBAAqB;IAuC7B;;OAEG;IACH,OAAO,CAAC,gBAAgB;IAiCxB;;OAEG;IACH,OAAO,CAAC,UAAU;IAMlB;;OAEG;IACH,OAAO,CAAC,aAAa;IAIrB;;OAEG;IACH,OAAO,CAAC,WAAW;IAqBnB;;OAEG;IACG,cAAc,IAAI,OAAO,CAAC;QAAE,OAAO,EAAE,OAAO,CAAC;QAAC,KAAK,CAAC,EAAE,MAAM,CAAA;KAAE,CAAC;IAqDrE;;OAEG;IACH,QAAQ,IAAI,aAAa;IAIzB;;OAEG;IACH,UAAU,IAAI,IAAI;IAWlB;;OAEG;IACG,WAAW,CAAC,SAAS,EAAE,UAAU,EAAE,EAAE,OAAO,GAAE,eAAoB,GAAG,OAAO,CAAC,cAAc,EAAE,CAAC;IAmCpG;;OAEG;IACG,iBAAiB,CAAC,aAAa,GAAE,MAAW,GAAG,OAAO,CAAC;QAAE,OAAO,EAAE,MAAM,CAAC;QAAC,MAAM,EAAE,MAAM,EAAE,CAAA;KAAE,CAAC;CAUpG"}