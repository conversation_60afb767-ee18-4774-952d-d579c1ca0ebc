{"version": 3, "file": "response-handler.js", "sourceRoot": "", "sources": ["../../src/response-handler.ts"], "names": [], "mappings": "AAAA;;;GAGG;AAEH,OAAO,EAAE,EAAE,IAAI,MAAM,EAAE,MAAM,MAAM,CAAC;AAEpC,OAAO,EAAE,SAAS,EAAE,MAAM,aAAa,CAAC;AACxC,OAAO,MAAM,MAAM,aAAa,CAAC;AAEjC,MAAM,OAAO,eAAe;IAClB,MAAM,CAA+B;IAE7C;QACE,IAAI,CAAC,MAAM,GAAG,SAAS,EAAE,CAAC;IAC5B,CAAC;IAED,KAAK,CAAC,eAAe,CAAC,OAAe,EAAE,OAAa;QAClD,MAAM,EAAE,GAAG,MAAM,EAAE,CAAC;QACpB,MAAM,SAAS,GAAG,IAAI,IAAI,EAAE,CAAC;QAE7B,MAAM,CAAC,KAAK,CAAC,wBAAwB,EAAE;YACrC,EAAE;YACF,aAAa,EAAE,OAAO,CAAC,MAAM;YAC7B,OAAO;SACR,CAAC,CAAC;QAEH,gCAAgC;QAChC,MAAM,aAAa,GAAG,IAAI,CAAC,gBAAgB,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;QAE9D,6CAA6C;QAC7C,MAAM,QAAQ,GAAG,IAAI,CAAC,eAAe,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;QAExD,MAAM,QAAQ,GAAe;YAC3B,EAAE;YACF,SAAS;YACT,OAAO;YACP,SAAS,EAAE,aAAa;YACxB,cAAc,EAAE,OAAO,EAAE,cAAc;YACvC,WAAW,EAAE,OAAO,EAAE,WAAW;YACjC,QAAQ;YACR,QAAQ,EAAE,OAAO,EAAE,QAAQ;SAC5B,CAAC;QAEF,MAAM,CAAC,IAAI,CAAC,uBAAuB,EAAE;YACnC,EAAE;YACF,SAAS,EAAE,aAAa,CAAC,SAAS;YAClC,eAAe,EAAE,aAAa,CAAC,MAAM;YACrC,mBAAmB,EAAE,aAAa,CAAC,UAAU;YAC7C,QAAQ,EAAE,QAAQ,EAAE,MAAM,IAAI,CAAC;SAChC,CAAC,CAAC;QAEH,OAAO,QAAQ,CAAC;IAClB,CAAC;IAEO,gBAAgB,CAAC,OAAe,EAAE,OAAa;QACrD,0DAA0D;QAE1D,wDAAwD;QACxD,IAAI,OAAO,EAAE,SAAS,EAAE,CAAC;YACvB,OAAO;gBACL,SAAS,EAAE,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,SAAS,CAAC;gBACpD,UAAU,EAAE,MAAM;gBAClB,MAAM,EAAE,WAAW;gBACnB,aAAa,EAAE,mBAAmB;aACnC,CAAC;QACJ,CAAC;QAED,8CAA8C;QAC9C,MAAM,gBAAgB,GAAG,IAAI,CAAC,0BAA0B,CAAC,OAAO,CAAC,CAAC;QAClE,IAAI,gBAAgB,EAAE,CAAC;YACrB,OAAO,gBAAgB,CAAC;QAC1B,CAAC;QAED,uCAAuC;QACvC,IAAI,OAAO,EAAE,QAAQ,IAAI,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE,CAAC;YACzD,MAAM,YAAY,GAAG,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;YAChE,IAAI,YAAY,EAAE,CAAC;gBACjB,OAAO,YAAY,CAAC;YACtB,CAAC;QACH,CAAC;QAED,uCAAuC;QACvC,MAAM,mBAAmB,GAAG,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC,CAAC;QAC7D,IAAI,mBAAmB,EAAE,CAAC;YACxB,OAAO,mBAAmB,CAAC;QAC7B,CAAC;QAED,4BAA4B;QAC5B,MAAM,gBAAgB,GAAG,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC,CAAC;QAC3D,IAAI,gBAAgB,EAAE,CAAC;YACrB,OAAO,gBAAgB,CAAC;QAC1B,CAAC;QAED,sBAAsB;QACtB,OAAO;YACL,SAAS,EAAE,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,sBAAsB;YACxD,UAAU,EAAE,KAAK;YACjB,MAAM,EAAE,SAAS;SAClB,CAAC;IACJ,CAAC;IAED,kBAAkB,CAAC,OAAY;QAC7B,IAAI,OAAO,EAAE,SAAS,EAAE,CAAC;YACvB,OAAO;gBACL,SAAS,EAAE,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,SAAS,CAAC;gBACpD,UAAU,EAAE,MAAM;gBAClB,MAAM,EAAE,WAAW;gBACnB,aAAa,EAAE,mBAAmB;aACnC,CAAC;QACJ,CAAC;QACD,OAAO,IAAI,CAAC;IACd,CAAC;IAED,kBAAkB,CAAC,OAAe;QAChC,iDAAiD;QACjD,MAAM,QAAQ,GAAG;YACf,qEAAqE;YACrE,+CAA+C;YAC/C,qDAAqD;YACrD,yBAAyB;YACzB,qCAAqC;YACrC,gCAAgC;YAChC,oDAAoD;YACpD,kCAAkC;SACnC,CAAC;QAEF,KAAK,MAAM,OAAO,IAAI,QAAQ,EAAE,CAAC;YAC/B,MAAM,OAAO,GAAG,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;YAC1C,KAAK,MAAM,KAAK,IAAI,OAAO,EAAE,CAAC;gBAC5B,MAAM,SAAS,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;gBAC3B,IAAI,SAAS,IAAI,IAAI,CAAC,gBAAgB,CAAC,SAAS,CAAC,EAAE,CAAC;oBAClD,OAAO;wBACL,SAAS,EAAE,IAAI,CAAC,iBAAiB,CAAC,SAAS,CAAC;wBAC5C,UAAU,EAAE,QAAQ;wBACpB,MAAM,EAAE,SAAS;wBACjB,aAAa,EAAE,KAAK,CAAC,CAAC,CAAC;qBACxB,CAAC;gBACJ,CAAC;YACH,CAAC;QACH,CAAC;QAED,OAAO,IAAI,CAAC;IACd,CAAC;IAED,kBAAkB,CAAC,OAAY;QAC7B,IAAI,OAAO,EAAE,SAAS,EAAE,CAAC;YACvB,OAAO;gBACL,SAAS,EAAE,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,SAAS,CAAC;gBACpD,UAAU,EAAE,MAAM;gBAClB,MAAM,EAAE,SAAS;gBACjB,aAAa,EAAE,mBAAmB;aACnC,CAAC;QACJ,CAAC;QACD,OAAO,IAAI,CAAC;IACd,CAAC;IAEO,0BAA0B,CAAC,OAAe;QAChD,yDAAyD;QACzD,MAAM,eAAe,GAAG;YACtB,qCAAqC;YACrC,iDAAiD;YACjD,4CAA4C;SAC7C,CAAC;QAEF,KAAK,MAAM,OAAO,IAAI,eAAe,EAAE,CAAC;YACtC,MAAM,OAAO,GAAG,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;YAC1C,KAAK,MAAM,KAAK,IAAI,OAAO,EAAE,CAAC;gBAC5B,MAAM,SAAS,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;gBAC3B,IAAI,SAAS,IAAI,IAAI,CAAC,gBAAgB,CAAC,SAAS,CAAC,EAAE,CAAC;oBAClD,OAAO;wBACL,SAAS,EAAE,IAAI,CAAC,iBAAiB,CAAC,SAAS,CAAC;wBAC5C,UAAU,EAAE,MAAM;wBAClB,MAAM,EAAE,SAAS;wBACjB,aAAa,EAAE,KAAK,CAAC,CAAC,CAAC;qBACxB,CAAC;gBACJ,CAAC;YACH,CAAC;QACH,CAAC;QAED,OAAO,IAAI,CAAC;IACd,CAAC;IAEO,mBAAmB,CAAC,QAAkB;QAC5C,uDAAuD;QACvD,KAAK,MAAM,OAAO,IAAI,QAAQ,EAAE,CAAC;YAC/B,2CAA2C;YAC3C,MAAM,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;YACpC,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBACrB,MAAM,kBAAkB,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;gBACpC,IAAI,kBAAkB,IAAI,IAAI,CAAC,gBAAgB,CAAC,kBAAkB,CAAC,EAAE,CAAC;oBACpE,OAAO;wBACL,SAAS,EAAE,IAAI,CAAC,iBAAiB,CAAC,kBAAkB,CAAC;wBACrD,UAAU,EAAE,QAAQ;wBACpB,MAAM,EAAE,UAAU;wBAClB,aAAa,EAAE,OAAO;qBACvB,CAAC;gBACJ,CAAC;YACH,CAAC;QACH,CAAC;QAED,OAAO,IAAI,CAAC;IACd,CAAC;IAEO,mBAAmB,CAAC,OAAe;QACzC,mEAAmE;QACnE,MAAM,eAAe,GAAG;YACtB,oCAAoC;YACpC,gDAAgD;YAChD,yBAAyB;SAC1B,CAAC;QAEF,KAAK,MAAM,OAAO,IAAI,eAAe,EAAE,CAAC;YACtC,MAAM,OAAO,GAAG,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;YAC1C,KAAK,MAAM,KAAK,IAAI,OAAO,EAAE,CAAC;gBAC5B,MAAM,SAAS,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;gBAC3B,IAAI,SAAS,IAAI,IAAI,CAAC,gBAAgB,CAAC,SAAS,CAAC,EAAE,CAAC;oBAClD,OAAO;wBACL,SAAS,EAAE,IAAI,CAAC,iBAAiB,CAAC,SAAS,CAAC;wBAC5C,UAAU,EAAE,QAAQ;wBACpB,MAAM,EAAE,UAAU;wBAClB,aAAa,EAAE,KAAK,CAAC,CAAC,CAAC;qBACxB,CAAC;gBACJ,CAAC;YACH,CAAC;QACH,CAAC;QAED,OAAO,IAAI,CAAC;IACd,CAAC;IAEO,eAAe,CAAC,OAAe,EAAE,OAAa;QACpD,MAAM,QAAQ,GAAa,EAAE,CAAC;QAE9B,6BAA6B;QAC7B,IAAI,OAAO,EAAE,QAAQ,IAAI,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE,CAAC;YACzD,QAAQ,CAAC,IAAI,CAAC,GAAG,OAAO,CAAC,QAAQ,CAAC,CAAC;QACrC,CAAC;QAED,uDAAuD;QACvD,MAAM,WAAW,GAAG;YAClB,2CAA2C;YAC3C,gDAAgD;YAChD,8BAA8B;YAC9B,6BAA6B;YAC7B,oBAAoB;YACpB,2BAA2B;SAC5B,CAAC;QAEF,KAAK,MAAM,OAAO,IAAI,WAAW,EAAE,CAAC;YAClC,MAAM,OAAO,GAAG,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;YAC1C,KAAK,MAAM,KAAK,IAAI,OAAO,EAAE,CAAC;gBAC5B,MAAM,OAAO,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;gBACzB,IAAI,OAAO,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE,CAAC;oBAC3C,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;gBACzB,CAAC;YACH,CAAC;QACH,CAAC;QAED,OAAO,QAAQ,CAAC;IAClB,CAAC;IAEO,gBAAgB,CAAC,SAAiB;QACxC,uDAAuD;QACvD,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,sBAAsB,EAAE,CAAC;YACnD,OAAO,IAAI,CAAC,CAAC,0DAA0D;QACzE,CAAC;QAED,qCAAqC;QACrC,8CAA8C;QAC9C,yCAAyC;QACzC,4BAA4B;QAC5B,MAAM,cAAc,GAAG,kCAAkC,CAAC;QAE1D,IAAI,CAAC,SAAS,IAAI,SAAS,CAAC,MAAM,KAAK,CAAC,IAAI,SAAS,CAAC,MAAM,GAAG,EAAE,EAAE,CAAC;YAClE,OAAO,KAAK,CAAC;QACf,CAAC;QAED,OAAO,cAAc,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;IACxC,CAAC;IAEO,iBAAiB,CAAC,SAAiB;QACzC,mCAAmC;QACnC,MAAM,OAAO,GAAG,SAAS,CAAC,WAAW,EAAE,CAAC,IAAI,EAAE,CAAC;QAE/C,IAAI,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,EAAE,CAAC;YACnC,OAAO,OAAO,CAAC;QACjB,CAAC;QAED,MAAM,CAAC,IAAI,CAAC,2CAA2C,EAAE;YACvD,QAAQ,EAAE,SAAS;YACnB,OAAO;YACP,OAAO,EAAE,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,sBAAsB;SACvD,CAAC,CAAC;QAEH,OAAO,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,sBAAsB,CAAC;IACvD,CAAC;CACF"}