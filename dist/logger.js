/**
 * Logging configuration for the S3 MCP Server
 */
import winston from 'winston';
import { getConfig } from './config.js';
const config = getConfig();
// Create logs directory if it doesn't exist
import { dirname } from 'path';
import { mkdirSync } from 'fs';
const logDir = dirname(config.logging.filePath);
try {
    mkdirSync(logDir, { recursive: true });
}
catch (error) {
    // Directory might already exist, ignore error
}
// Configure winston logger
const logger = winston.createLogger({
    level: config.logging.level,
    format: winston.format.combine(winston.format.timestamp({
        format: 'YYYY-MM-DD HH:mm:ss'
    }), winston.format.errors({ stack: true }), winston.format.json()),
    defaultMeta: { service: config.mcp.serverName },
    transports: [
        // File transport
        new winston.transports.File({
            filename: config.logging.filePath,
            maxsize: 5242880, // 5MB
            maxFiles: 5,
        }),
    ],
});
// Add console transport if enabled
if (config.logging.enableConsoleLogging) {
    logger.add(new winston.transports.Console({
        format: winston.format.combine(winston.format.colorize(), winston.format.simple())
    }));
}
export default logger;
//# sourceMappingURL=logger.js.map