"use strict";
/**
 * Logging configuration for the S3 MCP Server
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const winston_1 = __importDefault(require("winston"));
const config_js_1 = require("./config.js");
const config = (0, config_js_1.getConfig)();
// Create logs directory if it doesn't exist
const path_1 = require("path");
const fs_1 = require("fs");
const logDir = (0, path_1.dirname)(config.logging.filePath);
try {
    (0, fs_1.mkdirSync)(logDir, { recursive: true });
}
catch (error) {
    // Directory might already exist, ignore error
}
// Configure winston logger
const logger = winston_1.default.createLogger({
    level: config.logging.level,
    format: winston_1.default.format.combine(winston_1.default.format.timestamp({
        format: 'YYYY-MM-DD HH:mm:ss'
    }), winston_1.default.format.errors({ stack: true }), winston_1.default.format.json()),
    defaultMeta: { service: config.mcp.serverName },
    transports: [
        // File transport
        new winston_1.default.transports.File({
            filename: config.logging.filePath,
            maxsize: 5242880, // 5MB
            maxFiles: 5,
        }),
    ],
});
// Add console transport if enabled
if (config.logging.enableConsoleLogging) {
    logger.add(new winston_1.default.transports.Console({
        format: winston_1.default.format.combine(winston_1.default.format.colorize(), winston_1.default.format.simple())
    }));
}
exports.default = logger;
//# sourceMappingURL=logger.js.map