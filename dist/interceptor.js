/**
 * Advanced AI Agent Response Interceptor
 * Provides middleware pipeline for processing AI responses with enhanced capabilities
 */
import { EventEmitter } from 'events';
import { v4 as uuidv4 } from 'uuid';
import { ResponseHandler } from './response-handler.js';
import { getConfig } from './config.js';
import logger from './logger.js';
export class AIResponseInterceptor extends EventEmitter {
    responseHandler;
    _config;
    middleware = [];
    conversations = new Map();
    activeStreams = new Map();
    constructor() {
        super();
        this.responseHandler = new ResponseHandler();
        this._config = getConfig();
        this.setupDefaultMiddleware();
        this.startConversationCleanup();
    }
    /**
     * Add middleware to the processing pipeline
     */
    addMiddleware(middleware) {
        this.middleware.push(middleware);
        this.middleware.sort((a, b) => a.priority - b.priority);
        logger.info('Added middleware to interceptor', {
            name: middleware.name,
            priority: middleware.priority
        });
    }
    /**
     * Intercept a complete AI response
     */
    async interceptResponse(content, context = {}) {
        const fullContext = this.buildContext(context);
        logger.debug('Intercepting AI response', {
            conversationId: fullContext.conversationId,
            contentLength: content.length,
        });
        // Update conversation state
        this.updateConversationState(fullContext, content);
        // Process through response handler
        const response = await this.responseHandler.processResponse(content, {
            ...fullContext.metadata,
            conversationId: fullContext.conversationId,
            sessionId: fullContext.sessionId,
            userId: fullContext.userId,
        });
        // Apply middleware pipeline
        let processedResponse = response;
        for (const middleware of this.middleware) {
            try {
                processedResponse = await middleware.process(processedResponse, fullContext);
            }
            catch (error) {
                logger.error('Middleware processing failed', {
                    middleware: middleware.name,
                    error: error instanceof Error ? error.message : String(error),
                });
            }
        }
        // Emit events for other components
        this.emit('response-intercepted', processedResponse, fullContext);
        return processedResponse;
    }
    /**
     * Intercept streaming AI response chunks
     */
    async interceptStreamChunk(chunk, streamId, isComplete = false, context = {}) {
        const fullContext = this.buildContext(context);
        if (!this.activeStreams.has(streamId)) {
            this.activeStreams.set(streamId, []);
        }
        const chunks = this.activeStreams.get(streamId);
        const responseChunk = {
            id: uuidv4(),
            content: chunk,
            isComplete,
            chunkIndex: chunks.length,
            timestamp: new Date(),
        };
        chunks.push(responseChunk);
        logger.debug('Intercepted stream chunk', {
            streamId,
            chunkIndex: responseChunk.chunkIndex,
            isComplete,
            contentLength: chunk.length,
        });
        // If stream is complete, process the full response
        if (isComplete) {
            const fullContent = chunks.map(c => c.content).join('');
            responseChunk.totalChunks = chunks.length;
            // Process complete response
            await this.interceptResponse(fullContent, fullContext);
            // Clean up stream
            this.activeStreams.delete(streamId);
            this.emit('stream-completed', streamId, fullContent, fullContext);
        }
        this.emit('stream-chunk', responseChunk, streamId, fullContext);
        return responseChunk;
    }
    /**
     * Get conversation state
     */
    getConversationState(conversationId) {
        return this.conversations.get(conversationId);
    }
    /**
     * Update conversation state with new information
     */
    updateConversationState(context, content) {
        const { conversationId } = context;
        let conversation = this.conversations.get(conversationId);
        if (!conversation) {
            conversation = {
                id: conversationId,
                podNames: [],
                startTime: new Date(),
                lastActivity: new Date(),
                messageCount: 0,
                metadata: {},
            };
            this.conversations.set(conversationId, conversation);
        }
        // Update activity
        conversation.lastActivity = new Date();
        conversation.messageCount++;
        // Extract and update namespace if not already set
        if (!conversation.namespace) {
            const namespaceInfo = this.responseHandler.extractFromContent(content);
            if (namespaceInfo && namespaceInfo.confidence !== 'low') {
                conversation.namespace = namespaceInfo.namespace;
                logger.info('Detected namespace for conversation', {
                    conversationId,
                    namespace: conversation.namespace,
                    source: namespaceInfo.source,
                });
            }
        }
        // Extract and update pod names
        const podNames = this.extractPodNamesFromContent(content);
        for (const podName of podNames) {
            if (!conversation.podNames.includes(podName)) {
                conversation.podNames.push(podName);
            }
        }
        // Update metadata
        conversation.metadata = { ...conversation.metadata, ...context.metadata };
    }
    /**
     * Build complete context from partial context
     */
    buildContext(partial) {
        return {
            conversationId: partial.conversationId || uuidv4(),
            sessionId: partial.sessionId || undefined,
            userId: partial.userId || undefined,
            timestamp: partial.timestamp || new Date(),
            metadata: partial.metadata || {},
        };
    }
    /**
     * Extract pod names from content
     */
    extractPodNamesFromContent(content) {
        const podNames = [];
        const podPatterns = [
            // Standard Kubernetes pod names
            /\b([a-z0-9\-]+)-[a-z0-9]{5,10}-[a-z0-9]{5}\b/gi,
            // Pod names in kubectl output
            /^([a-z0-9\-]+)\s+\d+\/\d+/gm,
            // Pod names in logs and error messages
            /pod[\/\s]+([a-z0-9\-]+)/gi,
            // Pod names in resource references
            /pods\/([a-z0-9\-]+)/gi,
        ];
        for (const pattern of podPatterns) {
            const matches = content.matchAll(pattern);
            for (const match of matches) {
                const podName = match[1];
                if (podName && !podNames.includes(podName)) {
                    podNames.push(podName);
                }
            }
        }
        return podNames;
    }
    /**
     * Setup default middleware
     */
    setupDefaultMiddleware() {
        // Namespace enrichment middleware
        this.addMiddleware({
            name: 'namespace-enrichment',
            priority: 10,
            process: async (response, context) => {
                const conversation = this.conversations.get(context.conversationId);
                if (conversation?.namespace && response.namespace.confidence === 'low') {
                    // Use conversation namespace if response detection failed
                    response.namespace = {
                        namespace: conversation.namespace,
                        confidence: 'medium',
                        source: 'context',
                        extractedFrom: 'conversation-state',
                    };
                }
                return response;
            },
        });
        // Pod name enrichment middleware
        this.addMiddleware({
            name: 'pod-enrichment',
            priority: 20,
            process: async (response, context) => {
                const conversation = this.conversations.get(context.conversationId);
                if (conversation?.podNames.length) {
                    // Merge conversation pod names with response pod names
                    const allPodNames = [...(response.podNames || []), ...conversation.podNames];
                    response.podNames = [...new Set(allPodNames)]; // Remove duplicates
                }
                return response;
            },
        });
        // Content filtering middleware
        this.addMiddleware({
            name: 'content-filter',
            priority: 30,
            process: async (response, _context) => {
                // Filter out responses that are too short or don't contain useful information
                if (response.content.length < 50) {
                    logger.debug('Skipping short response', {
                        responseId: response.id,
                        length: response.content.length
                    });
                    response.metadata = { ...response.metadata, filtered: true, reason: 'too-short' };
                }
                return response;
            },
        });
    }
    /**
     * Start conversation cleanup process
     */
    startConversationCleanup() {
        const cleanupInterval = 60 * 60 * 1000; // 1 hour
        const maxAge = 24 * 60 * 60 * 1000; // 24 hours
        setInterval(() => {
            const now = new Date();
            const toDelete = [];
            for (const [id, conversation] of this.conversations) {
                const age = now.getTime() - conversation.lastActivity.getTime();
                if (age > maxAge) {
                    toDelete.push(id);
                }
            }
            for (const id of toDelete) {
                this.conversations.delete(id);
                logger.debug('Cleaned up old conversation', { conversationId: id });
            }
            if (toDelete.length > 0) {
                logger.info('Conversation cleanup completed', {
                    cleaned: toDelete.length,
                    remaining: this.conversations.size
                });
            }
        }, cleanupInterval);
    }
    /**
     * Get statistics about interceptor usage
     */
    getStats() {
        return {
            activeConversations: this.conversations.size,
            activeStreams: this.activeStreams.size,
            middlewareCount: this.middleware.length,
            totalInterceptions: Array.from(this.conversations.values())
                .reduce((sum, conv) => sum + conv.messageCount, 0),
        };
    }
}
//# sourceMappingURL=interceptor.js.map