/**
 * Kubernetes-specific middleware for AI response processing
 */
import { AIResponse } from '../types.js';
import { InterceptorContext, InterceptorMiddleware } from '../interceptor.js';
/**
 * Middleware to detect and extract Kubernetes resource information
 */
export declare class KubernetesResourceMiddleware implements InterceptorMiddleware {
    name: string;
    priority: number;
    process(response: AIResponse, _context: InterceptorContext): Promise<AIResponse>;
    private extractKubernetesResources;
    private isValidKubernetesResource;
}
/**
 * Middleware to detect error patterns in Kubernetes logs
 */
export declare class KubernetesErrorMiddleware implements InterceptorMiddleware {
    name: string;
    priority: number;
    process(response: AIResponse, _context: InterceptorContext): Promise<AIResponse>;
    private extractErrorPatterns;
}
/**
 * Middleware to extract performance metrics from Kubernetes content
 */
export declare class KubernetesMetricsMiddleware implements InterceptorMiddleware {
    name: string;
    priority: number;
    process(response: AIResponse, _context: InterceptorContext): Promise<AIResponse>;
    private extractMetrics;
}
//# sourceMappingURL=kubernetes-middleware.d.ts.map