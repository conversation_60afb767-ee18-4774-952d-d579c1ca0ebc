{"version": 3, "file": "interceptor.js", "sourceRoot": "", "sources": ["../src/interceptor.ts"], "names": [], "mappings": "AAAA;;;GAGG;AAEH,OAAO,EAAE,YAAY,EAAE,MAAM,QAAQ,CAAC;AACtC,OAAO,EAAE,EAAE,IAAI,MAAM,EAAE,MAAM,MAAM,CAAC;AAEpC,OAAO,EAAE,eAAe,EAAE,MAAM,uBAAuB,CAAC;AAExD,OAAO,MAAM,MAAM,aAAa,CAAC;AAoCjC,MAAM,OAAO,qBAAsB,SAAQ,YAAY;IAC7C,eAAe,CAAkB;IACjC,UAAU,GAA4B,EAAE,CAAC;IACzC,aAAa,GAAmC,IAAI,GAAG,EAAE,CAAC;IAC1D,aAAa,GAAiC,IAAI,GAAG,EAAE,CAAC;IAEhE;QACE,KAAK,EAAE,CAAC;QACR,IAAI,CAAC,eAAe,GAAG,IAAI,eAAe,EAAE,CAAC;QAC7C,IAAI,CAAC,sBAAsB,EAAE,CAAC;QAC9B,IAAI,CAAC,wBAAwB,EAAE,CAAC;IAClC,CAAC;IAED;;OAEG;IACH,aAAa,CAAC,UAAiC;QAC7C,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QACjC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,QAAQ,GAAG,CAAC,CAAC,QAAQ,CAAC,CAAC;QACxD,MAAM,CAAC,IAAI,CAAC,iCAAiC,EAAE;YAC7C,IAAI,EAAE,UAAU,CAAC,IAAI;YACrB,QAAQ,EAAE,UAAU,CAAC,QAAQ;SAC9B,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,iBAAiB,CACrB,OAAe,EACf,UAAuC,EAAE;QAEzC,MAAM,WAAW,GAAG,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC;QAE/C,MAAM,CAAC,KAAK,CAAC,0BAA0B,EAAE;YACvC,cAAc,EAAE,WAAW,CAAC,cAAc;YAC1C,aAAa,EAAE,OAAO,CAAC,MAAM;SAC9B,CAAC,CAAC;QAEH,4BAA4B;QAC5B,IAAI,CAAC,uBAAuB,CAAC,WAAW,EAAE,OAAO,CAAC,CAAC;QAEnD,mCAAmC;QACnC,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,eAAe,CAAC,OAAO,EAAE;YACnE,GAAG,WAAW,CAAC,QAAQ;YACvB,cAAc,EAAE,WAAW,CAAC,cAAc;YAC1C,SAAS,EAAE,WAAW,CAAC,SAAS;YAChC,MAAM,EAAE,WAAW,CAAC,MAAM;SAC3B,CAAC,CAAC;QAEH,4BAA4B;QAC5B,IAAI,iBAAiB,GAAG,QAAQ,CAAC;QACjC,KAAK,MAAM,UAAU,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC;YACzC,IAAI,CAAC;gBACH,iBAAiB,GAAG,MAAM,UAAU,CAAC,OAAO,CAAC,iBAAiB,EAAE,WAAW,CAAC,CAAC;YAC/E,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,MAAM,CAAC,KAAK,CAAC,8BAA8B,EAAE;oBAC3C,UAAU,EAAE,UAAU,CAAC,IAAI;oBAC3B,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC;iBAC9D,CAAC,CAAC;YACL,CAAC;QACH,CAAC;QAED,mCAAmC;QACnC,IAAI,CAAC,IAAI,CAAC,sBAAsB,EAAE,iBAAiB,EAAE,WAAW,CAAC,CAAC;QAElE,OAAO,iBAAiB,CAAC;IAC3B,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,oBAAoB,CACxB,KAAa,EACb,QAAgB,EAChB,aAAsB,KAAK,EAC3B,UAAuC,EAAE;QAEzC,MAAM,WAAW,GAAG,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC;QAE/C,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC;YACtC,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,QAAQ,EAAE,EAAE,CAAC,CAAC;QACvC,CAAC;QAED,MAAM,MAAM,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,QAAQ,CAAE,CAAC;QACjD,MAAM,aAAa,GAAkB;YACnC,EAAE,EAAE,MAAM,EAAE;YACZ,OAAO,EAAE,KAAK;YACd,UAAU;YACV,UAAU,EAAE,MAAM,CAAC,MAAM;YACzB,SAAS,EAAE,IAAI,IAAI,EAAE;SACtB,CAAC;QAEF,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;QAE3B,MAAM,CAAC,KAAK,CAAC,0BAA0B,EAAE;YACvC,QAAQ;YACR,UAAU,EAAE,aAAa,CAAC,UAAU;YACpC,UAAU;YACV,aAAa,EAAE,KAAK,CAAC,MAAM;SAC5B,CAAC,CAAC;QAEH,mDAAmD;QACnD,IAAI,UAAU,EAAE,CAAC;YACf,MAAM,WAAW,GAAG,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YACxD,aAAa,CAAC,WAAW,GAAG,MAAM,CAAC,MAAM,CAAC;YAE1C,4BAA4B;YAC5B,MAAM,IAAI,CAAC,iBAAiB,CAAC,WAAW,EAAE,WAAW,CAAC,CAAC;YAEvD,kBAAkB;YAClB,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;YAEpC,IAAI,CAAC,IAAI,CAAC,kBAAkB,EAAE,QAAQ,EAAE,WAAW,EAAE,WAAW,CAAC,CAAC;QACpE,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE,aAAa,EAAE,QAAQ,EAAE,WAAW,CAAC,CAAC;QAChE,OAAO,aAAa,CAAC;IACvB,CAAC;IAED;;OAEG;IACH,oBAAoB,CAAC,cAAsB;QACzC,OAAO,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,cAAc,CAAC,CAAC;IAChD,CAAC;IAED;;OAEG;IACK,uBAAuB,CAAC,OAA2B,EAAE,OAAe;QAC1E,MAAM,EAAE,cAAc,EAAE,GAAG,OAAO,CAAC;QAEnC,IAAI,YAAY,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,cAAc,CAAC,CAAC;QAC1D,IAAI,CAAC,YAAY,EAAE,CAAC;YAClB,YAAY,GAAG;gBACb,EAAE,EAAE,cAAc;gBAClB,QAAQ,EAAE,EAAE;gBACZ,SAAS,EAAE,IAAI,IAAI,EAAE;gBACrB,YAAY,EAAE,IAAI,IAAI,EAAE;gBACxB,YAAY,EAAE,CAAC;gBACf,QAAQ,EAAE,EAAE;aACb,CAAC;YACF,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,cAAc,EAAE,YAAY,CAAC,CAAC;QACvD,CAAC;QAED,kBAAkB;QAClB,YAAY,CAAC,YAAY,GAAG,IAAI,IAAI,EAAE,CAAC;QACvC,YAAY,CAAC,YAAY,EAAE,CAAC;QAE5B,kDAAkD;QAClD,IAAI,CAAC,YAAY,CAAC,SAAS,EAAE,CAAC;YAC5B,MAAM,aAAa,GAAG,IAAI,CAAC,eAAe,CAAC,kBAAkB,CAAC,OAAO,CAAC,CAAC;YACvE,IAAI,aAAa,IAAI,aAAa,CAAC,UAAU,KAAK,KAAK,EAAE,CAAC;gBACxD,YAAY,CAAC,SAAS,GAAG,aAAa,CAAC,SAAS,CAAC;gBACjD,MAAM,CAAC,IAAI,CAAC,qCAAqC,EAAE;oBACjD,cAAc;oBACd,SAAS,EAAE,YAAY,CAAC,SAAS;oBACjC,MAAM,EAAE,aAAa,CAAC,MAAM;iBAC7B,CAAC,CAAC;YACL,CAAC;QACH,CAAC;QAED,+BAA+B;QAC/B,MAAM,QAAQ,GAAG,IAAI,CAAC,0BAA0B,CAAC,OAAO,CAAC,CAAC;QAC1D,KAAK,MAAM,OAAO,IAAI,QAAQ,EAAE,CAAC;YAC/B,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE,CAAC;gBAC7C,YAAY,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YACtC,CAAC;QACH,CAAC;QAED,kBAAkB;QAClB,YAAY,CAAC,QAAQ,GAAG,EAAE,GAAG,YAAY,CAAC,QAAQ,EAAE,GAAG,OAAO,CAAC,QAAQ,EAAE,CAAC;IAC5E,CAAC;IAED;;OAEG;IACK,YAAY,CAAC,OAAoC;QACvD,OAAO;YACL,cAAc,EAAE,OAAO,CAAC,cAAc,IAAI,MAAM,EAAE;YAClD,SAAS,EAAE,OAAO,CAAC,SAAS,IAAI,SAAS;YACzC,MAAM,EAAE,OAAO,CAAC,MAAM,IAAI,SAAS;YACnC,SAAS,EAAE,OAAO,CAAC,SAAS,IAAI,IAAI,IAAI,EAAE;YAC1C,QAAQ,EAAE,OAAO,CAAC,QAAQ,IAAI,EAAE;SACjC,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,0BAA0B,CAAC,OAAe;QAChD,MAAM,QAAQ,GAAa,EAAE,CAAC;QAC9B,MAAM,WAAW,GAAG;YAClB,gCAAgC;YAChC,gDAAgD;YAChD,8BAA8B;YAC9B,6BAA6B;YAC7B,uCAAuC;YACvC,2BAA2B;YAC3B,mCAAmC;YACnC,uBAAuB;SACxB,CAAC;QAEF,KAAK,MAAM,OAAO,IAAI,WAAW,EAAE,CAAC;YAClC,MAAM,OAAO,GAAG,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;YAC1C,KAAK,MAAM,KAAK,IAAI,OAAO,EAAE,CAAC;gBAC5B,MAAM,OAAO,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;gBACzB,IAAI,OAAO,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE,CAAC;oBAC3C,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;gBACzB,CAAC;YACH,CAAC;QACH,CAAC;QAED,OAAO,QAAQ,CAAC;IAClB,CAAC;IAED;;OAEG;IACK,sBAAsB;QAC5B,kCAAkC;QAClC,IAAI,CAAC,aAAa,CAAC;YACjB,IAAI,EAAE,sBAAsB;YAC5B,QAAQ,EAAE,EAAE;YACZ,OAAO,EAAE,KAAK,EAAE,QAAoB,EAAE,OAA2B,EAAuB,EAAE;gBACxF,MAAM,YAAY,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC;gBACpE,IAAI,YAAY,EAAE,SAAS,IAAI,QAAQ,CAAC,SAAS,CAAC,UAAU,KAAK,KAAK,EAAE,CAAC;oBACvE,0DAA0D;oBAC1D,QAAQ,CAAC,SAAS,GAAG;wBACnB,SAAS,EAAE,YAAY,CAAC,SAAS;wBACjC,UAAU,EAAE,QAAQ;wBACpB,MAAM,EAAE,SAAS;wBACjB,aAAa,EAAE,oBAAoB;qBACpC,CAAC;gBACJ,CAAC;gBACD,OAAO,QAAQ,CAAC;YAClB,CAAC;SACF,CAAC,CAAC;QAEH,iCAAiC;QACjC,IAAI,CAAC,aAAa,CAAC;YACjB,IAAI,EAAE,gBAAgB;YACtB,QAAQ,EAAE,EAAE;YACZ,OAAO,EAAE,KAAK,EAAE,QAAoB,EAAE,OAA2B,EAAuB,EAAE;gBACxF,MAAM,YAAY,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC;gBACpE,IAAI,YAAY,EAAE,QAAQ,CAAC,MAAM,EAAE,CAAC;oBAClC,uDAAuD;oBACvD,MAAM,WAAW,GAAG,CAAC,GAAG,CAAC,QAAQ,CAAC,QAAQ,IAAI,EAAE,CAAC,EAAE,GAAG,YAAY,CAAC,QAAQ,CAAC,CAAC;oBAC7E,QAAQ,CAAC,QAAQ,GAAG,CAAC,GAAG,IAAI,GAAG,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,oBAAoB;gBACrE,CAAC;gBACD,OAAO,QAAQ,CAAC;YAClB,CAAC;SACF,CAAC,CAAC;QAEH,+BAA+B;QAC/B,IAAI,CAAC,aAAa,CAAC;YACjB,IAAI,EAAE,gBAAgB;YACtB,QAAQ,EAAE,EAAE;YACZ,OAAO,EAAE,KAAK,EAAE,QAAoB,EAAE,QAA4B,EAAuB,EAAE;gBACzF,8EAA8E;gBAC9E,IAAI,QAAQ,CAAC,OAAO,CAAC,MAAM,GAAG,EAAE,EAAE,CAAC;oBACjC,MAAM,CAAC,KAAK,CAAC,yBAAyB,EAAE;wBACtC,UAAU,EAAE,QAAQ,CAAC,EAAE;wBACvB,MAAM,EAAE,QAAQ,CAAC,OAAO,CAAC,MAAM;qBAChC,CAAC,CAAC;oBACH,QAAQ,CAAC,QAAQ,GAAG,EAAE,GAAG,QAAQ,CAAC,QAAQ,EAAE,QAAQ,EAAE,IAAI,EAAE,MAAM,EAAE,WAAW,EAAE,CAAC;gBACpF,CAAC;gBACD,OAAO,QAAQ,CAAC;YAClB,CAAC;SACF,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACK,wBAAwB;QAC9B,MAAM,eAAe,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC,SAAS;QACjD,MAAM,MAAM,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC,WAAW;QAE/C,WAAW,CAAC,GAAG,EAAE;YACf,MAAM,GAAG,GAAG,IAAI,IAAI,EAAE,CAAC;YACvB,MAAM,QAAQ,GAAa,EAAE,CAAC;YAE9B,KAAK,MAAM,CAAC,EAAE,EAAE,YAAY,CAAC,IAAI,IAAI,CAAC,aAAa,EAAE,CAAC;gBACpD,MAAM,GAAG,GAAG,GAAG,CAAC,OAAO,EAAE,GAAG,YAAY,CAAC,YAAY,CAAC,OAAO,EAAE,CAAC;gBAChE,IAAI,GAAG,GAAG,MAAM,EAAE,CAAC;oBACjB,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;gBACpB,CAAC;YACH,CAAC;YAED,KAAK,MAAM,EAAE,IAAI,QAAQ,EAAE,CAAC;gBAC1B,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;gBAC9B,MAAM,CAAC,KAAK,CAAC,6BAA6B,EAAE,EAAE,cAAc,EAAE,EAAE,EAAE,CAAC,CAAC;YACtE,CAAC;YAED,IAAI,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBACxB,MAAM,CAAC,IAAI,CAAC,gCAAgC,EAAE;oBAC5C,OAAO,EAAE,QAAQ,CAAC,MAAM;oBACxB,SAAS,EAAE,IAAI,CAAC,aAAa,CAAC,IAAI;iBACnC,CAAC,CAAC;YACL,CAAC;QACH,CAAC,EAAE,eAAe,CAAC,CAAC;IACtB,CAAC;IAED;;OAEG;IACH,QAAQ;QAMN,OAAO;YACL,mBAAmB,EAAE,IAAI,CAAC,aAAa,CAAC,IAAI;YAC5C,aAAa,EAAE,IAAI,CAAC,aAAa,CAAC,IAAI;YACtC,eAAe,EAAE,IAAI,CAAC,UAAU,CAAC,MAAM;YACvC,kBAAkB,EAAE,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE,CAAC;iBACxD,MAAM,CAAC,CAAC,GAAG,EAAE,IAAI,EAAE,EAAE,CAAC,GAAG,GAAG,IAAI,CAAC,YAAY,EAAE,CAAC,CAAC;SACrD,CAAC;IACJ,CAAC;CACF"}