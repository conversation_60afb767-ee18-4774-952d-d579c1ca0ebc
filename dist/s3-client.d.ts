/**
 * S3 Integration Module
 * Handles uploading AI responses to S3 with Kubernetes namespace-based organization
 */
import { S3UploadResult, AIResponse } from './types.js';
export interface S3UploadOptions {
    contentType?: string;
    metadata?: Record<string, string>;
    tags?: Record<string, string>;
    storageClass?: 'STANDARD' | 'REDUCED_REDUNDANCY' | 'STANDARD_IA' | 'ONEZONE_IA' | 'INTELLIGENT_TIERING' | 'GLACIER' | 'DEEP_ARCHIVE';
    serverSideEncryption?: 'AES256' | 'aws:kms';
    kmsKeyId?: string;
}
export interface S3ClientStats {
    totalUploads: number;
    successfulUploads: number;
    failedUploads: number;
    totalBytesUploaded: number;
    averageUploadTime: number;
    lastUploadTime?: Date;
    lastError?: string;
}
export declare class S3Client {
    private client;
    private config;
    private stats;
    private uploadTimes;
    constructor();
    /**
     * Upload an AI response to S3 with namespace-based organization
     */
    uploadResponse(response: AIResponse, options?: S3UploadOptions): Promise<S3UploadResult>;
    /**
     * Generate S3 key with namespace-based folder organization
     */
    private generateS3Key;
    /**
     * Format the AI response content for storage
     */
    private formatResponseContent;
    /**
     * Generate metadata for S3 object
     */
    private generateMetadata;
    /**
     * Format tags for S3 object tagging
     */
    private formatTags;
    /**
     * Generate S3 URL for the uploaded object
     */
    private generateS3Url;
    /**
     * Update upload statistics
     */
    private updateStats;
    /**
     * Test S3 connection and bucket access
     */
    testConnection(): Promise<{
        success: boolean;
        error?: string;
    }>;
    /**
     * Get upload statistics
     */
    getStats(): S3ClientStats;
    /**
     * Reset statistics
     */
    resetStats(): void;
    /**
     * Batch upload multiple responses
     */
    uploadBatch(responses: AIResponse[], options?: S3UploadOptions): Promise<S3UploadResult[]>;
    /**
     * Clean up old uploads (if lifecycle policies are not configured)
     */
    cleanupOldUploads(olderThanDays?: number): Promise<{
        deleted: number;
        errors: string[];
    }>;
}
//# sourceMappingURL=s3-client.d.ts.map