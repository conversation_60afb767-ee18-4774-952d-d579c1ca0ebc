/**
 * De<PERSON> script showing the flexible namespace handling
 */
import { AIResponseInterceptor } from '../src/interceptor.js';
import { KubernetesResourceMiddleware, KubernetesErrorMiddleware, KubernetesMetricsMiddleware } from '../src/middleware/kubernetes-middleware.js';
async function demonstrateNamespaceFlexibility() {
    console.log('🚀 Starting Namespace Flexibility Demo\n');
    // Create interceptor instance
    const interceptor = new AIResponseInterceptor();
    // Add Kubernetes-specific middleware
    interceptor.addMiddleware(new KubernetesResourceMiddleware());
    interceptor.addMiddleware(new KubernetesErrorMiddleware());
    interceptor.addMiddleware(new KubernetesMetricsMiddleware());
    console.log('🔍 Testing various namespace scenarios...\n');
    // Test 1: Standard production namespace
    console.log('📊 Test 1: Standard Production Namespace');
    const response1 = await interceptor.interceptResponse('kubectl logs -n production web-server-12345-abcde shows errors', {
        conversationId: 'test-conv-1',
        metadata: { requestType: 'pod_log_analysis' },
    });
    console.log(`✅ Detected namespace: ${response1.namespace.namespace} (${response1.namespace.confidence})`);
    console.log('');
    // Test 2: Custom application namespace
    console.log('📊 Test 2: Custom Application Namespace');
    const response2 = await interceptor.interceptResponse('Error in /var/log/pods/my-custom-app-namespace_api-service-67890_uuid/container.log', {
        conversationId: 'test-conv-2',
        metadata: { requestType: 'log_analysis' },
    });
    console.log(`✅ Detected namespace: ${response2.namespace.namespace} (${response2.namespace.confidence})`);
    console.log('');
    // Test 3: Team-specific namespace
    console.log('📊 Test 3: Team-Specific Namespace');
    const response3 = await interceptor.interceptResponse('kubectl get pods -n team-alpha-backend shows CrashLoopBackOff', {
        conversationId: 'test-conv-3',
        metadata: { requestType: 'troubleshooting' },
    });
    console.log(`✅ Detected namespace: ${response3.namespace.namespace} (${response3.namespace.confidence})`);
    console.log('');
    // Test 4: Environment-specific namespace
    console.log('📊 Test 4: Environment-Specific Namespace');
    const response4 = await interceptor.interceptResponse('Pod feature-branch-testing-env-worker-123 is experiencing memory issues', {
        conversationId: 'test-conv-4',
        metadata: { requestType: 'performance_analysis' },
    });
    console.log(`✅ Detected namespace: ${response4.namespace.namespace} (${response4.namespace.confidence})`);
    console.log('');
    // Test 5: System namespace
    console.log('📊 Test 5: System Namespace');
    const response5 = await interceptor.interceptResponse('kubectl logs -n kube-system coredns-558bd4d5db-xyz shows DNS resolution failures', {
        conversationId: 'test-conv-5',
        metadata: { requestType: 'system_troubleshooting' },
    });
    console.log(`✅ Detected namespace: ${response5.namespace.namespace} (${response5.namespace.confidence})`);
    console.log('');
    // Test 6: Monitoring namespace
    console.log('📊 Test 6: Monitoring Namespace');
    const response6 = await interceptor.interceptResponse('Prometheus in namespace observability-monitoring is scraping metrics', {
        conversationId: 'test-conv-6',
        metadata: { requestType: 'monitoring_analysis' },
    });
    console.log(`✅ Detected namespace: ${response6.namespace.namespace} (${response6.namespace.confidence})`);
    console.log('');
    // Test 7: Development namespace with special characters
    console.log('📊 Test 7: Development Namespace with Hyphens');
    const response7 = await interceptor.interceptResponse('kubectl describe pod -n dev-feature-xyz-123 shows ImagePullBackOff', {
        conversationId: 'test-conv-7',
        metadata: { requestType: 'deployment_troubleshooting' },
    });
    console.log(`✅ Detected namespace: ${response7.namespace.namespace} (${response7.namespace.confidence})`);
    console.log('');
    // Test 8: Customer-specific namespace
    console.log('📊 Test 8: Customer-Specific Namespace');
    const response8 = await interceptor.interceptResponse('Error in customer-acme-corp tenant showing database connection issues', {
        conversationId: 'test-conv-8',
        metadata: { requestType: 'customer_support' },
    });
    console.log(`✅ Detected namespace: ${response8.namespace.namespace} (${response8.namespace.confidence})`);
    console.log('');
    // Test 9: No namespace detected (fallback)
    console.log('📊 Test 9: No Namespace Detected (Fallback)');
    const response9 = await interceptor.interceptResponse('General Kubernetes cluster analysis without specific namespace context', {
        conversationId: 'test-conv-9',
        metadata: { requestType: 'general_analysis' },
    });
    console.log(`✅ Detected namespace: ${response9.namespace.namespace} (${response9.namespace.confidence})`);
    console.log('');
    // Summary
    console.log('📊 Summary of Detected Namespaces:');
    const responses = [response1, response2, response3, response4, response5, response6, response7, response8, response9];
    responses.forEach((response, index) => {
        console.log(`  ${index + 1}. ${response.namespace.namespace} (${response.namespace.source}, ${response.namespace.confidence})`);
    });
    console.log('');
    console.log('✅ Demo completed successfully!');
    console.log('');
    console.log('🎯 Key Features Demonstrated:');
    console.log('- ✅ Accepts ANY valid Kubernetes namespace');
    console.log('- ✅ No restrictions on namespace names');
    console.log('- ✅ Supports production, development, team, and custom namespaces');
    console.log('- ✅ Handles system namespaces (kube-system, monitoring, etc.)');
    console.log('- ✅ Works with customer-specific and feature-branch namespaces');
    console.log('- ✅ Graceful fallback when no namespace is detected');
    console.log('- ✅ Maintains confidence scoring for namespace detection');
    console.log('- ✅ Perfect for debugging across dozens of different namespaces');
    console.log('');
    console.log('🔧 Configuration:');
    console.log('- Format validation can be enabled/disabled via ENABLE_NAMESPACE_FORMAT_VALIDATION');
    console.log('- No allowlist restrictions - works with any namespace');
    console.log('- Fallback namespace configurable via DEFAULT_NAMESPACE_PREFIX');
}
// Run the demo
demonstrateNamespaceFlexibility().catch(console.error);
//# sourceMappingURL=namespace-flexibility-demo.js.map