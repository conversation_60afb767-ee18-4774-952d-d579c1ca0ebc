{"version": 3, "file": "s3-demo.js", "sourceRoot": "", "sources": ["../../examples/s3-demo.ts"], "names": [], "mappings": "AAAA;;GAEG;AAEH,OAAO,EAAE,QAAQ,EAAE,MAAM,qBAAqB,CAAC;AAG/C,KAAK,UAAU,mBAAmB;IAChC,OAAO,CAAC,GAAG,CAAC,8BAA8B,CAAC,CAAC;IAE5C,4BAA4B;IAC5B,MAAM,QAAQ,GAAG,IAAI,QAAQ,EAAE,CAAC;IAEhC,OAAO,CAAC,GAAG,CAAC,mBAAmB,EAAE,QAAQ,CAAC,QAAQ,EAAE,CAAC,CAAC;IACtD,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;IAEhB,yFAAyF;IACzF,OAAO,CAAC,GAAG,CAAC,6BAA6B,CAAC,CAAC;IAC3C,MAAM,cAAc,GAAG,MAAM,QAAQ,CAAC,cAAc,EAAE,CAAC;IACvD,OAAO,CAAC,GAAG,CAAC,yBAAyB,EAAE,cAAc,CAAC,CAAC;IACvD,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;IAEhB,gCAAgC;IAChC,OAAO,CAAC,GAAG,CAAC,kCAAkC,CAAC,CAAC;IAChD,MAAM,YAAY,GAAe;QAC/B,EAAE,EAAE,sCAAsC;QAC1C,SAAS,EAAE,IAAI,IAAI,CAAC,sBAAsB,CAAC;QAC3C,OAAO,EAAE,uJAAuJ;QAChK,SAAS,EAAE;YACT,SAAS,EAAE,YAAY;YACvB,UAAU,EAAE,MAAM;YAClB,MAAM,EAAE,WAAW;YACnB,aAAa,EAAE,mBAAmB;SACnC;QACD,cAAc,EAAE,UAAU;QAC1B,SAAS,EAAE,aAAa;QACxB,MAAM,EAAE,UAAU;QAClB,WAAW,EAAE,kBAAkB;QAC/B,QAAQ,EAAE,CAAC,wBAAwB,EAAE,yBAAyB,CAAC;QAC/D,QAAQ,EAAE;YACR,mBAAmB,EAAE;gBACnB,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,wBAAwB,EAAE;gBAC/C,EAAE,IAAI,EAAE,SAAS,EAAE,IAAI,EAAE,aAAa,EAAE;gBACxC,EAAE,IAAI,EAAE,YAAY,EAAE,IAAI,EAAE,gBAAgB,EAAE;aAC/C;YACD,gBAAgB,EAAE;gBAChB;oBACE,IAAI,EAAE,kBAAkB;oBACxB,OAAO,EAAE,+DAA+D;oBACxE,QAAQ,EAAE,MAAM;iBACjB;gBACD;oBACE,IAAI,EAAE,kBAAkB;oBACxB,OAAO,EAAE,8CAA8C;oBACvD,QAAQ,EAAE,MAAM;iBACjB;aACF;YACD,iBAAiB,EAAE;gBACjB,GAAG,EAAE,CAAC,EAAE,KAAK,EAAE,GAAG,EAAE,IAAI,EAAE,QAAQ,EAAE,EAAE,EAAE,KAAK,EAAE,GAAG,EAAE,IAAI,EAAE,OAAO,EAAE,CAAC;gBACpE,MAAM,EAAE,CAAC,EAAE,KAAK,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC;gBAC9D,WAAW,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;aAC1B;YACD,SAAS,EAAE,IAAI;SAChB;KACF,CAAC;IAEF,IAAI,CAAC;QACH,MAAM,YAAY,GAAG,MAAM,QAAQ,CAAC,cAAc,CAAC,YAAY,CAAC,CAAC;QACjE,OAAO,CAAC,GAAG,CAAC,gBAAgB,EAAE,YAAY,CAAC,CAAC;IAC9C,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,GAAG,CAAC,iDAAiD,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC;IACjH,CAAC;IACD,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;IAEhB,8BAA8B;IAC9B,OAAO,CAAC,GAAG,CAAC,uCAAuC,CAAC,CAAC;IACrD,MAAM,eAAe,GAAe;QAClC,GAAG,YAAY;QACf,EAAE,EAAE,sCAAsC;QAC1C,SAAS,EAAE,IAAI,IAAI,CAAC,sBAAsB,CAAC;QAC3C,OAAO,EAAE,wFAAwF;QACjG,SAAS,EAAE;YACT,SAAS,EAAE,SAAS;YACpB,UAAU,EAAE,QAAQ;YACpB,MAAM,EAAE,UAAU;YAClB,aAAa,EAAE,4DAA4D;SAC5E;QACD,QAAQ,EAAE,CAAC,yBAAyB,CAAC;QACrC,QAAQ,EAAE;YACR,iBAAiB,EAAE;gBACjB,MAAM,EAAE,CAAC,EAAE,KAAK,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC;gBACpC,WAAW,EAAE,CAAC,EAAE,CAAC;aAClB;SACF;KACF,CAAC;IAEF,IAAI,CAAC;QACH,MAAM,YAAY,GAAG,MAAM,QAAQ,CAAC,cAAc,CAAC,eAAe,CAAC,CAAC;QACpE,OAAO,CAAC,GAAG,CAAC,wBAAwB,EAAE,YAAY,CAAC,CAAC;IACtD,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,GAAG,CAAC,mCAAmC,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC;IACnG,CAAC;IACD,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;IAEhB,6BAA6B;IAC7B,OAAO,CAAC,GAAG,CAAC,sCAAsC,CAAC,CAAC;IACpD,MAAM,eAAe,GAAe;QAClC,GAAG,YAAY;QACf,EAAE,EAAE,sCAAsC;QAC1C,SAAS,EAAE,IAAI,IAAI,CAAC,sBAAsB,CAAC;QAC3C,OAAO,EAAE,wEAAwE;QACjF,SAAS,EAAE;YACT,SAAS,EAAE,cAAc;YACzB,UAAU,EAAE,KAAK;YACjB,MAAM,EAAE,SAAS;SAClB;QACD,QAAQ,EAAE,EAAE;QACZ,QAAQ,EAAE,EAAE;KACb,CAAC;IAEF,IAAI,CAAC;QACH,MAAM,YAAY,GAAG,MAAM,QAAQ,CAAC,cAAc,CAAC,eAAe,CAAC,CAAC;QACpE,OAAO,CAAC,GAAG,CAAC,kCAAkC,EAAE,YAAY,CAAC,CAAC;IAChE,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,GAAG,CAAC,6CAA6C,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC;IAC7G,CAAC;IACD,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;IAEhB,uBAAuB;IACvB,OAAO,CAAC,GAAG,CAAC,yBAAyB,CAAC,CAAC;IACvC,MAAM,cAAc,GAAG,CAAC,YAAY,EAAE,eAAe,EAAE,eAAe,CAAC,CAAC;IAExE,IAAI,CAAC;QACH,MAAM,YAAY,GAAG,MAAM,QAAQ,CAAC,WAAW,CAAC,cAAc,CAAC,CAAC;QAChE,OAAO,CAAC,GAAG,CAAC,uBAAuB,CAAC,CAAC;QACrC,YAAY,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;YACrC,OAAO,CAAC,GAAG,CAAC,cAAc,KAAK,GAAG,CAAC,GAAG,EAAE,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,WAAW,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC;QAClG,CAAC,CAAC,CAAC;IACL,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,GAAG,CAAC,iCAAiC,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC;IACjG,CAAC;IACD,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;IAEhB,qBAAqB;IACrB,OAAO,CAAC,GAAG,CAAC,8BAA8B,CAAC,CAAC;IAC5C,MAAM,KAAK,GAAG,QAAQ,CAAC,QAAQ,EAAE,CAAC;IAClC,OAAO,CAAC,GAAG,CAAC,cAAc,EAAE,KAAK,CAAC,CAAC;IACnC,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;IAEhB,kCAAkC;IAClC,OAAO,CAAC,GAAG,CAAC,uCAAuC,CAAC,CAAC;IACrD,OAAO,CAAC,GAAG,CAAC,kCAAkC,CAAC,CAAC;IAChD,OAAO,CAAC,GAAG,CAAC,yFAAyF,CAAC,CAAC;IAEvG,OAAO,CAAC,GAAG,CAAC,+BAA+B,CAAC,CAAC;IAC7C,OAAO,CAAC,GAAG,CAAC,mFAAmF,CAAC,CAAC;IAEjG,OAAO,CAAC,GAAG,CAAC,gCAAgC,CAAC,CAAC;IAC9C,OAAO,CAAC,GAAG,CAAC,6FAA6F,CAAC,CAAC;IAC3G,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;IAEhB,OAAO,CAAC,GAAG,CAAC,gCAAgC,CAAC,CAAC;IAC9C,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;IAChB,OAAO,CAAC,GAAG,CAAC,+BAA+B,CAAC,CAAC;IAC7C,OAAO,CAAC,GAAG,CAAC,4CAA4C,CAAC,CAAC;IAC1D,OAAO,CAAC,GAAG,CAAC,uDAAuD,CAAC,CAAC;IACrE,OAAO,CAAC,GAAG,CAAC,iDAAiD,CAAC,CAAC;IAC/D,OAAO,CAAC,GAAG,CAAC,+CAA+C,CAAC,CAAC;IAC7D,OAAO,CAAC,GAAG,CAAC,qCAAqC,CAAC,CAAC;IACnD,OAAO,CAAC,GAAG,CAAC,oCAAoC,CAAC,CAAC;IAClD,OAAO,CAAC,GAAG,CAAC,+BAA+B,CAAC,CAAC;IAC7C,OAAO,CAAC,GAAG,CAAC,sCAAsC,CAAC,CAAC;IACpD,OAAO,CAAC,GAAG,CAAC,uCAAuC,CAAC,CAAC;AACvD,CAAC;AAED,eAAe;AACf,mBAAmB,EAAE,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC"}