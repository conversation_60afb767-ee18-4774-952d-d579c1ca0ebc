{"version": 3, "file": "interceptor-demo.js", "sourceRoot": "", "sources": ["../../examples/interceptor-demo.ts"], "names": [], "mappings": "AAAA;;GAEG;AAEH,OAAO,EAAE,qBAAqB,EAAE,MAAM,uBAAuB,CAAC;AAC9D,OAAO,EACL,4BAA4B,EAC5B,yBAAyB,EACzB,2BAA2B,EAC5B,MAAM,4CAA4C,CAAC;AAEpD,KAAK,UAAU,sBAAsB;IACnC,OAAO,CAAC,GAAG,CAAC,4CAA4C,CAAC,CAAC;IAE1D,8BAA8B;IAC9B,MAAM,WAAW,GAAG,IAAI,qBAAqB,EAAE,CAAC;IAEhD,qCAAqC;IACrC,WAAW,CAAC,aAAa,CAAC,IAAI,4BAA4B,EAAE,CAAC,CAAC;IAC9D,WAAW,CAAC,aAAa,CAAC,IAAI,yBAAyB,EAAE,CAAC,CAAC;IAC3D,WAAW,CAAC,aAAa,CAAC,IAAI,2BAA2B,EAAE,CAAC,CAAC;IAE7D,OAAO,CAAC,GAAG,CAAC,mBAAmB,EAAE,WAAW,CAAC,QAAQ,EAAE,CAAC,CAAC;IACzD,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;IAEhB,yDAAyD;IACzD,OAAO,CAAC,GAAG,CAAC,qDAAqD,CAAC,CAAC;IACnE,MAAM,SAAS,GAAG,MAAM,WAAW,CAAC,iBAAiB,CACnD,gFAAgF,EAChF;QACE,cAAc,EAAE,aAAa;QAC7B,QAAQ,EAAE;YACR,WAAW,EAAE,kBAAkB;SAChC;KACF,CACF,CAAC;IAEF,OAAO,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC;IAC3B,OAAO,CAAC,GAAG,CAAC,OAAO,EAAE,SAAS,CAAC,EAAE,CAAC,CAAC;IACnC,OAAO,CAAC,GAAG,CAAC,cAAc,EAAE,SAAS,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;IAC3D,OAAO,CAAC,GAAG,CAAC,WAAW,EAAE,SAAS,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;IACrD,OAAO,CAAC,GAAG,CAAC,eAAe,EAAE,SAAS,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC;IAC7D,OAAO,CAAC,GAAG,CAAC,cAAc,EAAE,SAAS,CAAC,QAAQ,CAAC,CAAC;IAChD,OAAO,CAAC,GAAG,CAAC,yBAAyB,EAAE,SAAS,CAAC,QAAQ,EAAE,CAAC,qBAAqB,CAAC,CAAC,CAAC;IACpF,OAAO,CAAC,GAAG,CAAC,sBAAsB,EAAE,SAAS,CAAC,QAAQ,EAAE,CAAC,kBAAkB,CAAC,CAAC,CAAC;IAC9E,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;IAEhB,oDAAoD;IACpD,OAAO,CAAC,GAAG,CAAC,4CAA4C,CAAC,CAAC;IAC1D,MAAM,SAAS,GAAG,MAAM,WAAW,CAAC,iBAAiB,CACnD,4HAA4H,EAC5H;QACE,cAAc,EAAE,aAAa;QAC7B,QAAQ,EAAE;YACR,WAAW,EAAE,mBAAmB;SACjC;KACF,CACF,CAAC;IAEF,OAAO,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC;IAC3B,OAAO,CAAC,GAAG,CAAC,OAAO,EAAE,SAAS,CAAC,EAAE,CAAC,CAAC;IACnC,OAAO,CAAC,GAAG,CAAC,cAAc,EAAE,SAAS,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;IAC3D,OAAO,CAAC,GAAG,CAAC,WAAW,EAAE,SAAS,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;IACrD,OAAO,CAAC,GAAG,CAAC,cAAc,EAAE,SAAS,CAAC,QAAQ,CAAC,CAAC;IAChD,OAAO,CAAC,GAAG,CAAC,sBAAsB,EAAE,SAAS,CAAC,QAAQ,EAAE,CAAC,kBAAkB,CAAC,CAAC,CAAC;IAC9E,OAAO,CAAC,GAAG,CAAC,uBAAuB,EAAE,SAAS,CAAC,QAAQ,EAAE,CAAC,mBAAmB,CAAC,CAAC,CAAC;IAChF,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;IAEhB,6BAA6B;IAC7B,OAAO,CAAC,GAAG,CAAC,0CAA0C,CAAC,CAAC;IACxD,MAAM,QAAQ,GAAG,eAAe,CAAC;IAEjC,MAAM,WAAW,CAAC,oBAAoB,CACpC,iDAAiD,EACjD,QAAQ,EACR,KAAK,EACL,EAAE,cAAc,EAAE,aAAa,EAAE,CAClC,CAAC;IAEF,MAAM,WAAW,CAAC,oBAAoB,CACpC,wDAAwD,EACxD,QAAQ,EACR,KAAK,EACL,EAAE,cAAc,EAAE,aAAa,EAAE,CAClC,CAAC;IAEF,MAAM,UAAU,GAAG,MAAM,WAAW,CAAC,oBAAoB,CACvD,0DAA0D,EAC1D,QAAQ,EACR,IAAI,EAAE,mBAAmB;IACzB,EAAE,cAAc,EAAE,aAAa,EAAE,CAClC,CAAC;IAEF,OAAO,CAAC,GAAG,CAAC,cAAc,CAAC,CAAC;IAC5B,OAAO,CAAC,GAAG,CAAC,OAAO,EAAE,UAAU,CAAC,EAAE,CAAC,CAAC;IACpC,OAAO,CAAC,GAAG,CAAC,iBAAiB,EAAE,UAAU,CAAC,WAAW,CAAC,CAAC;IACvD,OAAO,CAAC,GAAG,CAAC,gBAAgB,EAAE,UAAU,CAAC,UAAU,CAAC,CAAC;IACrD,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;IAEhB,sCAAsC;IACtC,OAAO,CAAC,GAAG,CAAC,wCAAwC,CAAC,CAAC;IACtD,MAAM,UAAU,GAAG,WAAW,CAAC,oBAAoB,CAAC,aAAa,CAAC,CAAC;IACnE,MAAM,UAAU,GAAG,WAAW,CAAC,oBAAoB,CAAC,aAAa,CAAC,CAAC;IAEnE,OAAO,CAAC,GAAG,CAAC,uBAAuB,CAAC,CAAC;IACrC,OAAO,CAAC,GAAG,CAAC,OAAO,EAAE,UAAU,EAAE,EAAE,CAAC,CAAC;IACrC,OAAO,CAAC,GAAG,CAAC,cAAc,EAAE,UAAU,EAAE,SAAS,CAAC,CAAC;IACnD,OAAO,CAAC,GAAG,CAAC,cAAc,EAAE,UAAU,EAAE,QAAQ,CAAC,CAAC;IAClD,OAAO,CAAC,GAAG,CAAC,kBAAkB,EAAE,UAAU,EAAE,YAAY,CAAC,CAAC;IAC1D,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;IAEhB,OAAO,CAAC,GAAG,CAAC,uBAAuB,CAAC,CAAC;IACrC,OAAO,CAAC,GAAG,CAAC,OAAO,EAAE,UAAU,EAAE,EAAE,CAAC,CAAC;IACrC,OAAO,CAAC,GAAG,CAAC,cAAc,EAAE,UAAU,EAAE,SAAS,CAAC,CAAC;IACnD,OAAO,CAAC,GAAG,CAAC,cAAc,EAAE,UAAU,EAAE,QAAQ,CAAC,CAAC;IAClD,OAAO,CAAC,GAAG,CAAC,kBAAkB,EAAE,UAAU,EAAE,YAAY,CAAC,CAAC;IAC1D,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;IAEhB,cAAc;IACd,OAAO,CAAC,GAAG,CAAC,iBAAiB,EAAE,WAAW,CAAC,QAAQ,EAAE,CAAC,CAAC;IACvD,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;IAEhB,OAAO,CAAC,GAAG,CAAC,gCAAgC,CAAC,CAAC;IAC9C,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;IAChB,OAAO,CAAC,GAAG,CAAC,+BAA+B,CAAC,CAAC;IAC7C,OAAO,CAAC,GAAG,CAAC,2DAA2D,CAAC,CAAC;IACzE,OAAO,CAAC,GAAG,CAAC,uDAAuD,CAAC,CAAC;IACrE,OAAO,CAAC,GAAG,CAAC,oCAAoC,CAAC,CAAC;IAClD,OAAO,CAAC,GAAG,CAAC,gCAAgC,CAAC,CAAC;IAC9C,OAAO,CAAC,GAAG,CAAC,mCAAmC,CAAC,CAAC;IACjD,OAAO,CAAC,GAAG,CAAC,oCAAoC,CAAC,CAAC;IAClD,OAAO,CAAC,GAAG,CAAC,sCAAsC,CAAC,CAAC;AACtD,CAAC;AAED,eAAe;AACf,sBAAsB,EAAE,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC"}