/**
 * Demo script showing the S3 Client functionality
 */
import { S3Client } from '../src/s3-client.js';
async function demonstrateS3Client() {
    console.log('🚀 Starting S3 Client Demo\n');
    // Create S3 client instance
    const s3Client = new S3Client();
    console.log('📊 Initial Stats:', s3Client.getStats());
    console.log('');
    // Test S3 connection (this will fail with test credentials, but shows the functionality)
    console.log('🔍 Testing S3 Connection...');
    const connectionTest = await s3Client.testConnection();
    console.log('Connection Test Result:', connectionTest);
    console.log('');
    // Demo 1: Basic response upload
    console.log('🔍 Demo 1: Basic Response Upload');
    const mockResponse = {
        id: '123e4567-e89b-12d3-a456-426614174000',
        timestamp: new Date('2024-06-24T14:30:22Z'),
        content: 'This is a test AI response about Kubernetes pod logs in production namespace. The pod web-server-12345-abc<PERSON> is experiencing CrashLoopBackOff errors.',
        namespace: {
            namespace: 'production',
            confidence: 'high',
            source: 'parameter',
            extractedFrom: 'context.namespace',
        },
        conversationId: 'conv-123',
        sessionId: 'session-456',
        userId: 'user-789',
        requestType: 'pod_log_analysis',
        podNames: ['web-server-12345-abcde', 'api-gateway-67890-fghij'],
        metadata: {
            kubernetesResources: [
                { kind: 'Pod', name: 'web-server-12345-abcde' },
                { kind: 'Service', name: 'web-service' },
                { kind: 'Deployment', name: 'web-deployment' },
            ],
            kubernetesErrors: [
                {
                    type: 'CrashLoopBackOff',
                    message: 'Pod is crashing repeatedly due to application startup failure',
                    severity: 'high'
                },
                {
                    type: 'ImagePullBackOff',
                    message: 'Failed to pull container image from registry',
                    severity: 'high'
                },
            ],
            kubernetesMetrics: {
                cpu: [{ value: 250, unit: 'mcores' }, { value: 1.5, unit: 'cores' }],
                memory: [{ value: 512, unit: 'Mi' }, { value: 1, unit: 'Gi' }],
                percentages: [85, 92, 78],
            },
            hasErrors: true,
        },
    };
    try {
        const uploadResult = await s3Client.uploadResponse(mockResponse);
        console.log('Upload Result:', uploadResult);
    }
    catch (error) {
        console.log('Upload failed (expected with test credentials):', error instanceof Error ? error.message : error);
    }
    console.log('');
    // Demo 2: Different namespace
    console.log('🔍 Demo 2: Different Namespace Upload');
    const stagingResponse = {
        ...mockResponse,
        id: 'abc12345-def6-7890-ghij-klmnopqrstuv',
        timestamp: new Date('2024-06-24T15:45:30Z'),
        content: 'Staging environment analysis shows memory usage at 95% for pod api-service-98765-zyxwv',
        namespace: {
            namespace: 'staging',
            confidence: 'medium',
            source: 'log-path',
            extractedFrom: '/var/log/pods/staging_api-service-98765_uuid/container.log',
        },
        podNames: ['api-service-98765-zyxwv'],
        metadata: {
            kubernetesMetrics: {
                memory: [{ value: 1.9, unit: 'Gi' }],
                percentages: [95],
            },
        },
    };
    try {
        const uploadResult = await s3Client.uploadResponse(stagingResponse);
        console.log('Staging Upload Result:', uploadResult);
    }
    catch (error) {
        console.log('Staging upload failed (expected):', error instanceof Error ? error.message : error);
    }
    console.log('');
    // Demo 3: Fallback namespace
    console.log('🔍 Demo 3: Fallback Namespace Upload');
    const unknownResponse = {
        ...mockResponse,
        id: 'xyz98765-abc1-2345-defg-hijklmnopqrs',
        timestamp: new Date('2024-06-24T16:20:15Z'),
        content: 'General Kubernetes cluster analysis without specific namespace context',
        namespace: {
            namespace: 'NO_NAMESPACE',
            confidence: 'low',
            source: 'default',
        },
        podNames: [],
        metadata: {},
    };
    try {
        const uploadResult = await s3Client.uploadResponse(unknownResponse);
        console.log('Unknown Namespace Upload Result:', uploadResult);
    }
    catch (error) {
        console.log('Unknown namespace upload failed (expected):', error instanceof Error ? error.message : error);
    }
    console.log('');
    // Demo 4: Batch upload
    console.log('🔍 Demo 4: Batch Upload');
    const batchResponses = [mockResponse, stagingResponse, unknownResponse];
    try {
        const batchResults = await s3Client.uploadBatch(batchResponses);
        console.log('Batch Upload Results:');
        batchResults.forEach((result, index) => {
            console.log(`  Response ${index + 1}:`, result.success ? 'Success' : `Failed: ${result.error}`);
        });
    }
    catch (error) {
        console.log('Batch upload failed (expected):', error instanceof Error ? error.message : error);
    }
    console.log('');
    // Demo 5: Statistics
    console.log('🔍 Demo 5: Upload Statistics');
    const stats = s3Client.getStats();
    console.log('Final Stats:', stats);
    console.log('');
    // Demo 6: Key generation examples
    console.log('🔍 Demo 6: S3 Key Generation Examples');
    console.log('Production Response Key Pattern:');
    console.log('  Expected: mcp-responses/production/2024-06-24/production_20240624_143022_123e4567.txt');
    console.log('Staging Response Key Pattern:');
    console.log('  Expected: mcp-responses/staging/2024-06-24/staging_20240624_154530_abc12345.txt');
    console.log('Unknown Namespace Key Pattern:');
    console.log('  Expected: mcp-responses/NO_NAMESPACE/2024-06-24/NO_NAMESPACE_20240624_162015_xyz98765.txt');
    console.log('');
    console.log('✅ Demo completed successfully!');
    console.log('');
    console.log('🎯 Key Features Demonstrated:');
    console.log('- ✅ Namespace-based S3 folder organization');
    console.log('- ✅ Intelligent file naming with timestamps and UUIDs');
    console.log('- ✅ Rich metadata storage in S3 object metadata');
    console.log('- ✅ Comprehensive response content formatting');
    console.log('- ✅ Kubernetes context preservation');
    console.log('- ✅ Error handling and retry logic');
    console.log('- ✅ Batch upload capabilities');
    console.log('- ✅ Upload statistics and monitoring');
    console.log('- ✅ Connection testing and validation');
}
// Run the demo
demonstrateS3Client().catch(console.error);
//# sourceMappingURL=s3-demo.js.map