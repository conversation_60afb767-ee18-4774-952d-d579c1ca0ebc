{"version": 3, "file": "namespace-flexibility-demo.js", "sourceRoot": "", "sources": ["../../examples/namespace-flexibility-demo.ts"], "names": [], "mappings": "AAAA;;GAEG;AAEH,OAAO,EAAE,qBAAqB,EAAE,MAAM,uBAAuB,CAAC;AAC9D,OAAO,EACL,4BAA4B,EAC5B,yBAAyB,EACzB,2BAA2B,EAC5B,MAAM,4CAA4C,CAAC;AAEpD,KAAK,UAAU,+BAA+B;IAC5C,OAAO,CAAC,GAAG,CAAC,0CAA0C,CAAC,CAAC;IAExD,8BAA8B;IAC9B,MAAM,WAAW,GAAG,IAAI,qBAAqB,EAAE,CAAC;IAEhD,qCAAqC;IACrC,WAAW,CAAC,aAAa,CAAC,IAAI,4BAA4B,EAAE,CAAC,CAAC;IAC9D,WAAW,CAAC,aAAa,CAAC,IAAI,yBAAyB,EAAE,CAAC,CAAC;IAC3D,WAAW,CAAC,aAAa,CAAC,IAAI,2BAA2B,EAAE,CAAC,CAAC;IAE7D,OAAO,CAAC,GAAG,CAAC,6CAA6C,CAAC,CAAC;IAE3D,wCAAwC;IACxC,OAAO,CAAC,GAAG,CAAC,0CAA0C,CAAC,CAAC;IACxD,MAAM,SAAS,GAAG,MAAM,WAAW,CAAC,iBAAiB,CACnD,gEAAgE,EAChE;QACE,cAAc,EAAE,aAAa;QAC7B,QAAQ,EAAE,EAAE,WAAW,EAAE,kBAAkB,EAAE;KAC9C,CACF,CAAC;IACF,OAAO,CAAC,GAAG,CAAC,yBAAyB,SAAS,CAAC,SAAS,CAAC,SAAS,KAAK,SAAS,CAAC,SAAS,CAAC,UAAU,GAAG,CAAC,CAAC;IAC1G,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;IAEhB,uCAAuC;IACvC,OAAO,CAAC,GAAG,CAAC,yCAAyC,CAAC,CAAC;IACvD,MAAM,SAAS,GAAG,MAAM,WAAW,CAAC,iBAAiB,CACnD,qFAAqF,EACrF;QACE,cAAc,EAAE,aAAa;QAC7B,QAAQ,EAAE,EAAE,WAAW,EAAE,cAAc,EAAE;KAC1C,CACF,CAAC;IACF,OAAO,CAAC,GAAG,CAAC,yBAAyB,SAAS,CAAC,SAAS,CAAC,SAAS,KAAK,SAAS,CAAC,SAAS,CAAC,UAAU,GAAG,CAAC,CAAC;IAC1G,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;IAEhB,kCAAkC;IAClC,OAAO,CAAC,GAAG,CAAC,oCAAoC,CAAC,CAAC;IAClD,MAAM,SAAS,GAAG,MAAM,WAAW,CAAC,iBAAiB,CACnD,+DAA+D,EAC/D;QACE,cAAc,EAAE,aAAa;QAC7B,QAAQ,EAAE,EAAE,WAAW,EAAE,iBAAiB,EAAE;KAC7C,CACF,CAAC;IACF,OAAO,CAAC,GAAG,CAAC,yBAAyB,SAAS,CAAC,SAAS,CAAC,SAAS,KAAK,SAAS,CAAC,SAAS,CAAC,UAAU,GAAG,CAAC,CAAC;IAC1G,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;IAEhB,yCAAyC;IACzC,OAAO,CAAC,GAAG,CAAC,2CAA2C,CAAC,CAAC;IACzD,MAAM,SAAS,GAAG,MAAM,WAAW,CAAC,iBAAiB,CACnD,yEAAyE,EACzE;QACE,cAAc,EAAE,aAAa;QAC7B,QAAQ,EAAE,EAAE,WAAW,EAAE,sBAAsB,EAAE;KAClD,CACF,CAAC;IACF,OAAO,CAAC,GAAG,CAAC,yBAAyB,SAAS,CAAC,SAAS,CAAC,SAAS,KAAK,SAAS,CAAC,SAAS,CAAC,UAAU,GAAG,CAAC,CAAC;IAC1G,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;IAEhB,2BAA2B;IAC3B,OAAO,CAAC,GAAG,CAAC,6BAA6B,CAAC,CAAC;IAC3C,MAAM,SAAS,GAAG,MAAM,WAAW,CAAC,iBAAiB,CACnD,kFAAkF,EAClF;QACE,cAAc,EAAE,aAAa;QAC7B,QAAQ,EAAE,EAAE,WAAW,EAAE,wBAAwB,EAAE;KACpD,CACF,CAAC;IACF,OAAO,CAAC,GAAG,CAAC,yBAAyB,SAAS,CAAC,SAAS,CAAC,SAAS,KAAK,SAAS,CAAC,SAAS,CAAC,UAAU,GAAG,CAAC,CAAC;IAC1G,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;IAEhB,+BAA+B;IAC/B,OAAO,CAAC,GAAG,CAAC,iCAAiC,CAAC,CAAC;IAC/C,MAAM,SAAS,GAAG,MAAM,WAAW,CAAC,iBAAiB,CACnD,sEAAsE,EACtE;QACE,cAAc,EAAE,aAAa;QAC7B,QAAQ,EAAE,EAAE,WAAW,EAAE,qBAAqB,EAAE;KACjD,CACF,CAAC;IACF,OAAO,CAAC,GAAG,CAAC,yBAAyB,SAAS,CAAC,SAAS,CAAC,SAAS,KAAK,SAAS,CAAC,SAAS,CAAC,UAAU,GAAG,CAAC,CAAC;IAC1G,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;IAEhB,wDAAwD;IACxD,OAAO,CAAC,GAAG,CAAC,+CAA+C,CAAC,CAAC;IAC7D,MAAM,SAAS,GAAG,MAAM,WAAW,CAAC,iBAAiB,CACnD,oEAAoE,EACpE;QACE,cAAc,EAAE,aAAa;QAC7B,QAAQ,EAAE,EAAE,WAAW,EAAE,4BAA4B,EAAE;KACxD,CACF,CAAC;IACF,OAAO,CAAC,GAAG,CAAC,yBAAyB,SAAS,CAAC,SAAS,CAAC,SAAS,KAAK,SAAS,CAAC,SAAS,CAAC,UAAU,GAAG,CAAC,CAAC;IAC1G,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;IAEhB,sCAAsC;IACtC,OAAO,CAAC,GAAG,CAAC,wCAAwC,CAAC,CAAC;IACtD,MAAM,SAAS,GAAG,MAAM,WAAW,CAAC,iBAAiB,CACnD,uEAAuE,EACvE;QACE,cAAc,EAAE,aAAa;QAC7B,QAAQ,EAAE,EAAE,WAAW,EAAE,kBAAkB,EAAE;KAC9C,CACF,CAAC;IACF,OAAO,CAAC,GAAG,CAAC,yBAAyB,SAAS,CAAC,SAAS,CAAC,SAAS,KAAK,SAAS,CAAC,SAAS,CAAC,UAAU,GAAG,CAAC,CAAC;IAC1G,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;IAEhB,2CAA2C;IAC3C,OAAO,CAAC,GAAG,CAAC,6CAA6C,CAAC,CAAC;IAC3D,MAAM,SAAS,GAAG,MAAM,WAAW,CAAC,iBAAiB,CACnD,wEAAwE,EACxE;QACE,cAAc,EAAE,aAAa;QAC7B,QAAQ,EAAE,EAAE,WAAW,EAAE,kBAAkB,EAAE;KAC9C,CACF,CAAC;IACF,OAAO,CAAC,GAAG,CAAC,yBAAyB,SAAS,CAAC,SAAS,CAAC,SAAS,KAAK,SAAS,CAAC,SAAS,CAAC,UAAU,GAAG,CAAC,CAAC;IAC1G,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;IAEhB,UAAU;IACV,OAAO,CAAC,GAAG,CAAC,oCAAoC,CAAC,CAAC;IAClD,MAAM,SAAS,GAAG,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC,CAAC;IACtH,SAAS,CAAC,OAAO,CAAC,CAAC,QAAQ,EAAE,KAAK,EAAE,EAAE;QACpC,OAAO,CAAC,GAAG,CAAC,KAAK,KAAK,GAAG,CAAC,KAAK,QAAQ,CAAC,SAAS,CAAC,SAAS,KAAK,QAAQ,CAAC,SAAS,CAAC,MAAM,KAAK,QAAQ,CAAC,SAAS,CAAC,UAAU,GAAG,CAAC,CAAC;IAClI,CAAC,CAAC,CAAC;IACH,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;IAEhB,OAAO,CAAC,GAAG,CAAC,gCAAgC,CAAC,CAAC;IAC9C,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;IAChB,OAAO,CAAC,GAAG,CAAC,+BAA+B,CAAC,CAAC;IAC7C,OAAO,CAAC,GAAG,CAAC,4CAA4C,CAAC,CAAC;IAC1D,OAAO,CAAC,GAAG,CAAC,wCAAwC,CAAC,CAAC;IACtD,OAAO,CAAC,GAAG,CAAC,mEAAmE,CAAC,CAAC;IACjF,OAAO,CAAC,GAAG,CAAC,+DAA+D,CAAC,CAAC;IAC7E,OAAO,CAAC,GAAG,CAAC,gEAAgE,CAAC,CAAC;IAC9E,OAAO,CAAC,GAAG,CAAC,qDAAqD,CAAC,CAAC;IACnE,OAAO,CAAC,GAAG,CAAC,0DAA0D,CAAC,CAAC;IACxE,OAAO,CAAC,GAAG,CAAC,iEAAiE,CAAC,CAAC;IAC/E,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;IAChB,OAAO,CAAC,GAAG,CAAC,mBAAmB,CAAC,CAAC;IACjC,OAAO,CAAC,GAAG,CAAC,oFAAoF,CAAC,CAAC;IAClG,OAAO,CAAC,GAAG,CAAC,wDAAwD,CAAC,CAAC;IACtE,OAAO,CAAC,GAAG,CAAC,gEAAgE,CAAC,CAAC;AAChF,CAAC;AAED,eAAe;AACf,+BAA+B,EAAE,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC"}