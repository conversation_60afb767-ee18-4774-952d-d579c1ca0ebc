"use strict";
/**
 * Configuration management for the S3 MCP Server
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.getConfig = getConfig;
const dotenv_1 = __importDefault(require("dotenv"));
// Load environment variables
dotenv_1.default.config();
/**
 * Validates required environment variables
 */
function validateConfig() {
    const required = [
        'AWS_ACCESS_KEY_ID',
        'AWS_SECRET_ACCESS_KEY',
        'AWS_REGION',
        'S3_BUCKET_NAME'
    ];
    const missing = required.filter(key => !process.env[key]);
    if (missing.length > 0) {
        throw new Error(`Missing required environment variables: ${missing.join(', ')}`);
    }
}
/**
 * Parses comma-separated string into array
 */
function parseStringArray(value, defaultValue = []) {
    if (!value)
        return defaultValue;
    return value.split(',').map(item => item.trim()).filter(item => item.length > 0);
}
/**
 * Gets the server configuration from environment variables
 */
function getConfig() {
    validateConfig();
    return {
        aws: {
            accessKeyId: process.env['AWS_ACCESS_KEY_ID'],
            secretAccessKey: process.env['AWS_SECRET_ACCESS_KEY'],
            region: process.env['AWS_REGION'],
            bucketName: process.env['S3_BUCKET_NAME'],
        },
        mcp: {
            port: parseInt(process.env['MCP_SERVER_PORT'] || '3000', 10),
            serverName: process.env['MCP_SERVER_NAME'] || 's3-mcp-server',
        },
        kubernetes: {
            defaultNamespacePrefix: process.env['DEFAULT_NAMESPACE_PREFIX'] || 'NO_NAMESPACE',
            validationEnabled: process.env['NAMESPACE_VALIDATION_ENABLED'] === 'true',
            allowedNamespaces: parseStringArray(process.env['ALLOWED_NAMESPACES'], ['production', 'staging', 'development', 'kube-system']),
        },
        storage: {
            retentionDays: parseInt(process.env['FILE_RETENTION_DAYS'] || '30', 10),
            maxFileSizeMB: parseInt(process.env['MAX_FILE_SIZE_MB'] || '10', 10),
            enableCompression: process.env['ENABLE_COMPRESSION'] === 'true',
            basePath: process.env['S3_BASE_PATH'] || 'mcp-responses',
            enableDateFolders: process.env['ENABLE_DATE_FOLDERS'] !== 'false',
            enableNamespaceFolders: process.env['ENABLE_NAMESPACE_FOLDERS'] !== 'false',
        },
        logging: {
            level: process.env['LOG_LEVEL'] || 'info',
            filePath: process.env['LOG_FILE_PATH'] || './logs/mcp-server.log',
            enableConsoleLogging: process.env['ENABLE_CONSOLE_LOGGING'] !== 'false',
        },
    };
}
//# sourceMappingURL=config.js.map