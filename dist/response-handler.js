/**
 * AI Agent Response Interceptor
 * Handles processing AI responses and extracting Kubernetes namespace information
 */
import { v4 as uuidv4 } from 'uuid';
import { getConfig } from './config.js';
import logger from './logger.js';
export class ResponseHandler {
    config;
    constructor() {
        this.config = getConfig();
    }
    async processResponse(content, context) {
        const id = uuidv4();
        const timestamp = new Date();
        logger.debug('Processing AI response', {
            id,
            contentLength: content.length,
            context,
        });
        // Extract namespace information
        const namespaceInfo = this.extractNamespace(content, context);
        // Extract pod names from content and context
        const podNames = this.extractPodNames(content, context);
        const response = {
            id,
            timestamp,
            content,
            namespace: namespaceInfo,
            conversationId: context?.conversationId,
            requestType: context?.requestType,
            podNames,
            metadata: context?.metadata,
        };
        logger.info('AI response processed', {
            id,
            namespace: namespaceInfo.namespace,
            namespaceSource: namespaceInfo.source,
            namespaceConfidence: namespaceInfo.confidence,
            podCount: podNames?.length || 0,
        });
        return response;
    }
    extractNamespace(content, context) {
        // Try different extraction methods in order of preference
        // 1. Direct parameter from context (highest confidence)
        if (context?.namespace) {
            return {
                namespace: this.validateNamespace(context.namespace),
                confidence: 'high',
                source: 'parameter',
                extractedFrom: 'context.namespace',
            };
        }
        // 2. Extract from kubectl commands in content
        const kubectlNamespace = this.extractFromKubectlCommands(content);
        if (kubectlNamespace) {
            return kubectlNamespace;
        }
        // 3. Extract from pod names in context
        if (context?.podNames && Array.isArray(context.podNames)) {
            const podNamespace = this.extractFromPodNames(context.podNames);
            if (podNamespace) {
                return podNamespace;
            }
        }
        // 4. Extract from pod names in content
        const contentPodNamespace = this.extractFromContent(content);
        if (contentPodNamespace) {
            return contentPodNamespace;
        }
        // 5. Extract from log paths
        const logPathNamespace = this.extractFromLogPaths(content);
        if (logPathNamespace) {
            return logPathNamespace;
        }
        // 6. Default fallback
        return {
            namespace: this.config.kubernetes.defaultNamespacePrefix,
            confidence: 'low',
            source: 'default',
        };
    }
    extractFromRequest(request) {
        if (request?.namespace) {
            return {
                namespace: this.validateNamespace(request.namespace),
                confidence: 'high',
                source: 'parameter',
                extractedFrom: 'request.namespace',
            };
        }
        return null;
    }
    extractFromContent(content) {
        // Look for common Kubernetes patterns in content
        const patterns = [
            // Pod names with namespace: namespace/pod-name or namespace_pod-name
            /(?:namespace[\/\-_]|ns[\/\-_])([a-z0-9\-]+)/gi,
            // Kubernetes resource references: namespace:pod-name
            /([a-z0-9\-]+):[\w\-]+/gi,
            // Log entries with namespace context
            /namespace[:\s]+([a-z0-9\-]+)/gi,
            // Pod full names: pod-name-xxx in namespace context
            /in\s+namespace\s+([a-z0-9\-]+)/gi,
        ];
        for (const pattern of patterns) {
            const matches = content.matchAll(pattern);
            for (const match of matches) {
                const namespace = match[1];
                if (namespace && this.isValidNamespace(namespace)) {
                    return {
                        namespace: this.validateNamespace(namespace),
                        confidence: 'medium',
                        source: 'content',
                        extractedFrom: match[0],
                    };
                }
            }
        }
        return null;
    }
    extractFromContext(context) {
        if (context?.namespace) {
            return {
                namespace: this.validateNamespace(context.namespace),
                confidence: 'high',
                source: 'context',
                extractedFrom: 'context.namespace',
            };
        }
        return null;
    }
    extractFromKubectlCommands(content) {
        // Look for kubectl commands with -n or --namespace flags
        const kubectlPatterns = [
            /kubectl\s+[^-]*-n\s+([a-z0-9\-]+)/gi,
            /kubectl\s+[^-]*--namespace[=\s]+([a-z0-9\-]+)/gi,
            /kubectl\s+[^-]*--namespace=([a-z0-9\-]+)/gi,
        ];
        for (const pattern of kubectlPatterns) {
            const matches = content.matchAll(pattern);
            for (const match of matches) {
                const namespace = match[1];
                if (namespace && this.isValidNamespace(namespace)) {
                    return {
                        namespace: this.validateNamespace(namespace),
                        confidence: 'high',
                        source: 'command',
                        extractedFrom: match[0],
                    };
                }
            }
        }
        return null;
    }
    extractFromPodNames(podNames) {
        // Try to extract namespace from pod naming conventions
        for (const podName of podNames) {
            // Look for namespace prefixes in pod names
            const parts = podName.split(/[-_]/);
            if (parts.length > 1) {
                const potentialNamespace = parts[0];
                if (potentialNamespace && this.isValidNamespace(potentialNamespace)) {
                    return {
                        namespace: this.validateNamespace(potentialNamespace),
                        confidence: 'medium',
                        source: 'pod-name',
                        extractedFrom: podName,
                    };
                }
            }
        }
        return null;
    }
    extractFromLogPaths(content) {
        // Look for Kubernetes log paths that contain namespace information
        const logPathPatterns = [
            /\/var\/log\/pods\/([a-z0-9\-]+)_/gi,
            /\/var\/log\/containers\/[^_]+_([a-z0-9\-]+)_/gi,
            /logs\/([a-z0-9\-]+)\//gi,
        ];
        for (const pattern of logPathPatterns) {
            const matches = content.matchAll(pattern);
            for (const match of matches) {
                const namespace = match[1];
                if (namespace && this.isValidNamespace(namespace)) {
                    return {
                        namespace: this.validateNamespace(namespace),
                        confidence: 'medium',
                        source: 'log-path',
                        extractedFrom: match[0],
                    };
                }
            }
        }
        return null;
    }
    extractPodNames(content, context) {
        const podNames = [];
        // Add pod names from context
        if (context?.podNames && Array.isArray(context.podNames)) {
            podNames.push(...context.podNames);
        }
        // Extract pod names from content using common patterns
        const podPatterns = [
            // Standard pod names: app-name-xxxxx-xxxxx
            /\b([a-z0-9\-]+)-[a-z0-9]{5,10}-[a-z0-9]{5}\b/gi,
            // Pod names in kubectl output
            /^([a-z0-9\-]+)\s+\d+\/\d+/gm,
            // Pod names in logs
            /pod[\/\s]+([a-z0-9\-]+)/gi,
        ];
        for (const pattern of podPatterns) {
            const matches = content.matchAll(pattern);
            for (const match of matches) {
                const podName = match[1];
                if (podName && !podNames.includes(podName)) {
                    podNames.push(podName);
                }
            }
        }
        return podNames;
    }
    isValidNamespace(namespace) {
        // Kubernetes namespace naming rules:
        // - Must be lowercase alphanumeric or hyphens
        // - Must start and end with alphanumeric
        // - Must be 1-63 characters
        const namespaceRegex = /^[a-z0-9]([a-z0-9\-]*[a-z0-9])?$/;
        if (!namespace || namespace.length === 0 || namespace.length > 63) {
            return false;
        }
        if (!namespaceRegex.test(namespace)) {
            return false;
        }
        // Check against allowed namespaces if validation is enabled
        if (this.config.kubernetes.validationEnabled) {
            return this.config.kubernetes.allowedNamespaces.includes(namespace);
        }
        return true;
    }
    validateNamespace(namespace) {
        // Clean and validate the namespace
        const cleaned = namespace.toLowerCase().trim();
        if (this.isValidNamespace(cleaned)) {
            return cleaned;
        }
        logger.warn('Invalid namespace detected, using default', {
            original: namespace,
            cleaned,
            default: this.config.kubernetes.defaultNamespacePrefix,
        });
        return this.config.kubernetes.defaultNamespacePrefix;
    }
}
//# sourceMappingURL=response-handler.js.map