/**
 * AI Agent Response Interceptor
 * Handles processing AI responses and extracting Kubernetes namespace information
 */
import { AIResponse, NamespaceInfo, NamespaceExtractor } from './types.js';
export declare class ResponseHandler implements NamespaceExtractor {
    private config;
    constructor();
    processResponse(content: string, context?: any): Promise<AIResponse>;
    private extractNamespace;
    extractFromRequest(request: any): NamespaceInfo | null;
    extractFromContent(content: string): NamespaceInfo | null;
    extractFromContext(context: any): NamespaceInfo | null;
    private extractFromKubectlCommands;
    private extractFromPodNames;
    private extractFromLogPaths;
    private extractPodNames;
    private isValidNamespace;
    private validateNamespace;
}
//# sourceMappingURL=response-handler.d.ts.map