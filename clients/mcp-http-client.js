#!/usr/bin/env node

/**
 * HTTP Client for S3 MCP Server
 * This client bridges the MCP protocol over HTTP for remote server access
 */

const https = require('https');
const http = require('http');
const readline = require('readline');
const { URL } = require('url');

// Configuration from environment variables
const MCP_SERVER_URL = process.env.MCP_SERVER_URL || 'http://localhost:3000';
const REQUEST_TIMEOUT = parseInt(process.env.REQUEST_TIMEOUT || '30000');
const DEBUG = process.env.DEBUG === 'true';

// Parse server URL
const serverUrl = new URL(MCP_SERVER_URL);
const isHttps = serverUrl.protocol === 'https:';
const httpModule = isHttps ? https : http;

// Debug logging
function debugLog(message, data = null) {
  if (DEBUG) {
    console.error(`[DEBUG] ${message}`, data ? JSON.stringify(data, null, 2) : '');
  }
}

// Error logging
function errorLog(message, error = null) {
  console.error(`[ERROR] ${message}`, error ? error.message : '');
}

// Create readline interface for MCP communication
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout,
  terminal: false
});

// Handle MCP requests
rl.on('line', async (line) => {
  try {
    const request = JSON.parse(line.trim());
    debugLog('Received MCP request', request);

    // Handle different MCP methods
    if (request.method === 'tools/list') {
      await handleToolsList(request);
    } else if (request.method === 'tools/call') {
      await handleToolCall(request);
    } else {
      // Forward other requests to the MCP endpoint
      await forwardToMCP(request);
    }
  } catch (error) {
    errorLog('Failed to process MCP request', error);
    
    // Send error response
    const errorResponse = {
      jsonrpc: '2.0',
      id: null,
      error: {
        code: -32603,
        message: 'Internal error',
        data: {
          details: error.message
        }
      }
    };
    
    console.log(JSON.stringify(errorResponse));
  }
});

// Handle tools/list requests
async function handleToolsList(request) {
  try {
    const response = await makeHttpRequest('/mcp', {
      method: 'POST',
      body: JSON.stringify(request),
      headers: {
        'Content-Type': 'application/json'
      }
    });

    console.log(JSON.stringify(response));
  } catch (error) {
    errorLog('Failed to list tools', error);
    sendErrorResponse(request.id, 'Failed to list tools', error.message);
  }
}

// Handle tools/call requests
async function handleToolCall(request) {
  try {
    if (request.params.name === 'capture_ai_response') {
      // Use direct tool endpoint for better performance
      const response = await makeHttpRequest('/tools/capture_ai_response', {
        method: 'POST',
        body: JSON.stringify(request.params.arguments),
        headers: {
          'Content-Type': 'application/json',
          'X-Request-ID': generateRequestId()
        }
      });

      // Convert direct API response to MCP format
      const mcpResponse = {
        jsonrpc: '2.0',
        id: request.id,
        result: {
          content: [
            {
              type: 'text',
              text: response.success 
                ? `✅ Successfully stored AI response in S3\n\n**Details:**\n- S3 Key: ${response.s3Key}\n- Namespace: ${response.namespace}\n- Processing Time: ${response.processingTime}ms`
                : `❌ Failed to store AI response: ${response.error}`
            }
          ]
        }
      };

      console.log(JSON.stringify(mcpResponse));
    } else {
      // Forward unknown tools to MCP endpoint
      await forwardToMCP(request);
    }
  } catch (error) {
    errorLog('Failed to call tool', error);
    sendErrorResponse(request.id, 'Tool call failed', error.message);
  }
}

// Forward request to MCP endpoint
async function forwardToMCP(request) {
  try {
    const response = await makeHttpRequest('/mcp', {
      method: 'POST',
      body: JSON.stringify(request),
      headers: {
        'Content-Type': 'application/json'
      }
    });

    console.log(JSON.stringify(response));
  } catch (error) {
    errorLog('Failed to forward to MCP', error);
    sendErrorResponse(request.id, 'MCP request failed', error.message);
  }
}

// Make HTTP request to the server
function makeHttpRequest(path, options = {}) {
  return new Promise((resolve, reject) => {
    const requestOptions = {
      hostname: serverUrl.hostname,
      port: serverUrl.port || (isHttps ? 443 : 80),
      path: path,
      method: options.method || 'GET',
      headers: {
        'User-Agent': 'MCP-HTTP-Client/1.0.0',
        ...options.headers
      },
      timeout: REQUEST_TIMEOUT
    };

    debugLog('Making HTTP request', { url: MCP_SERVER_URL + path, options: requestOptions });

    const req = httpModule.request(requestOptions, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        try {
          if (res.statusCode >= 200 && res.statusCode < 300) {
            const response = data ? JSON.parse(data) : {};
            debugLog('HTTP request successful', { statusCode: res.statusCode, response });
            resolve(response);
          } else {
            const error = new Error(`HTTP ${res.statusCode}: ${data}`);
            errorLog('HTTP request failed', error);
            reject(error);
          }
        } catch (parseError) {
          errorLog('Failed to parse response', parseError);
          reject(parseError);
        }
      });
    });

    req.on('error', (error) => {
      errorLog('HTTP request error', error);
      reject(error);
    });

    req.on('timeout', () => {
      const error = new Error(`Request timeout after ${REQUEST_TIMEOUT}ms`);
      errorLog('HTTP request timeout', error);
      req.destroy();
      reject(error);
    });

    // Send request body if provided
    if (options.body) {
      req.write(options.body);
    }
    
    req.end();
  });
}

// Send error response in MCP format
function sendErrorResponse(id, message, details) {
  const errorResponse = {
    jsonrpc: '2.0',
    id: id,
    error: {
      code: -32603,
      message: message,
      data: {
        details: details
      }
    }
  };
  
  console.log(JSON.stringify(errorResponse));
}

// Generate unique request ID
function generateRequestId() {
  return `req_${Date.now()}_${Math.random().toString(36).substring(2, 8)}`;
}

// Handle process signals
process.on('SIGINT', () => {
  debugLog('Received SIGINT, shutting down...');
  process.exit(0);
});

process.on('SIGTERM', () => {
  debugLog('Received SIGTERM, shutting down...');
  process.exit(0);
});

// Handle uncaught errors
process.on('uncaughtException', (error) => {
  errorLog('Uncaught exception', error);
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  errorLog('Unhandled rejection', reason);
  process.exit(1);
});

debugLog('MCP HTTP Client started', { 
  serverUrl: MCP_SERVER_URL,
  timeout: REQUEST_TIMEOUT,
  debug: DEBUG 
});

// Keep the process alive
process.stdin.resume();
