#!/bin/bash

# S3 MCP Server Deployment Script
# This script automates the deployment process for different environments

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Default values
ENVIRONMENT="development"
SKIP_TESTS=false
SKIP_BUILD=false
DOCKER_TAG="latest"
NAMESPACE="s3-mcp-server"

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to show usage
show_usage() {
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Options:"
    echo "  -e, --environment ENV    Target environment (development|staging|production)"
    echo "  -t, --tag TAG           Docker image tag (default: latest)"
    echo "  -n, --namespace NS      Kubernetes namespace (default: s3-mcp-server)"
    echo "  --skip-tests           Skip running tests"
    echo "  --skip-build           Skip building the application"
    echo "  -h, --help             Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0 -e production -t v1.2.3"
    echo "  $0 --environment staging --skip-tests"
    echo "  $0 -e development --skip-build"
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -e|--environment)
            ENVIRONMENT="$2"
            shift 2
            ;;
        -t|--tag)
            DOCKER_TAG="$2"
            shift 2
            ;;
        -n|--namespace)
            NAMESPACE="$2"
            shift 2
            ;;
        --skip-tests)
            SKIP_TESTS=true
            shift
            ;;
        --skip-build)
            SKIP_BUILD=true
            shift
            ;;
        -h|--help)
            show_usage
            exit 0
            ;;
        *)
            print_error "Unknown option: $1"
            show_usage
            exit 1
            ;;
    esac
done

# Validate environment
if [[ ! "$ENVIRONMENT" =~ ^(development|staging|production)$ ]]; then
    print_error "Invalid environment: $ENVIRONMENT"
    print_error "Must be one of: development, staging, production"
    exit 1
fi

print_status "Starting deployment for environment: $ENVIRONMENT"

# Step 1: Validate configuration
print_status "Validating configuration..."
if ! npm run config:validate; then
    print_error "Configuration validation failed"
    exit 1
fi
print_success "Configuration validation passed"

# Step 2: Run tests (unless skipped)
if [ "$SKIP_TESTS" = false ]; then
    print_status "Running tests..."
    if ! npm test; then
        print_error "Tests failed"
        exit 1
    fi
    print_success "All tests passed"
else
    print_warning "Skipping tests"
fi

# Step 3: Build application (unless skipped)
if [ "$SKIP_BUILD" = false ]; then
    print_status "Building application..."
    if ! npm run build; then
        print_error "Build failed"
        exit 1
    fi
    print_success "Build completed"
else
    print_warning "Skipping build"
fi

# Step 4: Environment-specific deployment
case $ENVIRONMENT in
    development)
        deploy_development
        ;;
    staging)
        deploy_staging
        ;;
    production)
        deploy_production
        ;;
esac

print_success "Deployment completed successfully!"

# Function to deploy to development
deploy_development() {
    print_status "Deploying to development environment..."
    
    # Start local development server
    print_status "Starting development server..."
    npm run dev &
    DEV_PID=$!
    
    # Wait for server to start
    sleep 5
    
    # Test health endpoint
    if curl -f http://localhost:3000/health > /dev/null 2>&1; then
        print_success "Development server is running (PID: $DEV_PID)"
        print_status "Health check: http://localhost:3000/health"
        print_status "To stop: kill $DEV_PID"
    else
        print_error "Development server failed to start"
        kill $DEV_PID 2>/dev/null || true
        exit 1
    fi
}

# Function to deploy to staging
deploy_staging() {
    print_status "Deploying to staging environment..."
    
    # Build Docker image
    print_status "Building Docker image..."
    docker build -t s3-mcp-server:$DOCKER_TAG .
    
    # Stop existing container if running
    docker stop s3-mcp-server-staging 2>/dev/null || true
    docker rm s3-mcp-server-staging 2>/dev/null || true
    
    # Run new container
    print_status "Starting staging container..."
    docker run -d \
        --name s3-mcp-server-staging \
        -p 3001:3000 \
        --env-file .env.staging \
        s3-mcp-server:$DOCKER_TAG
    
    # Wait for container to start
    sleep 10
    
    # Test health endpoint
    if curl -f http://localhost:3001/health > /dev/null 2>&1; then
        print_success "Staging deployment successful"
        print_status "Health check: http://localhost:3001/health"
        print_status "Container: s3-mcp-server-staging"
    else
        print_error "Staging deployment failed"
        docker logs s3-mcp-server-staging
        exit 1
    fi
}

# Function to deploy to production
deploy_production() {
    print_status "Deploying to production environment..."
    
    # Verify kubectl is configured
    if ! kubectl cluster-info > /dev/null 2>&1; then
        print_error "kubectl is not configured or cluster is not accessible"
        exit 1
    fi
    
    # Build and push Docker image
    print_status "Building and pushing Docker image..."
    docker build -t s3-mcp-server:$DOCKER_TAG .
    
    # Tag for registry (adjust registry URL as needed)
    # docker tag s3-mcp-server:$DOCKER_TAG your-registry/s3-mcp-server:$DOCKER_TAG
    # docker push your-registry/s3-mcp-server:$DOCKER_TAG
    
    # Create namespace if it doesn't exist
    kubectl create namespace $NAMESPACE --dry-run=client -o yaml | kubectl apply -f -
    
    # Apply Kubernetes manifests
    print_status "Applying Kubernetes manifests..."
    
    # Apply ConfigMap
    kubectl apply -f - <<EOF
apiVersion: v1
kind: ConfigMap
metadata:
  name: s3-mcp-config
  namespace: $NAMESPACE
data:
  NODE_ENV: "production"
  AWS_REGION: "${AWS_REGION:-us-east-1}"
  S3_BUCKET_NAME: "${S3_BUCKET_NAME}"
  LOG_LEVEL: "info"
  ENABLE_CONSOLE_LOGGING: "false"
  DEFAULT_NAMESPACE_PREFIX: "NO_NAMESPACE"
  ENABLE_NAMESPACE_FORMAT_VALIDATION: "true"
EOF
    
    # Apply Deployment
    kubectl apply -f - <<EOF
apiVersion: apps/v1
kind: Deployment
metadata:
  name: s3-mcp-server
  namespace: $NAMESPACE
  labels:
    app: s3-mcp-server
    version: $DOCKER_TAG
spec:
  replicas: 3
  selector:
    matchLabels:
      app: s3-mcp-server
  template:
    metadata:
      labels:
        app: s3-mcp-server
        version: $DOCKER_TAG
    spec:
      containers:
      - name: s3-mcp-server
        image: s3-mcp-server:$DOCKER_TAG
        ports:
        - containerPort: 3000
        env:
        - name: AWS_ACCESS_KEY_ID
          valueFrom:
            secretKeyRef:
              name: aws-credentials
              key: access-key-id
        - name: AWS_SECRET_ACCESS_KEY
          valueFrom:
            secretKeyRef:
              name: aws-credentials
              key: secret-access-key
        envFrom:
        - configMapRef:
            name: s3-mcp-config
        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"
          limits:
            memory: "512Mi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /health
            port: 3000
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /health
            port: 3000
          initialDelaySeconds: 5
          periodSeconds: 5
EOF
    
    # Apply Service
    kubectl apply -f - <<EOF
apiVersion: v1
kind: Service
metadata:
  name: s3-mcp-service
  namespace: $NAMESPACE
spec:
  selector:
    app: s3-mcp-server
  ports:
  - protocol: TCP
    port: 80
    targetPort: 3000
  type: ClusterIP
EOF
    
    # Wait for deployment to be ready
    print_status "Waiting for deployment to be ready..."
    kubectl rollout status deployment/s3-mcp-server -n $NAMESPACE --timeout=300s
    
    # Verify deployment
    if kubectl get pods -n $NAMESPACE -l app=s3-mcp-server | grep -q Running; then
        print_success "Production deployment successful"
        print_status "Namespace: $NAMESPACE"
        print_status "Image: s3-mcp-server:$DOCKER_TAG"
        
        # Show deployment status
        kubectl get pods -n $NAMESPACE -l app=s3-mcp-server
    else
        print_error "Production deployment failed"
        kubectl describe pods -n $NAMESPACE -l app=s3-mcp-server
        exit 1
    fi
}

# Cleanup function
cleanup() {
    if [ "$ENVIRONMENT" = "development" ] && [ ! -z "$DEV_PID" ]; then
        print_status "Cleaning up development server..."
        kill $DEV_PID 2>/dev/null || true
    fi
}

# Set up cleanup on script exit
trap cleanup EXIT
