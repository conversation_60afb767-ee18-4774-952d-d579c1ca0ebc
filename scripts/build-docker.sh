#!/bin/bash

# Docker Build Script for S3 MCP Server
# This script builds and optionally pushes the Docker image

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Default values
IMAGE_NAME="s3-mcp-server"
TAG="latest"
REGISTRY=""
PUSH=false
PLATFORM="linux/amd64"
BUILD_ARGS=""

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to show usage
show_usage() {
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Options:"
    echo "  -n, --name NAME         Image name (default: s3-mcp-server)"
    echo "  -t, --tag TAG           Image tag (default: latest)"
    echo "  -r, --registry REGISTRY Registry URL (e.g., your-registry.com)"
    echo "  -p, --push              Push image to registry after building"
    echo "  --platform PLATFORM    Target platform (default: linux/amd64)"
    echo "  --build-arg ARG=VALUE   Pass build argument to Docker"
    echo "  -h, --help              Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0 -t v1.0.0 -p"
    echo "  $0 -r your-registry.com -t latest --push"
    echo "  $0 --platform linux/arm64 -t arm64-latest"
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -n|--name)
            IMAGE_NAME="$2"
            shift 2
            ;;
        -t|--tag)
            TAG="$2"
            shift 2
            ;;
        -r|--registry)
            REGISTRY="$2"
            shift 2
            ;;
        -p|--push)
            PUSH=true
            shift
            ;;
        --platform)
            PLATFORM="$2"
            shift 2
            ;;
        --build-arg)
            BUILD_ARGS="$BUILD_ARGS --build-arg $2"
            shift 2
            ;;
        -h|--help)
            show_usage
            exit 0
            ;;
        *)
            print_error "Unknown option: $1"
            show_usage
            exit 1
            ;;
    esac
done

# Construct full image name
if [ -n "$REGISTRY" ]; then
    FULL_IMAGE_NAME="$REGISTRY/$IMAGE_NAME:$TAG"
else
    FULL_IMAGE_NAME="$IMAGE_NAME:$TAG"
fi

print_status "Building Docker image: $FULL_IMAGE_NAME"
print_status "Platform: $PLATFORM"

# Check if Docker is available
if ! command -v docker &> /dev/null; then
    print_error "Docker is not installed or not in PATH"
    exit 1
fi

# Check if we're in the right directory
if [ ! -f "package.json" ]; then
    print_error "package.json not found. Please run this script from the project root."
    exit 1
fi

# Build the Docker image
print_status "Building Docker image..."
docker build \
    --platform "$PLATFORM" \
    --tag "$FULL_IMAGE_NAME" \
    $BUILD_ARGS \
    .

if [ $? -eq 0 ]; then
    print_success "Docker image built successfully: $FULL_IMAGE_NAME"
else
    print_error "Docker build failed"
    exit 1
fi

# Show image details
print_status "Image details:"
docker images "$FULL_IMAGE_NAME" --format "table {{.Repository}}\t{{.Tag}}\t{{.Size}}\t{{.CreatedAt}}"

# Test the image
print_status "Testing the image..."
CONTAINER_ID=$(docker run -d --rm -p 3001:3000 -e HTTP_MODE=true "$FULL_IMAGE_NAME")

# Wait for container to start
sleep 5

# Test health endpoint
if curl -f http://localhost:3001/health > /dev/null 2>&1; then
    print_success "Image test passed - health endpoint responding"
else
    print_warning "Image test failed - health endpoint not responding (this may be due to missing AWS credentials)"
fi

# Stop test container
docker stop "$CONTAINER_ID" > /dev/null 2>&1

# Push to registry if requested
if [ "$PUSH" = true ]; then
    if [ -z "$REGISTRY" ]; then
        print_error "Cannot push without registry. Use -r/--registry option."
        exit 1
    fi
    
    print_status "Pushing image to registry..."
    docker push "$FULL_IMAGE_NAME"
    
    if [ $? -eq 0 ]; then
        print_success "Image pushed successfully: $FULL_IMAGE_NAME"
    else
        print_error "Failed to push image"
        exit 1
    fi
fi

print_success "Build completed successfully!"
print_status "Image: $FULL_IMAGE_NAME"
print_status "Size: $(docker images "$FULL_IMAGE_NAME" --format "{{.Size}}")"

if [ "$PUSH" = true ]; then
    print_status "Image is available in registry: $REGISTRY"
fi

echo ""
print_status "To run the image locally:"
echo "  docker run -d -p 3000:3000 -e HTTP_MODE=true \\"
echo "    -e AWS_ACCESS_KEY_ID=your-key \\"
echo "    -e AWS_SECRET_ACCESS_KEY=your-secret \\"
echo "    -e AWS_REGION=us-east-1 \\"
echo "    -e S3_BUCKET_NAME=your-bucket \\"
echo "    $FULL_IMAGE_NAME"

echo ""
print_status "To deploy to Kubernetes:"
echo "  Update the image in k8s/http-deployment.yaml to: $FULL_IMAGE_NAME"
echo "  Then run: kubectl apply -f k8s/http-deployment.yaml"
