# S3 MCP Server

A production-ready Model Context Protocol (MCP) server that captures AI agent responses and stores them in AWS S3 with intelligent Kubernetes namespace-based organization. Designed specifically for debugging Kubernetes pod logs across dozens of different namespaces.

## 🌟 Key Features

### Core Functionality
- 🚀 **MCP Integration**: Seamlessly integrates with AI agents using the Model Context Protocol
- 🗂️ **Namespace Organization**: Automatically organizes S3 files by Kubernetes namespace
- 📁 **Smart File Structure**: Creates organized folder structures with date-based organization
- 🔍 **Intelligent Namespace Detection**: Extracts namespace from various sources (commands, pod names, etc.)
- 📝 **Rich Metadata**: Stores conversation context and debugging information

### Advanced Features
- 🛡️ **Enterprise Error Handling**: Comprehensive error handling with retry logic, circuit breakers, and graceful degradation
- 📊 **Production Monitoring**: Health checks, performance metrics, and real-time system monitoring
- 🔄 **Retry Logic**: Intelligent retry mechanisms with exponential backoff and jitter
- 🚨 **Error Reporting**: Automated error reporting with deduplication and alerting
- 📈 **Performance Tracking**: Detailed performance metrics and optimization insights
- 🔧 **Configuration Management**: Environment-specific configurations with validation and secrets management

### Kubernetes Integration
- 🎯 **Unlimited Namespace Support**: Works with ANY Kubernetes namespace - no restrictions or allowlists
- 🔍 **Advanced Pattern Detection**: Detects pods, errors, metrics, and resource information
- 📋 **Kubernetes Middleware**: Specialized processing for kubectl commands and pod logs
- 🏷️ **Resource Tagging**: Automatic tagging and categorization of Kubernetes resources

## Quick Start

1. **Clone and Install**
   ```bash
   git clone <repository-url>
   cd s3-mcp-server
   npm install
   ```

2. **Configure Environment**
   ```bash
   cp .env.example .env
   # Edit .env with your AWS credentials and S3 bucket details
   ```

3. **Build and Run**
   ```bash
   npm run build
   npm start
   ```

## Configuration

See `.env.example` for all available configuration options.

### Required Environment Variables

- `AWS_ACCESS_KEY_ID`: Your AWS access key
- `AWS_SECRET_ACCESS_KEY`: Your AWS secret key
- `AWS_REGION`: AWS region for your S3 bucket
- `S3_BUCKET_NAME`: Name of your S3 bucket

## S3 Organization Structure

```
s3-bucket/
├── production/           # namespace
│   ├── 2024-06-24/      # date
│   │   ├── production_20240624_143022_uuid.txt
│   │   └── production_20240624_143155_uuid.txt
├── staging/
│   ├── 2024-06-24/
│   │   └── staging_20240624_144301_uuid.txt
├── NO_NAMESPACE/         # fallback folder
│   ├── 2024-06-24/
│   │   └── NO_NAMESPACE_20240624_145022_uuid.txt
```

## Development

```bash
# Development mode with hot reload
npm run dev

# Run tests
npm test

# Run tests in watch mode
npm run test:watch

# Lint code
npm run lint

# Build for production
npm run build
```

## License

MIT
