# S3 MCP Server

A production-ready Model Context Protocol (MCP) server that captures AI agent responses and stores them in AWS S3 with intelligent Kubernetes namespace-based organization. Designed specifically for debugging Kubernetes pod logs across dozens of different namespaces.

## 🌟 Key Features

### Core Functionality
- 🚀 **MCP Integration**: Seamlessly integrates with AI agents using the Model Context Protocol
- 🗂️ **Namespace Organization**: Automatically organizes S3 files by Kubernetes namespace
- 📁 **Smart File Structure**: Creates organized folder structures with date-based organization
- 🔍 **Intelligent Namespace Detection**: Extracts namespace from various sources (commands, pod names, etc.)
- 📝 **Rich Metadata**: Stores conversation context and debugging information

### Advanced Features
- 🛡️ **Enterprise Error Handling**: Comprehensive error handling with retry logic, circuit breakers, and graceful degradation
- 📊 **Production Monitoring**: Health checks, performance metrics, and real-time system monitoring
- 🔄 **Retry Logic**: Intelligent retry mechanisms with exponential backoff and jitter
- 🚨 **Error Reporting**: Automated error reporting with deduplication and alerting
- 📈 **Performance Tracking**: Detailed performance metrics and optimization insights
- 🔧 **Configuration Management**: Environment-specific configurations with validation and secrets management

### Kubernetes Integration
- 🎯 **Unlimited Namespace Support**: Works with ANY Kubernetes namespace - no restrictions or allowlists
- 🔍 **Advanced Pattern Detection**: Detects pods, errors, metrics, and resource information
- 📋 **Kubernetes Middleware**: Specialized processing for kubectl commands and pod logs
- 🏷️ **Resource Tagging**: Automatic tagging and categorization of Kubernetes resources

## 🚀 Quick Start

### Prerequisites
- Node.js 18+
- AWS Account with S3 access
- AWS CLI configured (optional but recommended)

### Installation

1. **Clone and Install**
   ```bash
   git clone <repository-url>
   cd s3-mcp-server
   npm install
   ```

2. **Configure Environment**
   ```bash
   cp .env.example .env
   # Edit .env with your AWS credentials and settings
   ```

3. **Validate Configuration**
   ```bash
   npm run config:validate
   npm run config:show
   ```

4. **Test S3 Connection**
   ```bash
   npm run test:connection
   ```

5. **Start the Server**
   ```bash
   # Development mode
   npm run dev

   # Production mode
   npm run build
   npm start
   ```

### Verification

Test the server with a sample request:
```bash
# Test namespace detection and S3 upload
npm run demo:namespace-flexibility
```

## 🎯 Perfect for Kubernetes Debugging

This MCP server is specifically designed for debugging Kubernetes environments with **unlimited namespace support**:

### Supported Namespace Types
- ✅ **Production**: `production`, `prod`, `live`, `main`
- ✅ **Development**: `development`, `dev`, `staging`, `test`, `qa`
- ✅ **Team-based**: `team-alpha`, `backend-team`, `frontend-squad`
- ✅ **Feature branches**: `feature-xyz-123`, `dev-feature-abc`, `hotfix-urgent`
- ✅ **Customer-specific**: `customer-acme-corp`, `tenant-123`, `client-xyz`
- ✅ **System namespaces**: `kube-system`, `monitoring`, `logging`, `ingress-nginx`
- ✅ **Custom namespaces**: Any valid Kubernetes namespace name

### Intelligent Detection
The system automatically detects namespaces from:
- `kubectl` commands: `kubectl logs -n production pod-name`
- Log file paths: `/var/log/pods/production_pod-name_uuid/`
- Pod names: `production-web-server-12345-abcde`
- Content analysis: Text mentioning "namespace production"

## ⚙️ Configuration

See [CONFIGURATION.md](docs/CONFIGURATION.md) for comprehensive configuration documentation.

### Required Environment Variables

```bash
# AWS Configuration (Required)
AWS_ACCESS_KEY_ID=your-access-key
AWS_SECRET_ACCESS_KEY=your-secret-key
AWS_REGION=us-east-1
S3_BUCKET_NAME=your-bucket-name

# Kubernetes Configuration (Optional)
DEFAULT_NAMESPACE_PREFIX=NO_NAMESPACE
ENABLE_NAMESPACE_FORMAT_VALIDATION=true
```

### Quick Configuration Commands

```bash
# Validate all settings
npm run config:validate

# Show current configuration (masked)
npm run config:show

# Test environment setup
npm run config:env
```

## 📁 S3 Organization Structure

The server creates an intelligent, organized structure in your S3 bucket:

```
s3-bucket/
├── production/                    # Kubernetes namespace
│   ├── 2024/06/24/               # Date-based organization
│   │   ├── production_20240624_143022_abc123.txt
│   │   ├── production_20240624_143155_def456.txt
│   │   └── production_20240624_144301_ghi789.txt
├── team-alpha-backend/           # Team namespace
│   ├── 2024/06/24/
│   │   └── team-alpha-backend_20240624_150000_jkl012.txt
├── customer-acme-corp/           # Customer namespace
│   ├── 2024/06/24/
│   │   └── customer-acme-corp_20240624_151500_mno345.txt
├── kube-system/                  # System namespace
│   ├── 2024/06/24/
│   │   └── kube-system_20240624_152000_pqr678.txt
└── NO_NAMESPACE/                 # Fallback for undetected namespaces
    ├── 2024/06/24/
    │   └── NO_NAMESPACE_20240624_153000_stu901.txt
```

### File Naming Convention
- **Format**: `{namespace}_{YYYYMMDD}_{HHMMSS}_{uuid}.txt`
- **Metadata**: Rich metadata stored in S3 object tags and headers
- **Content**: Full AI response with conversation context

## 🔧 Monitoring & Health Checks

### Built-in Health Monitoring
```bash
# Check system health
curl http://localhost:3000/health

# Get detailed metrics
npm run health:check

# Monitor performance
npm run metrics:show
```

### Health Check Components
- ✅ **Memory Usage**: Monitors heap usage and garbage collection
- ✅ **S3 Connectivity**: Tests bucket access and permissions
- ✅ **Error Rates**: Tracks error frequency and patterns
- ✅ **Performance**: Monitors response times and throughput

### Error Handling Features
- 🔄 **Automatic Retry**: Intelligent retry with exponential backoff
- 🚨 **Circuit Breaker**: Prevents cascading failures
- 📊 **Error Reporting**: Automated error tracking and alerting
- 🔍 **Error Deduplication**: Groups similar errors for analysis

## 🛠️ Development

### Development Commands
```bash
# Development mode with hot reload
npm run dev

# Run tests
npm test

# Run tests in watch mode
npm run test:watch

# Run tests with coverage
npm run test:coverage

# Lint code
npm run lint

# Build for production
npm run build

# Configuration management
npm run config:validate
npm run config:show
npm run config:env

# Health and monitoring
npm run health:check
npm run metrics:show

# Demonstrations
npm run demo:namespace-flexibility
```

### Development Features
- 🔄 **Hot Reload**: Automatic server restart on changes
- 🧪 **Comprehensive Testing**: Unit, integration, and performance tests
- 📊 **Code Coverage**: Detailed coverage reports
- 🔍 **Debugging**: Enhanced debugging with source maps
- 📝 **Linting**: ESLint with TypeScript support

## 📚 Documentation

### Core Documentation
- 📖 **[Configuration Guide](docs/CONFIGURATION.md)** - Comprehensive configuration management
- 🚀 **[Deployment Guide](docs/DEPLOYMENT.md)** - Production deployment strategies
- 🔧 **[API Documentation](docs/API.md)** - Complete API reference
- 🆘 **[Troubleshooting Guide](docs/TROUBLESHOOTING.md)** - Common issues and solutions
- 🏗️ **[Architecture Documentation](docs/ARCHITECTURE.md)** - System design and patterns

### Quick Links
- [Environment Setup](docs/CONFIGURATION.md#quick-start)
- [Kubernetes Deployment](docs/DEPLOYMENT.md#kubernetes-deployment)
- [Health Monitoring](docs/API.md#health-check-endpoints)
- [Error Handling](docs/TROUBLESHOOTING.md#common-issues)
- [Performance Tuning](docs/ARCHITECTURE.md#performance-architecture)

## 🤝 Contributing

We welcome contributions! Please see our contributing guidelines:

1. **Fork the repository**
2. **Create a feature branch**: `git checkout -b feature/amazing-feature`
3. **Make your changes** with tests
4. **Run the test suite**: `npm test`
5. **Submit a pull request**

### Development Setup
```bash
git clone <repository-url>
cd s3-mcp-server
npm install
cp .env.example .env.development
npm run config:validate
npm run dev
```

## 📊 Project Status

- ✅ **Core MCP Integration**: Complete
- ✅ **S3 Storage**: Complete with retry logic
- ✅ **Namespace Detection**: Unlimited namespace support
- ✅ **Error Handling**: Production-ready error handling
- ✅ **Health Monitoring**: Real-time system monitoring
- ✅ **Configuration Management**: Multi-environment support
- ✅ **Documentation**: Comprehensive guides
- 🔄 **Performance Optimization**: Ongoing improvements

## 📄 License

MIT License - see [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- [Model Context Protocol](https://modelcontextprotocol.io/) - Protocol specification
- [AWS SDK](https://aws.amazon.com/sdk-for-javascript/) - AWS integration
- [TypeScript](https://www.typescriptlang.org/) - Type safety and development experience
