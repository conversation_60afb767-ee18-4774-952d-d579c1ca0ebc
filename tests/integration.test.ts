/**
 * Integration tests for the complete S3 MCP Server system
 */

import { AIResponseInterceptor } from '../src/interceptor';
import { S3Client } from '../src/s3-client';
import { ResponseHandler } from '../src/response-handler';
import { healthMonitor, createDefaultHealthChecks } from '../src/error-handling/health-monitor';
import { errorReporter } from '../src/error-handling/error-reporter';
import {
  KubernetesResourceMiddleware,
  KubernetesErrorMiddleware,
  KubernetesMetricsMiddleware
} from '../src/middleware/kubernetes-middleware';

// Mock AWS SDK
jest.mock('@aws-sdk/client-s3');

const mockS3Client = {
  send: jest.fn(),
};

const MockedS3Client = jest.mocked(require('@aws-sdk/client-s3').S3Client);

describe('S3 MCP Server Integration Tests', () => {
  let interceptor: AIResponseInterceptor;
  let s3Client: S3Client;
  let responseHandler: ResponseHandler;

  beforeEach(() => {
    jest.clearAllMocks();
    MockedS3Client.mockImplementation(() => mockS3Client as any);
    
    interceptor = new AIResponseInterceptor();
    s3Client = new S3Client();
    responseHandler = new ResponseHandler();

    // Setup middleware
    interceptor.addMiddleware(new KubernetesResourceMiddleware());
    interceptor.addMiddleware(new KubernetesErrorMiddleware());
    interceptor.addMiddleware(new KubernetesMetricsMiddleware());

    // Reset error reporter
    errorReporter['reports'].clear();
    errorReporter['lastAlerts'].clear();
  });

  afterEach(() => {
    healthMonitor.stop();
  });

  describe('End-to-End AI Response Processing', () => {
    it('should process kubectl log analysis request', async () => {
      mockS3Client.send.mockResolvedValue({ ETag: '"test-etag"' });

      const content = `
        kubectl logs -n production web-server-12345-abcde shows the following errors:
        
        2024-01-01 10:00:00 ERROR: Database connection failed
        2024-01-01 10:00:01 ERROR: CrashLoopBackOff detected
        2024-01-01 10:00:02 WARN: High memory usage detected
        
        The pod web-server-12345-abcde is experiencing issues.
      `;

      const context = {
        conversationId: 'conv-123',
        requestType: 'pod_log_analysis',
        metadata: { userId: 'user-123' },
      };

      // Process through interceptor
      const aiResponse = await interceptor.interceptResponse(content, context);

      // Verify namespace detection
      expect(aiResponse.namespace.namespace).toBe('production');
      expect(aiResponse.namespace.confidence).toBe('high');
      expect(aiResponse.namespace.source).toBe('command');

      // Verify pod detection
      expect(aiResponse.podNames).toContain('web-server-12345-abcde');

      // Verify error detection
      expect(aiResponse.metadata?.kubernetesErrors).toBeDefined();
      expect(aiResponse.metadata?.kubernetesErrors).toHaveLength(1);
      expect(aiResponse.metadata?.kubernetesErrors[0]?.type).toBe('CrashLoopBackOff');

      // Upload to S3
      const uploadResult = await s3Client.uploadResponse(aiResponse);

      expect(uploadResult.success).toBe(true);
      expect(uploadResult.key).toContain('production');
      expect(uploadResult.key).toContain('2024');
      expect(mockS3Client.send).toHaveBeenCalledTimes(1);
    });

    it('should handle namespace fallback correctly', async () => {
      mockS3Client.send.mockResolvedValue({ ETag: '"test-etag"' });

      const content = `
        General Kubernetes cluster analysis without specific namespace context.
        The cluster is experiencing some performance issues.
      `;

      const context = {
        conversationId: 'conv-456',
        requestType: 'general_analysis',
      };

      const aiResponse = await interceptor.interceptResponse(content, context);

      // Should fall back to default namespace
      expect(aiResponse.namespace.namespace).toBe('DEV_NAMESPACE');
      expect(aiResponse.namespace.confidence).toBe('low');
      expect(aiResponse.namespace.source).toBe('default');

      const uploadResult = await s3Client.uploadResponse(aiResponse);

      expect(uploadResult.success).toBe(true);
      expect(uploadResult.key).toContain('DEV_NAMESPACE');
    });

    it('should process multiple namespaces in content', async () => {
      mockS3Client.send.mockResolvedValue({ ETag: '"test-etag"' });

      const content = `
        kubectl get pods -n kube-system shows coredns issues.
        Also checking kubectl logs -n monitoring prometheus-server.
        The staging environment in namespace staging-env is also affected.
      `;

      const context = {
        conversationId: 'conv-789',
        requestType: 'multi_namespace_analysis',
      };

      const aiResponse = await interceptor.interceptResponse(content, context);

      // Should detect the first namespace mentioned
      expect(aiResponse.namespace.namespace).toBe('kube-system');
      expect(aiResponse.namespace.confidence).toBe('high');

      const uploadResult = await s3Client.uploadResponse(aiResponse);
      expect(uploadResult.success).toBe(true);
    });
  });

  describe('Error Handling Integration', () => {
    it('should handle S3 upload failures gracefully', async () => {
      const s3Error = new Error('AccessDenied');
      mockS3Client.send.mockRejectedValue(s3Error);

      const content = 'kubectl logs -n test-namespace pod-123';
      const aiResponse = await interceptor.interceptResponse(content);

      const uploadResult = await s3Client.uploadResponse(aiResponse);

      expect(uploadResult.success).toBe(false);
      expect(uploadResult.error).toContain('AccessDenied');

      // Check that error was reported
      const reports = errorReporter.getReports();
      expect(reports.length).toBeGreaterThan(0);
    });

    it('should retry transient S3 errors', async () => {
      const transientError = new Error('ServiceUnavailable');
      mockS3Client.send
        .mockRejectedValueOnce(transientError)
        .mockRejectedValueOnce(transientError)
        .mockResolvedValueOnce({ ETag: '"test-etag"' });

      const content = 'kubectl logs -n test-namespace pod-123';
      const aiResponse = await interceptor.interceptResponse(content);

      const uploadResult = await s3Client.uploadResponse(aiResponse);

      expect(uploadResult.success).toBe(true);
      expect(mockS3Client.send).toHaveBeenCalledTimes(3);
    });

    it('should handle middleware processing errors', async () => {
      // Create a middleware that throws an error
      const faultyMiddleware = {
        name: 'faulty-middleware',
        priority: 50,
        process: jest.fn().mockRejectedValue(new Error('Middleware error')),
      };

      interceptor.addMiddleware(faultyMiddleware);

      const content = 'kubectl logs -n test-namespace pod-123';
      
      // Should not throw, but handle the error gracefully
      const aiResponse = await interceptor.interceptResponse(content);
      
      expect(aiResponse).toBeDefined();
      expect(aiResponse.content).toBe(content);
    });
  });

  describe('Health Monitoring Integration', () => {
    it('should register and run health checks', async () => {
      const healthChecks = createDefaultHealthChecks(s3Client);
      healthChecks.forEach(check => healthMonitor.registerCheck(check));

      const systemHealth = await healthMonitor.getSystemHealth();

      expect(systemHealth.overall).toMatch(/^(healthy|degraded|unhealthy)$/);
      expect(systemHealth.checks).toHaveProperty('memory');
      expect(systemHealth.checks).toHaveProperty('s3_connectivity');
      expect(systemHealth.uptime).toBeGreaterThan(0);
    });

    it('should detect S3 connectivity issues', async () => {
      mockS3Client.send.mockRejectedValue(new Error('Connection failed'));

      const healthChecks = createDefaultHealthChecks(s3Client);
      const s3Check = healthChecks.find(check => check.name === 's3_connectivity');

      if (s3Check) {
        healthMonitor.registerCheck(s3Check);
        const result = await healthMonitor.runCheck('s3_connectivity');

        expect(result.status).toBe('unhealthy');
        expect(result.message).toContain('Connection failed');
      }
    });

    it('should monitor memory usage', async () => {
      const healthChecks = createDefaultHealthChecks();
      const memoryCheck = healthChecks.find(check => check.name === 'memory');

      if (memoryCheck) {
        healthMonitor.registerCheck(memoryCheck);
        const result = await healthMonitor.runCheck('memory');

        expect(result.status).toMatch(/^(healthy|degraded|unhealthy)$/);
        expect(result.details).toHaveProperty('usedMB');
        expect(result.details).toHaveProperty('totalMB');
        expect(result.details).toHaveProperty('usagePercent');
      }
    });
  });

  describe('Performance and Scalability', () => {
    it('should handle multiple concurrent requests', async () => {
      mockS3Client.send.mockResolvedValue({ ETag: '"test-etag"' });

      const requests = Array.from({ length: 10 }, (_, i) => ({
        content: `kubectl logs -n namespace-${i} pod-${i}`,
        context: {
          conversationId: `conv-${i}`,
          requestType: 'concurrent_test',
        },
      }));

      const promises = requests.map(async (req) => {
        const aiResponse = await interceptor.interceptResponse(req.content, req.context);
        return s3Client.uploadResponse(aiResponse);
      });

      const results = await Promise.all(promises);

      expect(results).toHaveLength(10);
      expect(results.every(r => r.success)).toBe(true);
      expect(mockS3Client.send).toHaveBeenCalledTimes(10);
    });

    it('should track performance metrics', async () => {
      mockS3Client.send.mockResolvedValue({ ETag: '"test-etag"' });

      const content = 'kubectl logs -n performance-test pod-123';
      const aiResponse = await interceptor.interceptResponse(content);
      const uploadResult = await s3Client.uploadResponse(aiResponse);

      expect(uploadResult.uploadTime).toBeGreaterThan(0);
      expect(uploadResult.size).toBeGreaterThan(0);

      const stats = s3Client.getStats();
      expect(stats.totalUploads).toBe(1);
      expect(stats.successfulUploads).toBe(1);
      expect(stats.averageUploadTime).toBeGreaterThan(0);
    });

    it('should handle large content efficiently', async () => {
      mockS3Client.send.mockResolvedValue({ ETag: '"test-etag"' });

      // Create large content (1MB)
      const largeContent = 'kubectl logs -n large-test pod-123\n' + 'x'.repeat(1024 * 1024);
      
      const aiResponse = await interceptor.interceptResponse(largeContent);
      const uploadResult = await s3Client.uploadResponse(aiResponse);

      expect(uploadResult.success).toBe(true);
      expect(uploadResult.size).toBeGreaterThan(1024 * 1024);
    });
  });

  describe('Configuration and Environment', () => {
    it('should work with different environment configurations', async () => {
      // Test with different namespace prefixes
      const originalEnv = process.env['DEFAULT_NAMESPACE_PREFIX'];
      process.env['DEFAULT_NAMESPACE_PREFIX'] = 'TEST_PREFIX';

      try {
        const handler = new ResponseHandler();
        const content = 'General analysis without namespace';
        const response = await handler.processResponse(content);

        expect(response.namespace.namespace).toBe('TEST_PREFIX');
      } finally {
        if (originalEnv) {
          process.env['DEFAULT_NAMESPACE_PREFIX'] = originalEnv;
        } else {
          delete process.env['DEFAULT_NAMESPACE_PREFIX'];
        }
      }
    });

    it('should handle missing configuration gracefully', async () => {
      // Test behavior when optional config is missing
      const content = 'kubectl logs -n test pod-123';
      const aiResponse = await interceptor.interceptResponse(content);

      expect(aiResponse).toBeDefined();
      expect(aiResponse.namespace.namespace).toBe('test');
    });
  });

  describe('Data Integrity and Validation', () => {
    it('should preserve all response data through processing pipeline', async () => {
      mockS3Client.send.mockResolvedValue({ ETag: '"test-etag"' });

      const originalContent = `
        kubectl logs -n production web-server-12345 shows:
        ERROR: Database connection failed
        Pod web-server-12345 is in CrashLoopBackOff state
      `;

      const context = {
        conversationId: 'conv-integrity-test',
        requestType: 'integrity_test',
        metadata: {
          userId: 'user-123',
          sessionId: 'session-456',
          customField: 'custom-value',
        },
      };

      const aiResponse = await interceptor.interceptResponse(originalContent, context);

      // Verify all data is preserved
      expect(aiResponse.content).toBe(originalContent);
      expect(aiResponse.conversationId).toBe('conv-integrity-test');
      expect(aiResponse.requestType).toBe('integrity_test');
      expect(aiResponse.metadata?.userId).toBe('user-123');
      expect(aiResponse.metadata?.sessionId).toBe('session-456');
      expect(aiResponse.metadata?.customField).toBe('custom-value');

      // Verify enhanced data is added
      expect(aiResponse.namespace.namespace).toBe('production');
      expect(aiResponse.podNames).toContain('web-server-12345');
      expect(aiResponse.metadata?.kubernetesErrors).toBeDefined();

      const uploadResult = await s3Client.uploadResponse(aiResponse);
      expect(uploadResult.success).toBe(true);
    });

    it('should generate consistent S3 keys', async () => {
      mockS3Client.send.mockResolvedValue({ ETag: '"test-etag"' });

      const content = 'kubectl logs -n consistent-test pod-123';
      const timestamp = new Date('2024-01-01T12:00:00Z');

      const aiResponse1 = await interceptor.interceptResponse(content);
      aiResponse1.timestamp = timestamp;

      const aiResponse2 = await interceptor.interceptResponse(content);
      aiResponse2.timestamp = timestamp;

      const result1 = await s3Client.uploadResponse(aiResponse1);
      const result2 = await s3Client.uploadResponse(aiResponse2);

      // Keys should have same structure but different IDs
      const key1Parts = result1.key.split('/');
      const key2Parts = result2.key.split('/');

      expect(key1Parts[0]).toBe(key2Parts[0]); // namespace
      expect(key1Parts[1]).toBe(key2Parts[1]); // year
      expect(key1Parts[2]).toBe(key2Parts[2]); // month
      expect(key1Parts[3]).toBe(key2Parts[3]); // day
      expect(key1Parts[4]).not.toBe(key2Parts[4]); // filename (different IDs)
    });
  });
});
