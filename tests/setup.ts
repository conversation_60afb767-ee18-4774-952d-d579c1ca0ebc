/**
 * Jest setup file for S3 MCP Server tests
 */

// Set test environment variables
process.env['NODE_ENV'] = 'test';
process.env['AWS_ACCESS_KEY_ID'] = 'test-access-key';
process.env['AWS_SECRET_ACCESS_KEY'] = 'test-secret-key';
process.env['AWS_REGION'] = 'us-east-1';
process.env['S3_BUCKET_NAME'] = 'test-bucket';
process.env['MCP_SERVER_PORT'] = '3001';
process.env['LOG_LEVEL'] = 'error';
process.env['ENABLE_CONSOLE_LOGGING'] = 'false';

// Mock AWS SDK
jest.mock('@aws-sdk/client-s3', () => ({
  S3Client: jest.fn().mockImplementation(() => ({
    send: jest.fn(),
  })),
  PutObjectCommand: jest.fn(),
}));

// Global test timeout
jest.setTimeout(10000);
