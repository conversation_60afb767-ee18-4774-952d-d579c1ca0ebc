/**
 * Tests for MCP Server functionality
 */

describe('MCP Server', () => {
  it('should be properly configured', () => {
    // Basic test to ensure the test suite runs
    expect(true).toBe(true);
  });

  it('should have proper TypeScript compilation', () => {
    // Test that our types are working
    const testNamespace = {
      namespace: 'test',
      confidence: 'high' as const,
      source: 'parameter' as const,
    };

    expect(testNamespace.namespace).toBe('test');
    expect(testNamespace.confidence).toBe('high');
    expect(testNamespace.source).toBe('parameter');
  });

  it('should validate namespace patterns', () => {
    // Test namespace validation logic
    const validNamespaces = ['production', 'staging', 'kube-system', 'default'];
    const invalidNamespaces = ['PRODUCTION', 'test_namespace', 'namespace with spaces'];

    validNamespaces.forEach(ns => {
      expect(ns).toMatch(/^[a-z0-9]([a-z0-9\-]*[a-z0-9])?$/);
    });

    invalidNamespaces.forEach(ns => {
      expect(ns).not.toMatch(/^[a-z0-9]([a-z0-9\-]*[a-z0-9])?$/);
    });
  });

  it('should extract kubectl commands with regex', () => {
    const content = 'kubectl logs -n kube-system my-pod-12345';
    const kubectlPattern = /kubectl\s+[^-]*-n\s+([a-z0-9\-]+)/gi;
    const match = kubectlPattern.exec(content);

    expect(match).not.toBeNull();
    expect(match![1]).toBe('kube-system');
  });

  it('should extract namespaces from log paths', () => {
    const content = 'Error in /var/log/pods/staging_my-app-12345_uuid/container.log';
    const logPathPattern = /\/var\/log\/pods\/([a-z0-9\-]+)_/gi;
    const match = logPathPattern.exec(content);

    expect(match).not.toBeNull();
    expect(match![1]).toBe('staging');
  });
});
