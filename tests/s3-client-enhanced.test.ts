/**
 * Tests for enhanced S3 client with error handling and retry logic
 */

import { S3Client } from '../src/s3-client';
import { AIResponse } from '../src/types';
import { PutObjectCommand, HeadBucketCommand } from '@aws-sdk/client-s3';

// Mock AWS SDK
jest.mock('@aws-sdk/client-s3');

const mockS3Client = {
  send: jest.fn(),
};

const MockedS3Client = jest.mocked(require('@aws-sdk/client-s3').S3Client);
const MockedPutObjectCommand = jest.mocked(PutObjectCommand);
const MockedHeadBucketCommand = jest.mocked(HeadBucketCommand);

describe('Enhanced S3 Client', () => {
  let s3Client: S3Client;
  let mockAIResponse: AIResponse;

  beforeEach(() => {
    jest.clearAllMocks();
    MockedS3Client.mockImplementation(() => mockS3Client as any);
    
    s3Client = new S3Client();
    
    mockAIResponse = {
      id: 'test-response-123',
      timestamp: new Date('2024-01-01T00:00:00Z'),
      content: 'Test AI response content',
      namespace: {
        namespace: 'test-namespace',
        confidence: 'high',
        source: 'parameter',
      },
      conversationId: 'conv-123',
      requestType: 'test-request',
      podNames: ['test-pod-1', 'test-pod-2'],
      metadata: {
        testKey: 'testValue',
      },
    };
  });

  describe('uploadResponse with retry logic', () => {
    it('should successfully upload on first attempt', async () => {
      mockS3Client.send.mockResolvedValueOnce({
        ETag: '"test-etag"',
      });

      const result = await s3Client.uploadResponse(mockAIResponse);

      expect(result.success).toBe(true);
      expect(result.etag).toBe('"test-etag"');
      expect(result.key).toContain('test-namespace');
      expect(result.key).toContain('2024/01/01');
      expect(mockS3Client.send).toHaveBeenCalledTimes(1);
    });

    it('should retry on retryable errors', async () => {
      const retryableError = new Error('RequestTimeout');
      mockS3Client.send
        .mockRejectedValueOnce(retryableError)
        .mockRejectedValueOnce(retryableError)
        .mockResolvedValueOnce({ ETag: '"test-etag"' });

      const result = await s3Client.uploadResponse(mockAIResponse);

      expect(result.success).toBe(true);
      expect(mockS3Client.send).toHaveBeenCalledTimes(3);
    });

    it('should fail after max retry attempts', async () => {
      const persistentError = new Error('ServiceUnavailable');
      mockS3Client.send.mockRejectedValue(persistentError);

      const result = await s3Client.uploadResponse(mockAIResponse);

      expect(result.success).toBe(false);
      expect(result.error).toContain('ServiceUnavailable');
      expect(mockS3Client.send).toHaveBeenCalledTimes(3); // Initial + 2 retries
    });

    it('should not retry on non-retryable errors', async () => {
      const nonRetryableError = new Error('AccessDenied');
      mockS3Client.send.mockRejectedValue(nonRetryableError);

      const result = await s3Client.uploadResponse(mockAIResponse);

      expect(result.success).toBe(false);
      expect(result.error).toContain('AccessDenied');
      expect(mockS3Client.send).toHaveBeenCalledTimes(1); // No retries
    });

    it('should handle circuit breaker opening', async () => {
      const error = new Error('ServiceUnavailable');
      mockS3Client.send.mockRejectedValue(error);

      // Trigger multiple failures to open circuit breaker
      const results = [];
      for (let i = 0; i < 6; i++) {
        const result = await s3Client.uploadResponse(mockAIResponse);
        results.push(result);
      }

      // All should fail
      expect(results.every(r => !r.success)).toBe(true);

      // Should have some calls, but circuit breaker should reduce total attempts
      expect(mockS3Client.send).toHaveBeenCalled();
    });

    it('should include performance metrics', async () => {
      mockS3Client.send.mockResolvedValueOnce({
        ETag: '"test-etag"',
      });

      const result = await s3Client.uploadResponse(mockAIResponse);

      expect(result.success).toBe(true);
      expect(result.uploadTime).toBeGreaterThan(0);
      expect(result.size).toBeGreaterThan(0);
    });

    it('should handle upload options correctly', async () => {
      mockS3Client.send.mockResolvedValueOnce({
        ETag: '"test-etag"',
      });

      const options = {
        contentType: 'application/json',
        storageClass: 'STANDARD_IA' as const,
        serverSideEncryption: 'aws:kms' as const,
        kmsKeyId: 'test-kms-key',
        tags: { Environment: 'test', Project: 'mcp' },
        metadata: { customKey: 'customValue' },
      };

      const result = await s3Client.uploadResponse(mockAIResponse, options);

      expect(result.success).toBe(true);
      expect(mockS3Client.send).toHaveBeenCalledWith(
        expect.objectContaining({
          input: expect.objectContaining({
            ContentType: 'application/json',
            StorageClass: 'STANDARD_IA',
            ServerSideEncryption: 'aws:kms',
            SSEKMSKeyId: 'test-kms-key',
            Tagging: 'Environment=test&Project=mcp',
            Metadata: expect.objectContaining({
              customKey: 'customValue',
            }),
          }),
        })
      );
    });
  });

  describe('testConnection with retry logic', () => {
    it('should successfully test connection', async () => {
      mockS3Client.send.mockResolvedValueOnce({});

      const result = await s3Client.testConnection();

      expect(result.success).toBe(true);
      expect(mockS3Client.send).toHaveBeenCalledWith(
        expect.any(MockedHeadBucketCommand)
      );
    });

    it('should retry connection test on failure', async () => {
      const error = new Error('Throttling');
      mockS3Client.send
        .mockRejectedValueOnce(error)
        .mockResolvedValueOnce({});

      const result = await s3Client.testConnection();

      expect(result.success).toBe(true);
      expect(mockS3Client.send).toHaveBeenCalledTimes(2);
    });

    it('should fail connection test after retries', async () => {
      const error = new Error('NoSuchBucket');
      mockS3Client.send.mockRejectedValue(error);

      const result = await s3Client.testConnection();

      expect(result.success).toBe(false);
      expect(result.error).toContain('NoSuchBucket');
    });
  });

  describe('batch upload with error handling', () => {
    it('should upload multiple responses', async () => {
      mockS3Client.send.mockResolvedValue({
        ETag: '"test-etag"',
      });

      const responses = [
        { ...mockAIResponse, id: 'response-1' },
        { ...mockAIResponse, id: 'response-2' },
        { ...mockAIResponse, id: 'response-3' },
      ];

      const results = await s3Client.uploadBatch(responses);

      expect(results).toHaveLength(3);
      expect(results.every(r => r.success)).toBe(true);
      expect(mockS3Client.send).toHaveBeenCalledTimes(3);
    });

    it('should handle partial failures in batch upload', async () => {
      mockS3Client.send
        .mockResolvedValueOnce({ ETag: '"etag-1"' })
        .mockRejectedValueOnce(new Error('Upload failed'))
        .mockResolvedValueOnce({ ETag: '"etag-3"' });

      const responses = [
        { ...mockAIResponse, id: 'response-1' },
        { ...mockAIResponse, id: 'response-2' },
        { ...mockAIResponse, id: 'response-3' },
      ];

      const results = await s3Client.uploadBatch(responses);

      expect(results).toHaveLength(3);
      expect(results[0]?.success).toBe(true);
      expect(results[1]?.success).toBe(false);
      expect(results[2]?.success).toBe(true);
    });

    it('should continue batch upload even if some uploads fail', async () => {
      mockS3Client.send
        .mockRejectedValueOnce(new Error('First upload failed'))
        .mockRejectedValueOnce(new Error('Second upload failed'))
        .mockResolvedValueOnce({ ETag: '"etag-3"' });

      const responses = [
        { ...mockAIResponse, id: 'response-1' },
        { ...mockAIResponse, id: 'response-2' },
        { ...mockAIResponse, id: 'response-3' },
      ];

      const results = await s3Client.uploadBatch(responses);

      expect(results).toHaveLength(3);
      expect(results[0]?.success).toBe(false);
      expect(results[1]?.success).toBe(false);
      expect(results[2]?.success).toBe(true);
      expect(mockS3Client.send).toHaveBeenCalledTimes(9); // 3 attempts per upload
    });
  });

  describe('statistics and monitoring', () => {
    it('should track upload statistics', async () => {
      mockS3Client.send
        .mockResolvedValueOnce({ ETag: '"etag-1"' })
        .mockRejectedValueOnce(new Error('Upload failed'))
        .mockResolvedValueOnce({ ETag: '"etag-3"' });

      await s3Client.uploadResponse(mockAIResponse);
      await s3Client.uploadResponse(mockAIResponse);
      await s3Client.uploadResponse(mockAIResponse);

      const stats = s3Client.getStats();

      expect(stats.totalUploads).toBe(3);
      expect(stats.successfulUploads).toBe(2);
      expect(stats.failedUploads).toBe(1);
      expect(stats.totalBytesUploaded).toBeGreaterThan(0);
      expect(stats.averageUploadTime).toBeGreaterThan(0);
    });

    it('should reset statistics', async () => {
      mockS3Client.send.mockResolvedValue({ ETag: '"test-etag"' });

      await s3Client.uploadResponse(mockAIResponse);
      
      let stats = s3Client.getStats();
      expect(stats.totalUploads).toBe(1);

      s3Client.resetStats();
      
      stats = s3Client.getStats();
      expect(stats.totalUploads).toBe(0);
      expect(stats.successfulUploads).toBe(0);
      expect(stats.failedUploads).toBe(0);
    });
  });

  describe('error context and logging', () => {
    it('should provide detailed error context', async () => {
      const error = new Error('Detailed S3 error');
      error.name = 'AccessDenied';
      mockS3Client.send.mockRejectedValue(error);

      const result = await s3Client.uploadResponse(mockAIResponse);

      expect(result.success).toBe(false);
      expect(result.error).toContain('AccessDenied');
      expect(result.uploadTime).toBeGreaterThan(0);
    });

    it('should handle different error types appropriately', async () => {
      const networkError = new Error('ENOTFOUND');
      const accessError = new Error('AccessDenied');
      const throttleError = new Error('SlowDown');

      // Network error should be retried
      mockS3Client.send.mockRejectedValue(networkError);
      let result = await s3Client.uploadResponse(mockAIResponse);
      expect(result.success).toBe(false);
      expect(mockS3Client.send).toHaveBeenCalledTimes(3); // Retried

      jest.clearAllMocks();

      // Access error should not be retried
      mockS3Client.send.mockRejectedValue(accessError);
      result = await s3Client.uploadResponse(mockAIResponse);
      expect(result.success).toBe(false);
      expect(mockS3Client.send).toHaveBeenCalledTimes(1); // Not retried

      jest.clearAllMocks();

      // Throttle error should be retried
      mockS3Client.send.mockRejectedValue(throttleError);
      result = await s3Client.uploadResponse(mockAIResponse);
      expect(result.success).toBe(false);
      expect(mockS3Client.send).toHaveBeenCalledTimes(3); // Retried
    });
  });
});
