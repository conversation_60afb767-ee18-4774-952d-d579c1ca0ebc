/**
 * Tests for error handling system
 */

import { MCPError, ErrorCode, ErrorSeverity, ErrorFactory, ErrorClassifier } from '../src/error-handling/error-types';
import { <PERSON>tryManager, CircuitBreaker } from '../src/error-handling/retry';
import { errorReporter } from '../src/error-handling/error-reporter';

describe('Error Handling System', () => {
  describe('MCPError', () => {
    it('should create error with all properties', () => {
      const context = { requestId: 'test-123', operation: 'test' };
      const originalError = new Error('Original error');
      
      const error = new MCPError(
        ErrorCode.S3_UPLOAD_FAILED,
        'Test error message',
        ErrorSeverity.HIGH,
        context,
        true,
        originalError
      );

      expect(error.code).toBe(ErrorCode.S3_UPLOAD_FAILED);
      expect(error.message).toBe('Test error message');
      expect(error.severity).toBe(ErrorSeverity.HIGH);
      expect(error.context.requestId).toBe('test-123');
      expect(error.isRetryable).toBe(true);
      expect(error.originalError).toBe(originalError);
      expect(error.context.timestamp).toBeInstanceOf(Date);
    });

    it('should serialize to JSON correctly', () => {
      const error = new MCPError(
        ErrorCode.CONFIG_INVALID,
        'Config error',
        ErrorSeverity.CRITICAL
      );

      const json = error.toJSON();
      
      expect(json.name).toBe('MCPError');
      expect(json.message).toBe('Config error');
      expect(json.code).toBe(ErrorCode.CONFIG_INVALID);
      expect(json.severity).toBe(ErrorSeverity.CRITICAL);
      expect(json.context).toBeDefined();
      expect(json.stack).toBeDefined();
    });
  });

  describe('ErrorFactory', () => {
    it('should create configuration errors', () => {
      const error = ErrorFactory.configurationError('Invalid config');
      
      expect(error.code).toBe(ErrorCode.CONFIG_INVALID);
      expect(error.severity).toBe(ErrorSeverity.HIGH);
      expect(error.isRetryable).toBe(false);
    });

    it('should create S3 errors with retry logic', () => {
      const originalError = new Error('RequestTimeout');
      const error = ErrorFactory.s3Error('S3 failed', {}, originalError);
      
      expect(error.code).toBe(ErrorCode.S3_UPLOAD_FAILED);
      expect(error.isRetryable).toBe(true);
      expect(error.originalError).toBe(originalError);
    });

    it('should create processing errors', () => {
      const error = ErrorFactory.processingError('Processing failed');
      
      expect(error.code).toBe(ErrorCode.RESPONSE_PROCESSING_FAILED);
      expect(error.severity).toBe(ErrorSeverity.MEDIUM);
      expect(error.isRetryable).toBe(true);
    });

    it('should create network errors', () => {
      const error = ErrorFactory.networkError('Network failed');
      
      expect(error.code).toBe(ErrorCode.NETWORK_CONNECTION_FAILED);
      expect(error.severity).toBe(ErrorSeverity.HIGH);
      expect(error.isRetryable).toBe(true);
    });

    it('should create validation errors', () => {
      const error = ErrorFactory.validationError('Validation failed');
      
      expect(error.code).toBe(ErrorCode.VALIDATION_FAILED);
      expect(error.severity).toBe(ErrorSeverity.LOW);
      expect(error.isRetryable).toBe(false);
    });
  });

  describe('ErrorClassifier', () => {
    it('should identify retryable errors', () => {
      const retryableError = new Error('Connection timeout');
      const nonRetryableError = new Error('Invalid input');
      
      expect(ErrorClassifier.isRetryable(retryableError)).toBe(true);
      expect(ErrorClassifier.isRetryable(nonRetryableError)).toBe(false);
    });

    it('should classify error severity', () => {
      const validationError = new Error('validation failed');
      const typeError = new TypeError('Type error');
      const networkError = new Error('ENOTFOUND');
      
      expect(ErrorClassifier.getSeverity(validationError)).toBe(ErrorSeverity.LOW);
      expect(ErrorClassifier.getSeverity(typeError)).toBe(ErrorSeverity.HIGH);
      expect(ErrorClassifier.getSeverity(networkError)).toBe(ErrorSeverity.HIGH);
    });

    it('should determine if error should alert', () => {
      const lowSeverityError = new Error('validation failed');
      const highSeverityError = new TypeError('Type error');
      
      expect(ErrorClassifier.shouldAlert(lowSeverityError)).toBe(false);
      expect(ErrorClassifier.shouldAlert(highSeverityError)).toBe(true);
    });
  });

  describe('RetryManager', () => {
    it('should retry failed operations', async () => {
      let attempts = 0;
      const operation = jest.fn().mockImplementation(() => {
        attempts++;
        if (attempts < 3) {
          throw new Error('Temporary failure');
        }
        return 'success';
      });

      const result = await RetryManager.execute(operation, {
        maxAttempts: 3,
        baseDelay: 10,
      });

      expect(result.success).toBe(true);
      expect(result.result).toBe('success');
      expect(result.attempts).toBe(3);
      expect(operation).toHaveBeenCalledTimes(3);
    });

    it('should fail after max attempts', async () => {
      const operation = jest.fn().mockRejectedValue(new Error('Persistent failure'));

      const result = await RetryManager.execute(operation, {
        maxAttempts: 2,
        baseDelay: 10,
      });

      expect(result.success).toBe(false);
      expect(result.error).toBeInstanceOf(Error);
      expect(result.attempts).toBe(2);
      expect(operation).toHaveBeenCalledTimes(2);
    });

    it('should not retry non-retryable errors', async () => {
      const operation = jest.fn().mockRejectedValue(new Error('Validation error'));

      const result = await RetryManager.execute(operation, {
        maxAttempts: 3,
        baseDelay: 10,
        retryCondition: () => false,
      });

      expect(result.success).toBe(false);
      expect(result.attempts).toBe(1);
      expect(operation).toHaveBeenCalledTimes(1);
    });

    it('should call onRetry callback', async () => {
      let attempts = 0;
      const operation = jest.fn().mockImplementation(() => {
        attempts++;
        if (attempts < 2) {
          throw new Error('Temporary failure');
        }
        return 'success';
      });

      const onRetry = jest.fn();

      await RetryManager.execute(operation, {
        maxAttempts: 2,
        baseDelay: 10,
        onRetry,
      });

      expect(onRetry).toHaveBeenCalledTimes(1);
      expect(onRetry).toHaveBeenCalledWith(expect.any(Error), 1);
    });
  });

  describe('CircuitBreaker', () => {
    it('should allow operations when closed', async () => {
      const circuitBreaker = new CircuitBreaker(3, 1000, 'test');
      const operation = jest.fn().mockResolvedValue('success');

      const result = await circuitBreaker.execute(operation);

      expect(result).toBe('success');
      expect(operation).toHaveBeenCalledTimes(1);
    });

    it('should open after failure threshold', async () => {
      const circuitBreaker = new CircuitBreaker(2, 1000, 'test');
      const operation = jest.fn().mockRejectedValue(new Error('Failure'));

      // First two failures should execute
      await expect(circuitBreaker.execute(operation)).rejects.toThrow();
      await expect(circuitBreaker.execute(operation)).rejects.toThrow();

      // Third attempt should be blocked by circuit breaker
      await expect(circuitBreaker.execute(operation)).rejects.toThrow('Circuit breaker test is OPEN');

      expect(operation).toHaveBeenCalledTimes(2);
    });

    it('should transition to half-open after timeout', async () => {
      const circuitBreaker = new CircuitBreaker(1, 50, 'test'); // 50ms timeout
      const operation = jest.fn()
        .mockRejectedValueOnce(new Error('Failure'))
        .mockResolvedValueOnce('success');

      // Trigger circuit breaker to open
      await expect(circuitBreaker.execute(operation)).rejects.toThrow();

      // Wait for timeout
      await new Promise(resolve => setTimeout(resolve, 60));

      // Should now allow one attempt (half-open)
      const result = await circuitBreaker.execute(operation);
      expect(result).toBe('success');
    });
  });

  describe('ErrorReporter', () => {
    beforeEach(() => {
      // Reset error reporter state
      errorReporter['reports'].clear();
      errorReporter['lastAlerts'].clear();
    });

    it('should report and deduplicate errors', async () => {
      const error1 = new Error('Test error');
      const error2 = new Error('Test error');

      await errorReporter.reportError(error1, { operation: 'test' });
      await errorReporter.reportError(error2, { operation: 'test' });

      const reports = errorReporter.getReports();
      expect(reports).toHaveLength(1);
      expect(reports[0]?.count).toBe(2);
    });

    it('should generate different fingerprints for different errors', async () => {
      const error1 = new Error('Error 1');
      const error2 = new Error('Error 2');

      await errorReporter.reportError(error1, { operation: 'test1' });
      await errorReporter.reportError(error2, { operation: 'test2' });

      const reports = errorReporter.getReports();
      expect(reports).toHaveLength(2);
      expect(reports[0]?.fingerprint).not.toBe(reports[1]?.fingerprint);
    });

    it('should provide error statistics', async () => {
      const criticalError = ErrorFactory.configurationError('Critical error');
      criticalError.severity = ErrorSeverity.CRITICAL;

      await errorReporter.reportError(criticalError);
      await errorReporter.reportError(new Error('Regular error'));

      const stats = errorReporter.getStatistics();
      expect(stats.totalReports).toBe(2);
      expect(stats.uniqueErrors).toBe(2);
      expect(stats.criticalErrors).toBe(1);
    });

    it('should export reports for analysis', async () => {
      const error = new Error('Test error');
      await errorReporter.reportError(error, { operation: 'test' });

      const exported = errorReporter.exportReports();
      expect(exported).toHaveLength(1);
      expect(exported[0]).toHaveProperty('id');
      expect(exported[0]).toHaveProperty('timestamp');
      expect(exported[0]).toHaveProperty('error');
      expect(exported[0]).toHaveProperty('context');
    });
  });
});
