/**
 * Tests for AI Response Interceptor functionality
 */

describe('AI Response Interceptor', () => {
  describe('Namespace Detection', () => {
    it('should detect namespace from kubectl commands', () => {
      const content = 'kubectl logs -n production my-pod-12345';
      const kubectlPattern = /kubectl\s+[^-]*-n\s+([a-z0-9\-]+)/gi;
      const match = kubectlPattern.exec(content);
      
      expect(match).not.toBeNull();
      expect(match![1]).toBe('production');
    });

    it('should detect namespace from log paths', () => {
      const content = 'Error in /var/log/pods/staging_my-app-12345_uuid/container.log';
      const logPathPattern = /\/var\/log\/pods\/([a-z0-9\-]+)_/gi;
      const match = logPathPattern.exec(content);
      
      expect(match).not.toBeNull();
      expect(match![1]).toBe('staging');
    });

    it('should detect namespace from content patterns', () => {
      const content = 'Pod is running in namespace kube-system';
      const namespacePattern = /in\s+namespace\s+([a-z0-9\-]+)/gi;
      const match = namespacePattern.exec(content);
      
      expect(match).not.toBeNull();
      expect(match![1]).toBe('kube-system');
    });
  });

  describe('Pod Name Extraction', () => {
    it('should extract standard Kubernetes pod names', () => {
      const content = 'Pod my-app-12345-abcde is failing';
      const podPattern = /\b([a-z0-9\-]+)-[a-z0-9]{5}-[a-z0-9]{5}\b/gi;
      const match = podPattern.exec(content);

      expect(match).not.toBeNull();
      expect(match![0]).toBe('my-app-12345-abcde');
    });

    it('should extract pod names from kubectl output', () => {
      const content = `
NAME                     READY   STATUS    RESTARTS   AGE
web-server-123           1/1     Running   0          1d
api-gateway-456          1/1     Running   0          2d
      `;
      const podPattern = /^([a-z0-9\-]+)\s+\d+\/\d+/gm;
      const matches = Array.from(content.matchAll(podPattern));
      
      expect(matches).toHaveLength(2);
      expect(matches[0]?.[1]).toBe('web-server-123');
      expect(matches[1]?.[1]).toBe('api-gateway-456');
    });
  });

  describe('Kubernetes Resource Detection', () => {
    it('should detect resource references', () => {
      const content = 'Deployment my-deployment is not ready';
      const resourcePattern = /(Deployment|Service|Pod)\s+([a-z0-9\-]+)/gi;
      const match = resourcePattern.exec(content);
      
      expect(match).not.toBeNull();
      expect(match![1]).toBe('Deployment');
      expect(match![2]).toBe('my-deployment');
    });

    it('should detect kubectl resource format', () => {
      const content = 'kubectl get pod/my-pod-123';
      const resourcePattern = /(\w+)\/([a-z0-9\-]+)/gi;
      const match = resourcePattern.exec(content);
      
      expect(match).not.toBeNull();
      expect(match![1]).toBe('pod');
      expect(match![2]).toBe('my-pod-123');
    });
  });

  describe('Error Pattern Detection', () => {
    it('should detect CrashLoopBackOff errors', () => {
      const content = 'Pod is in CrashLoopBackOff state';
      const errorPattern = /CrashLoopBackOff/gi;
      const match = errorPattern.exec(content);
      
      expect(match).not.toBeNull();
      expect(match?.[0]).toBe('CrashLoopBackOff');
    });

    it('should detect ImagePullBackOff errors', () => {
      const content = 'Failed to pull image: ImagePullBackOff';
      const errorPattern = /ImagePullBackOff/gi;
      const match = errorPattern.exec(content);
      
      expect(match).not.toBeNull();
      expect(match?.[0]).toBe('ImagePullBackOff');
    });

    it('should detect OOMKilled errors', () => {
      const content = 'Container was OOMKilled due to memory limit';
      const errorPattern = /OOMKilled/gi;
      const match = errorPattern.exec(content);
      
      expect(match).not.toBeNull();
      expect(match?.[0]).toBe('OOMKilled');
    });
  });

  describe('Metrics Extraction', () => {
    it('should extract CPU metrics', () => {
      const content = 'CPU usage: 250 mcores';
      const cpuPattern = /(\d+(?:\.\d+)?)\s*(m?cores?|cpu)/gi;
      const match = cpuPattern.exec(content);

      expect(match).not.toBeNull();
      expect(parseFloat(match![1] || '0')).toBe(250);
      expect(match![2]).toBe('mcores');
    });

    it('should extract memory metrics', () => {
      const content = 'Memory usage: 512Mi';
      const memoryPattern = /(\d+(?:\.\d+)?)\s*(Mi|Gi|Ki|MB|GB|KB)/gi;
      const match = memoryPattern.exec(content);
      
      expect(match).not.toBeNull();
      expect(parseFloat(match![1] || '0')).toBe(512);
      expect(match![2]).toBe('Mi');
    });

    it('should extract percentage metrics', () => {
      const content = 'CPU utilization at 85%';
      const percentagePattern = /(\d+(?:\.\d+)?)%/gi;
      const match = percentagePattern.exec(content);
      
      expect(match).not.toBeNull();
      expect(parseFloat(match![1] || '0')).toBe(85);
    });
  });

  describe('Namespace Validation', () => {
    it('should validate correct namespace format', () => {
      const validNamespaces = ['production', 'staging', 'kube-system', 'default', 'my-app-123'];
      const namespaceRegex = /^[a-z0-9]([a-z0-9\-]*[a-z0-9])?$/;
      
      validNamespaces.forEach(ns => {
        expect(ns).toMatch(namespaceRegex);
        expect(ns.length).toBeLessThanOrEqual(63);
      });
    });

    it('should reject invalid namespace formats', () => {
      const invalidNamespaces = ['PRODUCTION', 'test_namespace', 'namespace with spaces', '-invalid', 'invalid-'];
      const namespaceRegex = /^[a-z0-9]([a-z0-9\-]*[a-z0-9])?$/;
      
      invalidNamespaces.forEach(ns => {
        expect(ns).not.toMatch(namespaceRegex);
      });
    });
  });

  describe('Conversation Context', () => {
    it('should generate valid UUIDs for conversation IDs', () => {
      const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;

      // Simulate UUID generation (we can't import uuid in this test environment)
      const mockUuid = '123e4567-e89b-12d3-a456-************';
      expect(mockUuid).toMatch(uuidRegex);
    });

    it('should handle conversation state updates', () => {
      const conversationState = {
        id: 'test-conversation',
        namespace: 'production',
        podNames: ['web-1', 'web-2'],
        startTime: new Date(),
        lastActivity: new Date(),
        messageCount: 5,
        metadata: { requestType: 'pod_log_analysis' },
      };

      expect(conversationState.id).toBe('test-conversation');
      expect(conversationState.namespace).toBe('production');
      expect(conversationState.podNames).toHaveLength(2);
      expect(conversationState.messageCount).toBe(5);
    });
  });

  describe('Streaming Support', () => {
    it('should handle response chunks', () => {
      const chunk = {
        id: 'chunk-1',
        content: 'This is a chunk of streaming response',
        isComplete: false,
        chunkIndex: 0,
        timestamp: new Date(),
      };

      expect(chunk.id).toBe('chunk-1');
      expect(chunk.isComplete).toBe(false);
      expect(chunk.chunkIndex).toBe(0);
      expect(chunk.content).toContain('streaming response');
    });

    it('should identify complete streams', () => {
      const finalChunk = {
        id: 'chunk-final',
        content: 'Final chunk',
        isComplete: true,
        chunkIndex: 5,
        totalChunks: 6,
        timestamp: new Date(),
      };

      expect(finalChunk.isComplete).toBe(true);
      expect(finalChunk.totalChunks).toBe(6);
      expect(finalChunk.chunkIndex).toBe(5);
    });
  });
});
