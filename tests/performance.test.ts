/**
 * Performance and load tests for the S3 MCP Server
 */

import { AIResponseInterceptor } from '../src/interceptor';
import { S3Client } from '../src/s3-client';
import { ResponseHandler } from '../src/response-handler';
import {
  KubernetesResourceMiddleware,
  KubernetesErrorMiddleware,
  KubernetesMetricsMiddleware
} from '../src/middleware/kubernetes-middleware';

// Mock AWS SDK
jest.mock('@aws-sdk/client-s3');

const mockS3Client = {
  send: jest.fn(),
};

const MockedS3Client = jest.mocked(require('@aws-sdk/client-s3').S3Client);

describe('Performance Tests', () => {
  let interceptor: AIResponseInterceptor;
  let s3Client: S3Client;
  let responseHandler: ResponseHandler;

  beforeEach(() => {
    jest.clearAllMocks();
    MockedS3Client.mockImplementation(() => mockS3Client as any);
    
    interceptor = new AIResponseInterceptor();
    s3Client = new S3Client();
    responseHandler = new ResponseHandler();

    // Setup middleware
    interceptor.addMiddleware(new KubernetesResourceMiddleware());
    interceptor.addMiddleware(new KubernetesErrorMiddleware());
    interceptor.addMiddleware(new KubernetesMetricsMiddleware());

    mockS3Client.send.mockResolvedValue({ ETag: '"test-etag"' });
  });

  describe('Response Processing Performance', () => {
    it('should process simple responses quickly', async () => {
      const content = 'kubectl logs -n test pod-123';
      const startTime = Date.now();

      const aiResponse = await interceptor.interceptResponse(content);

      const processingTime = Date.now() - startTime;
      expect(processingTime).toBeLessThan(100); // Should complete in under 100ms
      expect(aiResponse.namespace.namespace).toBe('test');
    });

    it('should handle large content efficiently', async () => {
      // Create 1MB of content
      const largeContent = 'kubectl logs -n large-test pod-123\n' + 'x'.repeat(1024 * 1024);
      const startTime = Date.now();

      const aiResponse = await interceptor.interceptResponse(largeContent);

      const processingTime = Date.now() - startTime;
      expect(processingTime).toBeLessThan(1000); // Should complete in under 1 second
      expect(aiResponse.content.length).toBeGreaterThan(1024 * 1024);
    });

    it('should process complex kubectl output efficiently', async () => {
      const complexContent = `
        kubectl get pods -n production --all-namespaces -o wide
        NAME                                READY   STATUS    RESTARTS   AGE     IP           NODE
        web-server-12345-abcde             1/1     Running   0          1d      **********   node-1
        api-server-67890-fghij             1/1     Running   2          2d      **********   node-2
        database-server-11111-klmno        0/1     CrashLoopBackOff   5   1h      **********   node-3
        cache-server-22222-pqrst           1/1     Running   0          3d      **********   node-1
        
        kubectl logs -n production database-server-11111-klmno shows:
        2024-01-01 10:00:00 ERROR: Connection to database failed
        2024-01-01 10:00:01 ERROR: Retrying connection...
        2024-01-01 10:00:02 ERROR: Max retries exceeded
        2024-01-01 10:00:03 FATAL: Shutting down
        
        kubectl describe pod -n production database-server-11111-klmno
        Events:
          Type     Reason     Age                From               Message
          ----     ------     ----               ----               -------
          Warning  Failed     2m (x5 over 10m)   kubelet            Error: ImagePullBackOff
          Warning  BackOff    1m (x10 over 10m)  kubelet            Back-off restarting failed container
      `;

      const startTime = Date.now();

      const aiResponse = await interceptor.interceptResponse(complexContent);

      const processingTime = Date.now() - startTime;
      expect(processingTime).toBeLessThan(500); // Should complete in under 500ms

      // Verify complex parsing worked
      expect(aiResponse.namespace.namespace).toBe('production');
      expect(aiResponse.podNames?.length).toBeGreaterThan(0);
      expect(aiResponse.metadata?.kubernetesErrors?.length).toBeGreaterThan(0);
    });
  });

  describe('Concurrent Processing Performance', () => {
    it('should handle multiple concurrent requests efficiently', async () => {
      const requestCount = 50;
      const requests = Array.from({ length: requestCount }, (_, i) => ({
        content: `kubectl logs -n namespace-${i} pod-${i}`,
        context: { conversationId: `conv-${i}` },
      }));

      const startTime = Date.now();

      const promises = requests.map(req => 
        interceptor.interceptResponse(req.content, req.context)
      );

      const results = await Promise.all(promises);

      const totalTime = Date.now() - startTime;
      const averageTime = totalTime / requestCount;

      expect(results).toHaveLength(requestCount);
      expect(averageTime).toBeLessThan(50); // Average under 50ms per request
      expect(totalTime).toBeLessThan(2000); // Total under 2 seconds

      // Verify all requests were processed correctly
      results.forEach((result, i) => {
        expect(result.namespace.namespace).toBe(`namespace-${i}`);
        expect(result.conversationId).toBe(`conv-${i}`);
      });
    });

    it('should handle concurrent S3 uploads efficiently', async () => {
      const uploadCount = 20;
      const responses = Array.from({ length: uploadCount }, (_, i) => ({
        id: `response-${i}`,
        timestamp: new Date(),
        content: `Test content ${i}`,
        namespace: {
          namespace: `test-${i}`,
          confidence: 'high' as const,
          source: 'parameter' as const,
        },
        conversationId: `conv-${i}`,
      }));

      const startTime = Date.now();

      const promises = responses.map(response => 
        s3Client.uploadResponse(response)
      );

      const results = await Promise.all(promises);

      const totalTime = Date.now() - startTime;
      const averageTime = totalTime / uploadCount;

      expect(results).toHaveLength(uploadCount);
      expect(results.every(r => r.success)).toBe(true);
      expect(averageTime).toBeLessThan(100); // Average under 100ms per upload
      expect(mockS3Client.send).toHaveBeenCalledTimes(uploadCount);
    });

    it('should maintain performance under mixed workload', async () => {
      const workloadSize = 30;
      const startTime = Date.now();

      // Mix of different request types
      const promises = Array.from({ length: workloadSize }, (_, i) => {
        const type = i % 3;
        
        if (type === 0) {
          // Simple namespace detection
          return interceptor.interceptResponse(`kubectl logs -n simple-${i} pod-${i}`);
        } else if (type === 1) {
          // Complex error analysis
          return interceptor.interceptResponse(`
            kubectl logs -n complex-${i} pod-${i} shows:
            ERROR: CrashLoopBackOff detected
            WARN: High memory usage
          `);
        } else {
          // Large content processing
          return interceptor.interceptResponse(
            `kubectl logs -n large-${i} pod-${i}\n` + 'x'.repeat(10000)
          );
        }
      });

      const results = await Promise.all(promises);

      const totalTime = Date.now() - startTime;
      const averageTime = totalTime / workloadSize;

      expect(results).toHaveLength(workloadSize);
      expect(averageTime).toBeLessThan(200); // Average under 200ms per request
      expect(totalTime).toBeLessThan(5000); // Total under 5 seconds

      // Verify all requests were processed correctly
      results.forEach((result, i) => {
        expect(result).toBeDefined();
        expect(result.content).toBeDefined();
        expect(result.namespace).toBeDefined();
      });
    });
  });

  describe('Memory Usage Performance', () => {
    it('should not leak memory during processing', async () => {
      const initialMemory = process.memoryUsage().heapUsed;
      const iterations = 100;

      for (let i = 0; i < iterations; i++) {
        const content = `kubectl logs -n memory-test-${i} pod-${i}`;
        const aiResponse = await interceptor.interceptResponse(content);
        await s3Client.uploadResponse(aiResponse);
        
        // Force garbage collection periodically
        if (i % 10 === 0 && global.gc) {
          global.gc();
        }
      }

      // Force final garbage collection
      if (global.gc) {
        global.gc();
      }

      const finalMemory = process.memoryUsage().heapUsed;
      const memoryIncrease = finalMemory - initialMemory;
      const memoryIncreasePerIteration = memoryIncrease / iterations;

      // Memory increase should be minimal (less than 1KB per iteration)
      expect(memoryIncreasePerIteration).toBeLessThan(1024);
    });

    it('should handle large batches without excessive memory usage', async () => {
      const batchSize = 50;
      const responses = Array.from({ length: batchSize }, (_, i) => ({
        id: `batch-response-${i}`,
        timestamp: new Date(),
        content: `Batch test content ${i}`,
        namespace: {
          namespace: `batch-${i}`,
          confidence: 'high' as const,
          source: 'parameter' as const,
        },
      }));

      const initialMemory = process.memoryUsage().heapUsed;

      const results = await s3Client.uploadBatch(responses);

      const finalMemory = process.memoryUsage().heapUsed;
      const memoryIncrease = finalMemory - initialMemory;

      expect(results).toHaveLength(batchSize);
      expect(results.every(r => r.success)).toBe(true);
      
      // Memory increase should be reasonable (less than 10MB for 50 items)
      expect(memoryIncrease).toBeLessThan(10 * 1024 * 1024);
    });
  });

  describe('Error Handling Performance', () => {
    it('should handle retry scenarios efficiently', async () => {
      // Setup S3 to fail twice then succeed
      mockS3Client.send
        .mockRejectedValueOnce(new Error('ServiceUnavailable'))
        .mockRejectedValueOnce(new Error('ServiceUnavailable'))
        .mockResolvedValue({ ETag: '"test-etag"' });

      const content = 'kubectl logs -n retry-test pod-123';
      const aiResponse = await interceptor.interceptResponse(content);

      const startTime = Date.now();
      const result = await s3Client.uploadResponse(aiResponse);
      const retryTime = Date.now() - startTime;

      expect(result.success).toBe(true);
      expect(mockS3Client.send).toHaveBeenCalledTimes(3);
      
      // Even with retries, should complete reasonably quickly
      expect(retryTime).toBeLessThan(5000); // Under 5 seconds with backoff
    });

    it('should fail fast on non-retryable errors', async () => {
      mockS3Client.send.mockRejectedValue(new Error('AccessDenied'));

      const content = 'kubectl logs -n fail-fast-test pod-123';
      const aiResponse = await interceptor.interceptResponse(content);

      const startTime = Date.now();
      const result = await s3Client.uploadResponse(aiResponse);
      const failTime = Date.now() - startTime;

      expect(result.success).toBe(false);
      expect(mockS3Client.send).toHaveBeenCalledTimes(1); // No retries
      expect(failTime).toBeLessThan(100); // Should fail quickly
    });
  });

  describe('Scalability Tests', () => {
    it('should maintain performance with increasing middleware', async () => {
      const baselineStart = Date.now();
      const content = 'kubectl logs -n baseline pod-123';
      await interceptor.interceptResponse(content);
      const baselineTime = Date.now() - baselineStart;

      // Add more middleware
      for (let i = 0; i < 5; i++) {
        interceptor.addMiddleware({
          name: `test-middleware-${i}`,
          priority: 100 + i,
          process: async (response) => {
            // Simulate some processing
            await new Promise(resolve => setTimeout(resolve, 1));
            return response;
          },
        });
      }

      const scaledStart = Date.now();
      await interceptor.interceptResponse(content);
      const scaledTime = Date.now() - scaledStart;

      // Performance should not degrade significantly
      expect(scaledTime).toBeLessThan(baselineTime * 3); // Less than 3x slower
    });

    it('should handle varying content sizes efficiently', async () => {
      const sizes = [100, 1000, 10000, 100000]; // Different content sizes
      const results = [];

      for (const size of sizes) {
        const content = 'kubectl logs -n size-test pod-123\n' + 'x'.repeat(size);
        
        const startTime = Date.now();
        const aiResponse = await interceptor.interceptResponse(content);
        const processingTime = Date.now() - startTime;

        results.push({ size, processingTime });
        expect(aiResponse.content.length).toBeGreaterThan(size);
      }

      // Processing time should scale reasonably with content size
      for (let i = 1; i < results.length; i++) {
        const prev = results[i - 1]!;
        const curr = results[i]!;
        const sizeRatio = curr.size / prev.size;
        const timeRatio = curr.processingTime / prev.processingTime;

        // Time should not increase faster than size (should be sub-linear)
        expect(timeRatio).toBeLessThan(sizeRatio * 2);
      }
    });
  });
});
