/**
 * Tests for health monitoring system
 */

import { HealthMonitor, HealthCheck, HealthCheckResult, createDefaultHealthChecks } from '../src/error-handling/health-monitor';

describe('Health Monitoring System', () => {
  let healthMonitor: HealthMonitor;

  beforeEach(() => {
    healthMonitor = new HealthMonitor();
  });

  afterEach(() => {
    healthMonitor.stop();
  });

  describe('HealthMonitor', () => {
    it('should register and run health checks', async () => {
      const mockCheck: HealthCheck = {
        name: 'test-check',
        check: jest.fn().mockResolvedValue({
          status: 'healthy' as const,
          message: 'All good',
          timestamp: new Date(),
          duration: 100,
        }),
        interval: 5000,
        timeout: 1000,
        critical: false,
      };

      healthMonitor.registerCheck(mockCheck);
      const result = await healthMonitor.runCheck('test-check');

      expect(result.status).toBe('healthy');
      expect(result.message).toBe('All good');
      expect(mockCheck.check).toHaveBeenCalledTimes(1);
    });

    it('should handle check failures', async () => {
      const mockCheck: HealthCheck = {
        name: 'failing-check',
        check: jest.fn().mockRejectedValue(new Error('Check failed')),
        interval: 5000,
        timeout: 1000,
        critical: true,
      };

      healthMonitor.registerCheck(mockCheck);
      const result = await healthMonitor.runCheck('failing-check');

      expect(result.status).toBe('unhealthy');
      expect(result.message).toBe('Check failed');
    });

    it('should handle check timeouts', async () => {
      const mockCheck: HealthCheck = {
        name: 'slow-check',
        check: jest.fn().mockImplementation(() => 
          new Promise(resolve => setTimeout(resolve, 2000))
        ),
        interval: 5000,
        timeout: 100, // Very short timeout
        critical: false,
      };

      healthMonitor.registerCheck(mockCheck);
      const result = await healthMonitor.runCheck('slow-check');

      expect(result.status).toBe('unhealthy');
      expect(result.message).toContain('timed out');
    });

    it('should run all checks', async () => {
      const check1: HealthCheck = {
        name: 'check-1',
        check: jest.fn().mockResolvedValue({
          status: 'healthy' as const,
          timestamp: new Date(),
          duration: 50,
        }),
        interval: 5000,
        timeout: 1000,
        critical: false,
      };

      const check2: HealthCheck = {
        name: 'check-2',
        check: jest.fn().mockResolvedValue({
          status: 'degraded' as const,
          timestamp: new Date(),
          duration: 75,
        }),
        interval: 5000,
        timeout: 1000,
        critical: false,
      };

      healthMonitor.registerCheck(check1);
      healthMonitor.registerCheck(check2);

      const results = await healthMonitor.runAllChecks();

      expect(Object.keys(results)).toHaveLength(2);
      expect(results['check-1']?.status).toBe('healthy');
      expect(results['check-2']?.status).toBe('degraded');
    });

    it('should calculate overall system health', async () => {
      const healthyCheck: HealthCheck = {
        name: 'healthy-check',
        check: jest.fn().mockResolvedValue({
          status: 'healthy' as const,
          timestamp: new Date(),
          duration: 50,
        }),
        interval: 5000,
        timeout: 1000,
        critical: false,
      };

      const degradedCheck: HealthCheck = {
        name: 'degraded-check',
        check: jest.fn().mockResolvedValue({
          status: 'degraded' as const,
          timestamp: new Date(),
          duration: 75,
        }),
        interval: 5000,
        timeout: 1000,
        critical: false,
      };

      healthMonitor.registerCheck(healthyCheck);
      healthMonitor.registerCheck(degradedCheck);

      const systemHealth = await healthMonitor.getSystemHealth();

      expect(systemHealth.overall).toBe('degraded');
      expect(systemHealth.uptime).toBeGreaterThan(0);
      expect(systemHealth.timestamp).toBeInstanceOf(Date);
    });

    it('should mark system as unhealthy if critical check fails', async () => {
      const criticalCheck: HealthCheck = {
        name: 'critical-check',
        check: jest.fn().mockRejectedValue(new Error('Critical failure')),
        interval: 5000,
        timeout: 1000,
        critical: true,
      };

      const healthyCheck: HealthCheck = {
        name: 'healthy-check',
        check: jest.fn().mockResolvedValue({
          status: 'healthy' as const,
          timestamp: new Date(),
          duration: 50,
        }),
        interval: 5000,
        timeout: 1000,
        critical: false,
      };

      healthMonitor.registerCheck(criticalCheck);
      healthMonitor.registerCheck(healthyCheck);

      const systemHealth = await healthMonitor.getSystemHealth();

      expect(systemHealth.overall).toBe('unhealthy');
    });

    it('should unregister health checks', () => {
      const mockCheck: HealthCheck = {
        name: 'test-check',
        check: jest.fn(),
        interval: 5000,
        timeout: 1000,
        critical: false,
      };

      healthMonitor.registerCheck(mockCheck);
      healthMonitor.unregisterCheck('test-check');

      expect(() => healthMonitor.runCheck('test-check')).rejects.toThrow('not found');
    });

    it('should get current health without running checks', async () => {
      const mockCheck: HealthCheck = {
        name: 'test-check',
        check: jest.fn().mockResolvedValue({
          status: 'healthy' as const,
          timestamp: new Date(),
          duration: 50,
        }),
        interval: 5000,
        timeout: 1000,
        critical: false,
      };

      healthMonitor.registerCheck(mockCheck);
      
      // Run check once to populate results
      await healthMonitor.runCheck('test-check');
      
      // Get current health without running checks again
      const currentHealth = healthMonitor.getCurrentHealth();
      
      expect(currentHealth.checks['test-check']?.status).toBe('healthy');
      expect(mockCheck.check).toHaveBeenCalledTimes(1); // Should not be called again
    });
  });

  describe('Default Health Checks', () => {
    it('should create memory health check', () => {
      const checks = createDefaultHealthChecks();
      const memoryCheck = checks.find(check => check.name === 'memory');

      expect(memoryCheck).toBeDefined();
      expect(memoryCheck?.critical).toBe(true);
      expect(memoryCheck?.interval).toBe(30000);
    });

    it('should create S3 connectivity check', () => {
      const mockS3Client = {
        testConnection: jest.fn().mockResolvedValue({ success: true }),
      };

      const checks = createDefaultHealthChecks(mockS3Client);
      const s3Check = checks.find(check => check.name === 's3_connectivity');

      expect(s3Check).toBeDefined();
      expect(s3Check?.critical).toBe(true);
      expect(s3Check?.interval).toBe(60000);
    });

    it('should run memory health check', async () => {
      const checks = createDefaultHealthChecks();
      const memoryCheck = checks.find(check => check.name === 'memory');

      if (memoryCheck) {
        const result = await memoryCheck.check();
        
        expect(result.status).toMatch(/^(healthy|degraded|unhealthy)$/);
        expect(result.details).toHaveProperty('usedMB');
        expect(result.details).toHaveProperty('totalMB');
        expect(result.details).toHaveProperty('usagePercent');
      }
    });

    it('should run S3 connectivity check with successful connection', async () => {
      const mockS3Client = {
        testConnection: jest.fn().mockResolvedValue({ success: true }),
      };

      const checks = createDefaultHealthChecks(mockS3Client);
      const s3Check = checks.find(check => check.name === 's3_connectivity');

      if (s3Check) {
        const result = await s3Check.check();
        
        expect(result.status).toBe('healthy');
        expect(result.message).toBe('S3 connection successful');
        expect(mockS3Client.testConnection).toHaveBeenCalledTimes(1);
      }
    });

    it('should run S3 connectivity check with failed connection', async () => {
      const mockS3Client = {
        testConnection: jest.fn().mockResolvedValue({ 
          success: false, 
          error: 'Connection failed' 
        }),
      };

      const checks = createDefaultHealthChecks(mockS3Client);
      const s3Check = checks.find(check => check.name === 's3_connectivity');

      if (s3Check) {
        const result = await s3Check.check();
        
        expect(result.status).toBe('unhealthy');
        expect(result.message).toBe('Connection failed');
      }
    });

    it('should handle missing S3 client gracefully', async () => {
      const checks = createDefaultHealthChecks();
      const s3Check = checks.find(check => check.name === 's3_connectivity');

      if (s3Check) {
        const result = await s3Check.check();
        
        expect(result.status).toBe('degraded');
        expect(result.message).toBe('S3 client not available');
      }
    });
  });

  describe('Health Check Integration', () => {
    it('should handle multiple concurrent health checks', async () => {
      const checks = Array.from({ length: 5 }, (_, i) => ({
        name: `check-${i}`,
        check: jest.fn().mockResolvedValue({
          status: 'healthy' as const,
          timestamp: new Date(),
          duration: Math.random() * 100,
        }),
        interval: 5000,
        timeout: 1000,
        critical: false,
      }));

      checks.forEach(check => healthMonitor.registerCheck(check));

      const results = await healthMonitor.runAllChecks();

      expect(Object.keys(results)).toHaveLength(5);
      checks.forEach((check, i) => {
        expect(results[`check-${i}`]?.status).toBe('healthy');
        expect(check.check).toHaveBeenCalledTimes(1);
      });
    });

    it('should continue running other checks if one fails', async () => {
      const failingCheck: HealthCheck = {
        name: 'failing-check',
        check: jest.fn().mockRejectedValue(new Error('Check failed')),
        interval: 5000,
        timeout: 1000,
        critical: false,
      };

      const successfulCheck: HealthCheck = {
        name: 'successful-check',
        check: jest.fn().mockResolvedValue({
          status: 'healthy' as const,
          timestamp: new Date(),
          duration: 50,
        }),
        interval: 5000,
        timeout: 1000,
        critical: false,
      };

      healthMonitor.registerCheck(failingCheck);
      healthMonitor.registerCheck(successfulCheck);

      const results = await healthMonitor.runAllChecks();

      expect(results['failing-check']?.status).toBe('unhealthy');
      expect(results['successful-check']?.status).toBe('healthy');
    });
  });
});
